[{"task_id": 80030101, "group_id": 301, "index": 1, "period_type": 1, "task_cond": [{"cond_type": 5, "params": [10010001]}], "task_goal": {"goal_type": 28, "params": [301, 200], "desc": ""}, "accumulate": false, "reward_id": 30100201}, {"task_id": 80030102, "group_id": 301, "index": 2, "period_type": 1, "task_cond": [{"cond_type": 5, "params": [10010001]}], "task_goal": {"goal_type": 28, "params": [301, 400], "desc": ""}, "accumulate": false, "reward_id": 30100201}, {"task_id": 80030103, "group_id": 301, "index": 3, "period_type": 1, "task_cond": [{"cond_type": 5, "params": [10010001]}], "task_goal": {"goal_type": 28, "params": [301, 600], "desc": ""}, "accumulate": false, "reward_id": 30100201}, {"task_id": 80030104, "group_id": 301, "index": 4, "period_type": 1, "task_cond": [{"cond_type": 5, "params": [10010001]}], "task_goal": {"goal_type": 28, "params": [301, 800], "desc": ""}, "accumulate": false, "reward_id": 30100201}, {"task_id": 80030105, "group_id": 301, "index": 5, "period_type": 1, "task_cond": [{"cond_type": 5, "params": [10010001]}], "task_goal": {"goal_type": 28, "params": [301, 1000], "desc": ""}, "accumulate": false, "reward_id": 30100201}, {"task_id": 80040101, "group_id": 401, "index": 1, "period_type": 1, "task_cond": [{"cond_type": 5, "params": [10010001]}, {"cond_type": 7, "params": []}], "task_goal": {"goal_type": 28, "params": [301, 200], "desc": ""}, "accumulate": false, "reward_id": 30100201}, {"task_id": 80040102, "group_id": 401, "index": 2, "period_type": 1, "task_cond": [{"cond_type": 5, "params": [10010001]}, {"cond_type": 7, "params": []}], "task_goal": {"goal_type": 28, "params": [301, 400], "desc": ""}, "accumulate": false, "reward_id": 30100201}, {"task_id": 80040103, "group_id": 401, "index": 3, "period_type": 1, "task_cond": [{"cond_type": 5, "params": [10010001]}, {"cond_type": 7, "params": []}], "task_goal": {"goal_type": 28, "params": [301, 600], "desc": ""}, "accumulate": false, "reward_id": 30100201}, {"task_id": 80040104, "group_id": 401, "index": 4, "period_type": 1, "task_cond": [{"cond_type": 5, "params": [10010001]}, {"cond_type": 7, "params": []}], "task_goal": {"goal_type": 28, "params": [301, 800], "desc": ""}, "accumulate": false, "reward_id": 30100201}, {"task_id": 80040105, "group_id": 401, "index": 5, "period_type": 1, "task_cond": [{"cond_type": 5, "params": [10010001]}, {"cond_type": 7, "params": []}], "task_goal": {"goal_type": 28, "params": [301, 1000], "desc": ""}, "accumulate": false, "reward_id": 30100201}]