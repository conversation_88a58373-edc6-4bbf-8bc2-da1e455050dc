import { _decorator, instantiate, Node, Prefab, size, UIOpacity, view } from "cc";
const { ccclass, property } = _decorator;

import { MyApp } from "db://assets/bundles/common/script/app/MyApp";
import { AttributeConst } from "db://assets/bundles/common/script/const/AttributeConst";
import { AttributeData } from "db://assets/bundles/common/script/data/base/AttributeData";
import { PlaneData } from "db://assets/bundles/common/script/data/plane/PlaneData";
import { Plane } from "db://assets/bundles/common/script/ui/Plane";
import { Bullet } from "../../../bullet/Bullet";
import { Emitter } from "../../../bullet/Emitter";
import FBoxCollider from "../../../collider-system/FBoxCollider";
import FCollider, { ColliderGroupType } from "../../../collider-system/FCollider";
import { GameConst } from "../../../const/GameConst";
import GameResourceList from "../../../const/GameResourceList";
import { eEntityTag } from "../../base/Entity";
import EffectLayer from "../../layer/EffectLayer";
import EnemyPlaneBase from "../enemy/EnemyPlaneBase";
import MainPlaneStat from "./MainPlaneStat";
import { GameIns } from "../../../GameIns";
import PlaneBase from "db://assets/bundles/common/script/game/ui/plane/PlaneBase";

@ccclass("MainPlane")
export class MainPlane extends PlaneBase {

    @property(Node)
    planeParent: Node | null = null;
    @property(Node)
    NodeEmitter: Node | null = null;
    @property(Node)
    InvincibleNode: Node | null = null;

    m_moveEnable = true; // 是否允许移动
    emitterComp: Emitter | null = null; // 发射器

    _hurtActTime = 0; // 受伤动画时间
    _hurtActDuration = 0.5; // 受伤动画持续时间

    _planeData: PlaneData | null = null;//飞机数据
    _plane: Plane | null = null;//飞机显示节点
    _fireEnable = true;//是否允许射击
    _unColliderTime: number = 0;//无敌时间
    statData: MainPlaneStat = new MainPlaneStat();

    private hpRecoveryTime = 0;
    private _nuclearNum = 0;
    get nuclearNum() {
        return this._nuclearNum;
    }
    addNuclear(num: number) {
        this._nuclearNum += num;
    }

    onLoad() {
        this.collideComp = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider);
        this.collideComp!.init(this, size(180, 150)); // 初始化碰撞组件
        this.collideComp!.groupType = ColliderGroupType.PLAYER;
    }

    // 纯表现层业务，请勿将逻辑代码写到这里
    update(dt: number) {
        dt = dt * GameIns.battleManager.animSpeed;
        this._hurtActTime += dt;

        if (this._unColliderTime > 0) {
            this._unColliderTime -= dt;
            if (this._unColliderTime <= 0) {
                this.cancelUncollide();
            }
        }
    }

    updateGameLogic(dt: number): void {
        super.updateGameLogic(dt)
        while (this.hpRecoveryTime <= GameIns.battleManager._gameTime) {
            this.hpRecoveryTime += 1;
            let hpRecovery = this.attribute.getHPRecovery()
            this.addHp(hpRecovery);
        }
    }

    initPlane(planeData: PlaneData) {
        this._planeData = planeData;

        this._nuclearNum = planeData.getFinalAttributeByKey(AttributeConst.NuclearMax);

        //加载飞机显示
        let plane = MyApp.planeMgr.getPlane(planeData);
        this._plane = plane.getComponent(Plane);
        this.planeParent?.addChild(plane);

        this.addTag(eEntityTag.Player);

        //设置飞机发射组件
        this.setEmitter();
        this.resetPlane();
    }

    resetPlane() {
        // 禁用射击
        this.setFireEnable(false);
        this.setMoveAble(false);
        this.colliderEnabled = false;
        this.isDead = false;
        this.curHp = this.maxHp;
        this.updateHpUI();

        this.hpRecoveryTime = 0;
        this._plane!.reset();

        const targetY = -view.getVisibleSize().height * 0.7;
        this.node.setPosition(0, targetY)
    }

    setEmitter() {
        //后期根据飞机的数据，加载不同的发送组件预制体
        let path = GameResourceList.EmitterPrefabPath + "Emitter_main_01";
        MyApp.resMgr.loadAsync(path, Prefab).then((prefab) => {
            let node = instantiate(prefab);
            this.NodeEmitter?.addChild(node);
            node.setPosition(0, 0);

            this.emitterComp = node.getComponent(Emitter);
            this.emitterComp!.setEntity(this);
            this.emitterComp!.setIsActive(this._fireEnable);
            this.emitterComp!.emitterId = 1000001;
        });
    }

    /**
     * 主飞机入场动画
     */
    planeIn(): void {
        this.hpNode!.getComponent(UIOpacity)!.opacity = 0;

        this.node.getComponent(UIOpacity)!.opacity = 0;
        this.scheduleOnce(() => {
            this.node.getComponent(UIOpacity)!.opacity = 255;
            this._plane?.onEnter(() => {
                this.hpNode!.getComponent(UIOpacity)!.opacity = 255;
                GameIns.battleManager.onPlaneIn();
            });
        }, 0.7);
    }


    /**
     * 碰撞处理
     * @param {Object} collision 碰撞对象
     */
    onCollide(collision: FCollider) {
        let damage = 0;
        if (collision.entity instanceof Bullet) {
            if (this.attribute.getFinalAttributeByKey(AttributeConst.StatusImmuneBulletHurt) == 0) {
                damage = collision.entity.calcDamage(this);
            }
            if (damage > 0) {
                this.hurt(damage)
            }
        } else if (collision.entity instanceof EnemyPlaneBase) {
            this.collisionPlane(collision.entity);
        }
    }

    /**
     * 控制飞机移动
     * @param {number} moveX 水平方向的移动量
     * @param {number} moveY 垂直方向的移动量
     */
    onControl(posX: number, posY: number) {
        if (!this.isDead && this.m_moveEnable) {
            let isLeft = posX < this.node.position.x;
            this._plane?.onMoveCommand(isLeft);

            // 限制飞机移动范围
            let width = GameConst.ViewBattleWidth / 2;
            posX = Math.min(width, posX);
            posX = Math.max(-width, posX);
            posY = Math.min(0, posY);
            posY = Math.max(-GameConst.ViewHeight, posY);
            this.node.setPosition(posX, posY);
        }
    }

    begine(isContinue = false) {
        this.setFireEnable(true);
        this.setMoveAble(true);
        if (isContinue) {
            this.setUncollideByTime(2);
        } else {
            this.cancelUncollide();
        }
    }

    revive() {
        this.node.active = true;
        this.begine(true);
    }

    //实现父类的方法
    playHurtAnim() {
        if (this._hurtActTime > this._hurtActDuration) {
            this._hurtActTime = 0;
            // 显示红屏效果
            EffectLayer.me.showRedScreen();
        }
    }

    toDie(): boolean {
        if (!super.toDie()) {
            return false;
        }
        this.node.active = false;
        this.resetPlane();
        GameIns.battleManager.battleDie();
        return true;
    }

    get collisionLevel() {
        return this._planeData!.config?.collideLevel || 0;
    }
    get collisionHurt() {
        return this._planeData!.config?.collideDamage || 0;
    }

    get attribute(): AttributeData {
        return this._planeData!;
    }

    getAttack(): number {
        return this._planeData!.getAttack();
    }

    setMoveAble(enable: boolean) {
        this.m_moveEnable = enable;
    }

    setFireEnable(enable: boolean) {
        this._fireEnable = enable;
        if (this.emitterComp) {
            this.emitterComp!.setIsActive(enable);
        }
    }
    //设置无敌状态
    setUncollideByTime(time: number) {
        this._unColliderTime = time;
        this.colliderEnabled = false;
        this.InvincibleNode!.active = true;
    }
    //取消无敌状态
    cancelUncollide() {
        this.colliderEnabled = true;
        this.InvincibleNode!.active = false;
    }

    setAnimSpeed(speed: number) {
        if (this._plane) {
            this._plane.setAnimSpeed(speed);
        }
    }
    get pickDiamondNum():number {
        return this.statData.pickDiamond;
    }
    get killEnemyNum():number {
        return this.statData.killEnemy;
    }
    get usedNuclearNum():number {
        return this.statData.usedNuclear;
    }
    get usedSuperNum():number {
        return this.statData.usedSuper;
    }
}