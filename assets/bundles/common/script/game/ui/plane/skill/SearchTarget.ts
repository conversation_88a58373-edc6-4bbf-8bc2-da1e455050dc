import { TargetType } from "../../../../autogen/luban/schema";
import { GameIns } from "../../../GameIns";
import type PlaneBase from "../PlaneBase";


export default function forEachEntityByTargetType(caster: PlaneBase, targetType: TargetType, callback: (entity: PlaneBase) => void) {
        switch (targetType) {
            case TargetType.Self:
                callback(caster);
                break;
            case TargetType.Main:
                callback(GameIns.mainPlaneManager.mainPlane!);
                break;
            case TargetType.MainFriendly:
                callback(GameIns.mainPlaneManager.mainPlane!);
                break;
            case TargetType.Enemy:
                GameIns.enemyManager.enemies.forEach((plane) => {
                    callback(plane);
                });
                GameIns.bossManager.bosses.forEach((boss) => {
                    // boss.getUnits().forEach((unit) => {
                    //     callback(unit);
                    // });
                    callback(boss)
                });
                break;
            case TargetType.BossEnemy:
                GameIns.bossManager.bosses.forEach((boss) => {
                    // boss.getUnits().forEach((unit) => {
                    //     callback(unit);
                    // });
                    callback(boss)
                });
                break;
            case TargetType.NormalEnemy:
                GameIns.enemyManager.enemies.forEach((plane) => {
                    callback(plane);
                });
                break;
            default:
                break;
        }
    }