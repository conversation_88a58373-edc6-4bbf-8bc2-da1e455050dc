import { _decorator, Component, Enum, misc, Node, UITransform, Vec2, Vec3 } from 'cc';
import { BulletSystem } from '../bullet/BulletSystem';
import { IMovable } from './IMovable';
import Entity from '../ui/base/Entity';
const { degreesToRadians, radiansToDegrees } = misc;
const { ccclass, property, executeInEditMode } = _decorator;

export enum eSpriteDefaultFacing {
    Right = 0,    // →
    Up = -90,     // ↑
    Down = 90,    // ↓
    Left = 180    // ←
}

export enum eMoveEvent {
    onBecomeVisible,
    onBecomeInvisible,
}

@ccclass('Movable')
@executeInEditMode
export class Movable extends Component implements IMovable {

    @property({ type: Enum(eSpriteDefaultFacing), displayName: '图片默认朝向' })
    public defaultFacing: eSpriteDefaultFacing = eSpriteDefaultFacing.Up;

    public isFacingMoveDir: boolean = false;      // 是否朝向行进方向
    public isTrackingTarget: boolean = false;     // 是否正在追踪目标
    public speed: number = 1;                     // 速度
    public speedAngle: number = 0;                // 速度方向 (用角度表示)
    public turnSpeed: number = 60;                // 转向速度（仅用在追踪目标时）
    public acceleration: number = 0;              // 加速度
    public accelerationAngle: number = 0;         // 加速度方向 (用角度表示)

    // 追踪相关的新属性
    public trackingDuration: number = 0;          // 追踪持续时间（毫秒，0表示无限制）
    public trackingMaxDistance: number = 0;       // 最大追踪距离（0表示无限制）
    public trackingMaxAngle: number = 180;        // 最大转向角度（度，180表示可以完全掉头）
    public trackingMinEfficiency: number = 0.1;   // 最小追踪效率（当效率低于此值时停止追踪）

    private _trackingElapsedTime: number = 0;     // 已追踪时间
    private _lastDistanceToTarget: number = 0;    // 上一帧到目标的距离
    private _trackingEfficiencyCheckInterval: number = 500; // 效率检查间隔（毫秒）
    private _lastEfficiencyCheckTime: number = 0; // 上次效率检查时间

    // @property({displayName: '振荡偏移速度', tooltip: '控制倾斜振荡的频率'})
    public tiltSpeed: number = 0;                 // 偏移速度
    // @property({displayName: '振荡偏移幅度', tooltip: '控制倾斜振荡的幅度'})
    public tiltOffset: number = 100;               // 偏移距离

    public target: Node | null = null;            // 追踪的目标节点
    public arrivalDistance: number = 10;          // 到达目标的距离

    private _selfSize: Vec2 = new Vec2();
    private _position: Vec3 = new Vec3();
    private _tiltTime: number = 0;                // 用于计算倾斜偏移的累积时间
    private _basePosition: Vec3 = new Vec3();     // 基础位置（不包含倾斜偏移）

    private _wasVisible: boolean = false;
    private _isVisible: boolean = false;           // 是否可见
    public get isVisible() { return this._isVisible; }
    private _isMovable: boolean = true;           // 是否可移动
    public get isMovable() { return this._isMovable; }

    private _visibilityCheckCounter: number = 0;  // 可见性检查计数器
    private static readonly VISIBILITY_CHECK_INTERVAL = 5; // 每x帧检查一次可见性

    // Event system:
    private _eventListeners: Map<eMoveEvent, Array<() => void>> = new Map();

    onLoad() {
        const uiTransform = this.node.getComponent(UITransform);
        const self_size = uiTransform ? uiTransform.contentSize : { width: 0, height: 0 };
        this._selfSize.set(self_size.width / 2, self_size.height / 2);
    }

    onDestroy() {
        // clear all event listeners
        this._eventListeners.clear();
    }

    /**
     * 添加事件监听器
     * @param event 事件类型
     * @param listener 监听器函数
     */
    public on(event: eMoveEvent, listener: () => void): void {
        if (!this._eventListeners.has(event)) {
            this._eventListeners.set(event, []);
        }
        const listeners = this._eventListeners.get(event)!;
        if (!listeners.includes(listener)) {
            listeners.push(listener);
        }
    }

    /**
     * 移除事件监听器
     * @param event 事件类型
     * @param listener 监听器函数
     */
    public off(event: eMoveEvent, listener: () => void): void {
        const listeners = this._eventListeners.get(event);
        if (listeners) {
            const index = listeners.indexOf(listener);
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        }
    }

    public removeAllListeners(): void {
        this._eventListeners.clear();
    }

    /**
     * 触发事件
     * @param event 事件类型
     */
    private emit(event: eMoveEvent): void {
        const listeners = this._eventListeners.get(event);
        if (listeners && listeners.length > 0) {
            listeners.forEach(listener => listener());
        }
    }

    public tick(dt: number): void {
        if (!this._isMovable) return;

        const angleRadians = degreesToRadians(this.speedAngle);
        // Convert speed and angle to velocity vector
        let velocityX = this.speed * Math.cos(angleRadians);
        let velocityY = this.speed * Math.sin(angleRadians);

        if (this.isTrackingTarget && this.target) {
            const targetPos = this.target.getPosition();
            const currentPos = this._basePosition;

            // Calculate direction to target
            const directionX = targetPos.x - currentPos.x;
            const directionY = targetPos.y - currentPos.y;
            const distance = Math.sqrt(directionX * directionX + directionY * directionY);

            if (distance > 0) {
                // Calculate desired angle to target
                const desiredAngle = radiansToDegrees(Math.atan2(directionY, directionX));

                // Smoothly adjust speedAngle toward target
                const angleDiff = desiredAngle - this.speedAngle;
                // Normalize angle difference to [-180, 180] range
                const normalizedAngleDiff = ((angleDiff + 180) % 360) - 180;

                // Apply tracking adjustment (you can add a trackingStrength property to control this)
                const trackingStrength = 1.0; // Can be made configurable
                const maxTurnRate = this.turnSpeed; // degrees per second - can be made configurable
                const turnAmount = Math.min(Math.abs(normalizedAngleDiff), maxTurnRate * dt) * Math.sign(normalizedAngleDiff);

                this.speedAngle += turnAmount * trackingStrength;
                const angleRadiansAfterTracking = degreesToRadians(this.speedAngle);

                // Recalculate velocity with new angle
                velocityX = this.speed * Math.cos(angleRadiansAfterTracking);
                velocityY = this.speed * Math.sin(angleRadiansAfterTracking);
            }
        }

        // Convert acceleration and angle to acceleration vector
        if (this.acceleration !== 0) {
            const accelerationRadians = degreesToRadians(this.accelerationAngle);
            const accelerationX = this.acceleration * Math.cos(accelerationRadians);
            const accelerationY = this.acceleration * Math.sin(accelerationRadians);
            // Update velocity vector: v = v + a * dt
            velocityX += accelerationX * dt;
            velocityY += accelerationY * dt;
        }

        // Convert back to speed and angle
        this.speed = Math.sqrt(velocityX * velocityX + velocityY * velocityY);
        this.speedAngle = radiansToDegrees(Math.atan2(velocityY, velocityX));

        // Update position: p = p + v * dt
        if (velocityX !== 0 || velocityY !== 0) {
            // Update base position (main movement path)
            this._basePosition.x += velocityX * dt;
            this._basePosition.y += velocityY * dt;

            // Start with base position
            this._position.set(this._basePosition);

            // Apply tilting behavior if enabled
            if (this.tiltSpeed > 0 && this.tiltOffset > 0) {
                // Update tilt time
                this._tiltTime += dt;

                // Calculate perpendicular direction to movement
                // If moving in direction (cos(angle), sin(angle)), perpendicular is (-sin(angle), cos(angle))
                const moveAngleRad = degreesToRadians(this.speedAngle);
                const perpX = -Math.sin(moveAngleRad);
                const perpY = Math.cos(moveAngleRad);

                // Calculate tilt offset using sine wave
                const tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset;

                // Apply tilt offset in perpendicular direction (as position offset, not velocity)
                this._position.x += perpX * tiltAmount;
                this._position.y += perpY * tiltAmount;
            }

            this.node.setPosition(this._position);

            // 降低可见性检查频率
            if (++this._visibilityCheckCounter >= Movable.VISIBILITY_CHECK_INTERVAL) {
                this._visibilityCheckCounter = 0;
                this.checkVisibility();
            }
        }

        if (this.isFacingMoveDir && this.speed > 0) {
            const finalAngle = this.speedAngle + this.defaultFacing;
            this.node.setRotationFromEuler(0, 0, finalAngle);
        }
    }

    public checkVisibility(): void {
        // 这里目前的检查逻辑没有考虑旋转和缩放
        // 正常来说需要判定world corners，如果四个角有一个在屏幕内，就认为是可见的
        const visibleSize = BulletSystem.worldBounds;
        const position = this.node.worldPosition;
        const isVisible = (position.x + this._selfSize.x) >= visibleSize.xMin &&
            (position.x - this._selfSize.x) <= visibleSize.xMax &&
            (position.y - this._selfSize.y) <= visibleSize.yMax &&
            (position.y + this._selfSize.y) >= visibleSize.yMin;

        // debug visibility
        // if (!isVisible) {
        //     console.log("Movable", "checkVisibility", this.node.name + " is not visible");
        //     console.log("Movable", "checkLeftBound  :", (this._position.x - this._selfSize.x) <= visibleSize.xMax, (this._position.x - this._selfSize.x), "<=", visibleSize.xMax);
        //     console.log("Movable", "checkRightBound :", (this._position.x + this._selfSize.x) >= visibleSize.xMin, (this._position.x + this._selfSize.x), ">=", visibleSize.xMin);
        //     console.log("Movable", "checkTopBound   :", (this._position.y + this._selfSize.y) <= visibleSize.yMax, (this._position.y + this._selfSize.y), "<=", visibleSize.yMax);
        //     console.log("Movable", "checkBottomBound:", (this._position.y - this._selfSize.y) >= visibleSize.yMin, (this._position.y - this._selfSize.y), ">=", visibleSize.yMin);
        // }

        this.setVisible(isVisible);
    }

    private setVisible(visible: boolean) {
        // console.log('setVisible: ', this._wasVisible, ', ', visible);
        if (visible) {
            if (!this._wasVisible)
                this.emit(eMoveEvent.onBecomeVisible);
        } else {
            if (this._wasVisible)
                this.emit(eMoveEvent.onBecomeInvisible);
        }
        this._wasVisible = this._isVisible;
        this._isVisible = visible;
    }

    /**
     * Set the target
     */
    public setTarget(target: Entity | null): Movable {
        this.target = target ? target.node : null;
        return this;
    }

    /**
     * Set whether to track the target
     */
    public setTracking(tracking: boolean): Movable {
        this.isTrackingTarget = tracking;
        return this;
    }

    public setMovable(movable: boolean): Movable {
        this._isMovable = movable;

        if (this._isMovable) {
            // Initialize base position to current node position
            this.node.getPosition(this._basePosition);
        }

        return this;
    }
}