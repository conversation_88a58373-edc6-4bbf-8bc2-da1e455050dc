import { _decorator, Vec3 } from "cc";
import { EventConditionBase, Comparer } from "db://assets/bundles/common/script/game/eventgroup/IEventCondition";
import { IEventGroupContext } from "db://assets/bundles/common/script/game/eventgroup/IEventGroupContext";
import { EventConditionData, eCompareOp, eConditionOp } from "../../data/bullet/EventGroupData";

export class EmitterConditionBase extends EventConditionBase {
}

/////////////////////////////////////////////////////////////////////////////////
// 以下是发射器相关参数
/////////////////////////////////////////////////////////////////////////////////
// 发射器是否启用
export class EmitterCondition_Active extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        switch (this.data.compareOp) {
            case eCompareOp.Equal:
                return context.emitter!.isActive.value === (this._targetValue === 1) ? true : false;
            case eCompareOp.NotEqual:
                return context.emitter!.isActive.value !== (this._targetValue === 1) ? true : false;
            default:
                return false;
        }
    }
}

// 发射器初始延迟时间
export class EmitterCondition_InitialDelay extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        return Comparer.compare(context.emitter!.initialDelay.value, this._targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_Prewarm extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        switch (this.data.compareOp) {
            case eCompareOp.Equal:
                return context.emitter!.isPreWarm.value === (this._targetValue === 1) ? true : false;
            case eCompareOp.NotEqual:
                return context.emitter!.isPreWarm.value !== (this._targetValue === 1) ? true : false;
            default:
                return false;
        }
    }
}

export class EmitterCondition_PrewarmDuration extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        return Comparer.compare(context.emitter!.preWarmDuration.value, this._targetValue, this.data.compareOp);
    }
}

// 发射器持续时间
export class EmitterCondition_Duration extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        return Comparer.compare(context.emitter!.emitDuration.value, this._targetValue, this.data.compareOp);
    }
}

// 发射器已运行时间
export class EmitterCondition_ElapsedTime extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        return Comparer.compare(context.emitter!.elapsedTime.value, this._targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_Loop extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        switch (this.data.compareOp) {
            case eCompareOp.Equal:
                return context.emitter!.isLoop.value === (this._targetValue === 1) ? true : false;
            case eCompareOp.NotEqual:
                return context.emitter!.isLoop.value !== (this._targetValue === 1) ? true : false;
            default:
                return false;
        }
    }
}

export class EmitterCondition_LoopInterval extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        return Comparer.compare(context.emitter!.loopInterval.value, this._targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_EmitInterval extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        return Comparer.compare(context.emitter!.emitInterval.value, this._targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_PerEmitCount extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        return Comparer.compare(context.emitter!.perEmitCount.value, this._targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_PerEmitInterval extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        return Comparer.compare(context.emitter!.perEmitInterval.value, this._targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_PerEmitOffsetX extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        return Comparer.compare(context.emitter!.perEmitOffsetX.value, this._targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_Angle extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        return Comparer.compare(context.emitter!.angle.value, this._targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_Count extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        return Comparer.compare(context.emitter!.count.value, this._targetValue, this.data.compareOp);
    }
}

/////////////////////////////////////////////////////////////////////////////////
// 以下是发射器配置的子弹相关参数
/////////////////////////////////////////////////////////////////////////////////
export class EmitterCondition_BulletDuration extends EmitterConditionBase {
    private _evalValue: number = 0;
    
    public onLoad(context: IEventGroupContext): void {
        super.onLoad(context);
        this._evalValue = context.emitter!.bulletProp.duration.value;
    }

    public evaluate(context: IEventGroupContext): boolean {
        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_BulletSpeed extends EmitterConditionBase {
    private _evalValue: number = 0;

    public onLoad(context: IEventGroupContext): void {
        super.onLoad(context);
        this._evalValue = context.emitter!.bulletProp.speed.value;
    }

    public evaluate(context: IEventGroupContext): boolean {
        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_BulletAcceleration extends EmitterConditionBase {
    private _evalValue: number = 0;

    public onLoad(context: IEventGroupContext): void {
        super.onLoad(context);
        this._evalValue = context.emitter!.bulletProp.acceleration.value;
    }

    public evaluate(context: IEventGroupContext): boolean {
        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_BulletAccelerationAngle extends EmitterConditionBase {
    private _evalValue: number = 0;

    public onLoad(context: IEventGroupContext): void {
        super.onLoad(context);
        this._evalValue = context.emitter!.bulletProp.accelerationAngle.value;
    }

    public evaluate(context: IEventGroupContext): boolean {
        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_BulletFacingMoveDir extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        switch (this.data.compareOp) {
            case eCompareOp.Equal:
                return context.emitter!.bulletProp.isFacingMoveDir.value === (this._targetValue === 1) ? true : false;
            case eCompareOp.NotEqual:
                return context.emitter!.bulletProp.isFacingMoveDir.value !== (this._targetValue === 1) ? true : false;
            default:
                return false;
        }
    }
}

export class EmitterCondition_BulletTrackingTarget extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        switch (this.data.compareOp) {
            case eCompareOp.Equal:
                return context.emitter!.bulletProp.isTrackingTarget.value === (this._targetValue === 1) ? true : false;
                case eCompareOp.NotEqual:
                return context.emitter!.bulletProp.isTrackingTarget.value !== (this._targetValue === 1) ? true : false;
            default:
                return false;
        }
    }
}

export class EmitterCondition_BulletDestructive extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        switch (this.data.compareOp) {
            case eCompareOp.Equal:
                return context.emitter!.bulletProp.isDestructive.value === (this._targetValue === 1) ? true : false;
            case eCompareOp.NotEqual:
                return context.emitter!.bulletProp.isDestructive.value !== (this._targetValue === 1) ? true : false;
            default:
                return false;
        }
    }
}

export class EmitterCondition_BulletDestructiveOnHit extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        switch (this.data.compareOp) {
            case eCompareOp.Equal:
                return context.emitter!.bulletProp.isDestructiveOnHit.value === (this._targetValue === 1) ? true : false;
            case eCompareOp.NotEqual:
                return context.emitter!.bulletProp.isDestructiveOnHit.value !== (this._targetValue === 1) ? true : false;
            default:
                return false;
        }
    }
}

export class EmitterCondition_BulletHitEffect extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        switch (this.data.compareOp) {
            case eCompareOp.Equal:
                // return context.emitter!.bulletData.hitEffect === (this._targetValue === 1) ? true : false;
            case eCompareOp.NotEqual:
                // return context.emitter!.bulletData.hitEffect !== (this._targetValue === 1) ? true : false;
            default:
                return false;
        }
    }
}

export class EmitterCondition_BulletScale extends EmitterConditionBase {
    private _evalValue: number = 1;

    public onLoad(context: IEventGroupContext): void {
        super.onLoad(context);
        this._evalValue = context.emitter!.bulletProp.scale.value;
    }

    public evaluate(context: IEventGroupContext): boolean {
        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_BulletColorR extends EmitterConditionBase {
    private _evalValue: number = 0;
    
    public onLoad(context: IEventGroupContext): void {
        super.onLoad(context);
        this._evalValue = context.emitter!.bulletProp.color.value.r;
    }
    
    public evaluate(context: IEventGroupContext): boolean {
        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_BulletColorG extends EmitterConditionBase {
    private _evalValue: number = 0;
    
    public onLoad(context: IEventGroupContext): void {
        super.onLoad(context);
        this._evalValue = context.emitter!.bulletProp.color.value.g;
    }
    
    public evaluate(context: IEventGroupContext): boolean {
        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_BulletColorB extends EmitterConditionBase {
    private _evalValue: number = 0;
    
    public onLoad(context: IEventGroupContext): void {
        super.onLoad(context);
        this._evalValue = context.emitter!.bulletProp.color.value.b;
    }
    
    public evaluate(context: IEventGroupContext): boolean {
        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_BulletDefaultFacing extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        switch (this.data.compareOp) {
            case eCompareOp.Equal:
                return context.emitter!.bulletProp.defaultFacing.value === this._targetValue;
            case eCompareOp.NotEqual:
                return context.emitter!.bulletProp.defaultFacing.value !== this._targetValue;
            default:
                return false;
        }
    }
}

/////////////////////////////////////////////////////////////////////////////////
// Player
/////////////////////////////////////////////////////////////////////////////////
// 玩家account等级
export class EmitterCondition_PlayerActLevel extends EmitterConditionBase {
    // TODO:
}

export class EmitterCondition_PlayerPosX extends EmitterConditionBase {
    _playerPos: Vec3 = Vec3.ZERO;

    public evaluate(context: IEventGroupContext): boolean {
        if (context.playerPlane === null) {
            return false;
        }

        context.playerPlane.node.getPosition(this._playerPos);
        return Comparer.compare(this._playerPos.x, this._targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_PlayerPosY extends EmitterCondition_PlayerPosX {
    public evaluate(context: IEventGroupContext): boolean {
        if (context.playerPlane === null) {
            return false;
        }

        context.playerPlane.node.getPosition(this._playerPos);
        return Comparer.compare(this._playerPos.y, this._targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_PlayerLifePercent extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        if (context.playerPlane === null) {
            return false;
        }

        const hp_ratio = context.playerPlane.curHp / context.playerPlane.maxHp * 100;
        return Comparer.compare(hp_ratio, this._targetValue, this.data.compareOp);
    }
}

export class EmitterCondition_PlayerGainBuff extends EmitterConditionBase {
    public evaluate(context: IEventGroupContext): boolean {
        if (context.playerPlane === null) {
            return false;
        }

        return context.playerPlane.buffComp?.HasBuff(this._targetValue);
    }
}