import { WaveConditionData, WaveEventGroupData, WaveActionData, eWaveConditionType, eWaveActionType } from "../data/WaveData";
import { IEventGroupContext } from "db://assets/bundles/common/script/game/eventgroup/IEventGroupContext";
import { EventGroupBase } from "db://assets/bundles/common/script/game/eventgroup/IEventGroup";
import { IEventCondition, ConditionChain } from "db://assets/bundles/common/script/game/eventgroup/IEventCondition";
import { IEventAction } from "db://assets/bundles/common/script/game/eventgroup/IEventAction";
import { _decorator, CCInteger } from "cc";
import { Wave } from "./Wave";
import * as wave_cond from "./WaveEventConditions";
import * as wave_act from "./WaveEventActions";
const { ccclass, property, type } = _decorator;

export class WaveEventGroupContext implements IEventGroupContext {
    // 继承来的，在波次这里不使用
    emitter: null = null;
    // 继承来的，在波次这里不使用
    bullet: null = null;
    playerPlane: null = null;

    wave: Wave|null = null;

    reset() {
        this.emitter = null;
        this.bullet = null;
        this.playerPlane = null;
        this.wave = null;
    }
}

/// Wave事件组
/// 和子弹&发射器事件组主要差异在于数据源不同: WaveEventGroupData vs EventGroupData
export class WaveEventGroup extends EventGroupBase<WaveEventGroupContext, WaveEventGroupData> {
    protected buildConditions(): ConditionChain {
        const chain = new ConditionChain();
        this.data.conditions.forEach((condData, index) => {
            const condition = WaveConditionFactory.create(condData);
            if (condition) {
                condition.onLoad(this.context);
                chain.conditions.push(condition);
            }
        });
        return chain;
    }

    protected buildActions(): IEventAction[] {
        return this.data.actions.map(actionData => {
            let action = WaveActionFactory.create(actionData);
            return action;
        });
    }
}

class WaveConditionFactory {
    static create(data: WaveConditionData): IEventCondition {
        switch (data.type) {
            case eWaveConditionType.Spawn_Count:
                return new wave_cond.WaveCondition_SpawnCount(data);
            case eWaveConditionType.Player_Level:
                return new wave_cond.WaveCondition_PlayerLevel(data);
            default: 
                throw new Error(`Unknown condition type: ${data.type}`);
        }
    }
}

class WaveActionFactory {
    static create(data: WaveActionData): IEventAction {
        switch (data.type) {
            case eWaveActionType.Spawn_Interval:
                return new wave_act.WaveAction_SpawnInterval(data);
            case eWaveActionType.Spawn_Angle:
                return new wave_act.WaveAction_SpawnAngle(data);
            default:
                throw new Error(`Unknown action type: ${data.type}`);
        }
    }
}