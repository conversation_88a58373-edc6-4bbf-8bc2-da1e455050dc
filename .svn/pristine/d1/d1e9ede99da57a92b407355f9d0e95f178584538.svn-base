import { ResCondition, ResSkillConditionElem } from "../../../../autogen/luban/schema";

export class ExCondition extends ResSkillConditionElem {
    constructor(res:ResSkillConditionElem) {
        super(res)
    }
    reset() {}
}
export class ExConditionNum extends ExCondition{
    num:number = 0;
    constructor(res:ResSkillConditionElem, num:number) {
        super(res)
        this.num = num
    }
    reset() {
        this.num -= this.value
    }
}
