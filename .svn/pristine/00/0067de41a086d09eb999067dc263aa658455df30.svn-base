import { _decorator, Graphics, Color, Node, Vec3, Camera } from 'cc';
import { GizmoDrawer, RegisterGizmoDrawer } from './GizmoDrawer';
import { GizmoUtils } from './GizmoUtils';
import { Emitter } from 'db://assets/bundles/common/script/game/bullet/Emitter';

/**
 * Gizmo drawer for EmitterArc components
 * Draws visual debugging information for arc-based bullet emitters
 */
@RegisterGizmoDrawer
export class EmitterGizmo extends GizmoDrawer<Emitter> {
    
    public readonly componentType = Emitter;
    public readonly drawerName = "EmitterGizmo";
    
    // Gizmo display options
    public showRadius: boolean = true;
    public showDirections: boolean = true;
    public showCenter: boolean = true;
    public showArc: boolean = true;
    
    // Colors
    public radiusColor: Color = Color.GRAY;
    public directionColor: Color = Color.RED;
    public centerColor: Color = Color.WHITE;
    public arcColor: Color = Color.YELLOW;
    
    // Display settings
    public speedScale: number = 1.0;
    public arrowSize: number = 8;
    public centerSize: number = 8;

    // Debug option to disable camera scaling
    public disableCameraScaling: boolean = false;
    
    public drawGizmos(emitter: Emitter, graphics: Graphics, node: Node): void {
        // For 2D projects, convert world position to graphics local space
        const gizmoPos = this.worldToGizmoSpace(node.worldPosition, graphics.node);
        const gizmoX = gizmoPos.x;
        const gizmoY = gizmoPos.y;

        // Calculate camera zoom scale to keep gizmo size consistent
        const cameraScale = GizmoUtils.getCameraZoomScale();

        // Draw center point
        if (this.showCenter) {
            this.drawCenter(graphics, gizmoX, gizmoY);
        }

        // Draw radius circle
        if (this.showRadius && emitter.radius.value > 0) {
            this.drawRadius(graphics, gizmoX, gizmoY, emitter.radius.value);
        }

        // Draw arc indicator
        if (this.showArc && emitter.arc.value > 0) {
            this.drawArcIndicator(graphics, emitter, gizmoX, gizmoY, cameraScale);
        }

        // Draw direction arrows
        if (this.showDirections && emitter.count.value > 0) {
            this.drawDirections(graphics, emitter, gizmoX, gizmoY, cameraScale);
        }
    }

    /**
     * Convert world position to gizmo graphics coordinate space
     * For 2D projects, this converts world coordinates to the local space of the graphics node
     */
    private worldToGizmoSpace(worldPos: Readonly<Vec3>, gizmoNode: Node): { x: number, y: number } {
        // Convert world position to local position of the gizmo graphics node
        const localPos = new Vec3();
        gizmoNode.inverseTransformPoint(localPos, worldPos);
        return { x: localPos.x, y: localPos.y };
    }

    private drawCenter(graphics: Graphics, worldX: number, worldY: number): void {
        if (this.disableCameraScaling) {
            GizmoUtils.drawCross(graphics, worldX, worldY, this.centerSize, this.centerColor);
            return;
        }

        try {
            GizmoUtils.drawCrossScaled(graphics, worldX, worldY, this.centerSize, this.centerColor);
        } catch (error) {
            GizmoUtils.drawCross(graphics, worldX, worldY, this.centerSize, this.centerColor);
        }
    }

    private drawRadius(graphics: Graphics, worldX: number, worldY: number, radius: number): void {
        if (this.disableCameraScaling) {
            GizmoUtils.drawCircle(graphics, worldX, worldY, radius, this.radiusColor, false);
            return;
        }

        try {
            GizmoUtils.drawCircleScaled(graphics, worldX, worldY, radius, this.radiusColor, false);
        } catch (error) {
            GizmoUtils.drawCircle(graphics, worldX, worldY, radius, this.radiusColor, false);
        }
    }
    
    private drawArcIndicator(graphics: Graphics, emitter: Emitter, worldX: number, worldY: number, cameraScale: number): void {
        if (emitter.arc.value <= 0) return;

        graphics.strokeColor = this.arcColor;
        graphics.lineWidth = 2 * cameraScale;

        // Convert angle and arc to radians
        // Use the same coordinate system as EmitterArc.getDirection() - no +90 offset
        const baseAngleRad = emitter.angle.value * Math.PI / 180;
        const arcRad = emitter.arc.value * Math.PI / 180;

        const startAngle = baseAngleRad - arcRad / 2;
        const endAngle = baseAngleRad + arcRad / 2;

        // Draw arc starting from the emitter radius (spawn position) extending outward
        const arcStartRadius = emitter.radius.value; // Start from spawn radius
        // Use same length calculation as direction arrows for consistency
        const baseLength = 30 * cameraScale; // Scale the base length
        const speedFactor = emitter.emitPower.value || 1;
        const arcLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);
        const arcEndRadius = arcStartRadius + arcLength;

        // Draw arc lines from spawn radius extending outward to show direction range
        const segments = Math.max(8, Math.floor(emitter.arc.value / 5)); // More segments for larger arcs

        // Draw the arc at the end radius to show the direction spread
        for (let i = 0; i <= segments; i++) {
            const angle = startAngle + (endAngle - startAngle) * (i / segments);
            const x = worldX + Math.cos(angle) * arcEndRadius;
            const y = worldY + Math.sin(angle) * arcEndRadius;

            if (i === 0) {
                graphics.moveTo(x, y);
            } else {
                graphics.lineTo(x, y);
            }
        }

        // Draw lines from spawn position to end of arc to show the direction range
        const startSpawnX = worldX + Math.cos(startAngle) * arcStartRadius;
        const startSpawnY = worldY + Math.sin(startAngle) * arcStartRadius;
        const endSpawnX = worldX + Math.cos(endAngle) * arcStartRadius;
        const endSpawnY = worldY + Math.sin(endAngle) * arcStartRadius;

        const startEndX = worldX + Math.cos(startAngle) * arcEndRadius;
        const startEndY = worldY + Math.sin(startAngle) * arcEndRadius;
        const endEndX = worldX + Math.cos(endAngle) * arcEndRadius;
        const endEndY = worldY + Math.sin(endAngle) * arcEndRadius;

        // Draw lines from spawn radius to end radius for arc boundaries
        graphics.moveTo(startSpawnX, startSpawnY);
        graphics.lineTo(startEndX, startEndY);
        graphics.moveTo(endSpawnX, endSpawnY);
        graphics.lineTo(endEndX, endEndY);

        graphics.stroke();
    }
    
    private drawDirections(graphics: Graphics, emitter: Emitter, worldX: number, worldY: number, cameraScale: number): void {
        const baseLength = 30 * cameraScale; // Scale the base length
        const speedFactor = emitter.emitPower.value || 1;
        const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);

        for (let i = 0; i < emitter.count.value; i++) {
            const direction = emitter.getSpawnDirection(i);
            const spawnPos = emitter.getSpawnPosition(i, 0);

            // Start position (at spawn position relative to world position)
            const startX = worldX + spawnPos.x;
            const startY = worldY + spawnPos.y;

            // End position (direction from spawn position)
            const endX = startX + direction.x * arrowLength;
            const endY = startY + direction.y * arrowLength;

            // Draw arrow with scaled size
            GizmoUtils.drawArrowScaled(graphics, startX, startY, endX, endY, this.directionColor, this.arrowSize);
        }
    }
    
    public getPriority(): number {
        return 10; // Draw emitter gizmos with medium priority
    }
    
    /**
     * Configure display options
     */
    public configure(options: {
        showRadius?: boolean;
        showDirections?: boolean;
        showCenter?: boolean;
        showArc?: boolean;
        radiusColor?: Color;
        directionColor?: Color;
        centerColor?: Color;
        arcColor?: Color;
        speedScale?: number;
        arrowSize?: number;
        centerSize?: number;
        disableCameraScaling?: boolean;
    }): void {
        if (options.showRadius !== undefined) this.showRadius = options.showRadius;
        if (options.showDirections !== undefined) this.showDirections = options.showDirections;
        if (options.showCenter !== undefined) this.showCenter = options.showCenter;
        if (options.showArc !== undefined) this.showArc = options.showArc;
        if (options.radiusColor !== undefined) this.radiusColor = options.radiusColor;
        if (options.directionColor !== undefined) this.directionColor = options.directionColor;
        if (options.centerColor !== undefined) this.centerColor = options.centerColor;
        if (options.arcColor !== undefined) this.arcColor = options.arcColor;
        if (options.speedScale !== undefined) this.speedScale = options.speedScale;
        if (options.arrowSize !== undefined) this.arrowSize = options.arrowSize;
        if (options.centerSize !== undefined) this.centerSize = options.centerSize;
        if (options.disableCameraScaling !== undefined) this.disableCameraScaling = options.disableCameraScaling;
    }

    /**
     * Quick method to disable camera scaling for troubleshooting
     * Call this from the console: EmitterGizmo.disableAllCameraScaling()
     */
    public static disableAllCameraScaling(): void {
        try {
            const { GizmoManager } = require('./GizmoManager');
            const drawer = GizmoManager.getDrawer("EmitterGizmo") as EmitterGizmo;
            if (drawer) {
                drawer.disableCameraScaling = true;
            }
        } catch (error) {
            // Silent fallback
        }
    }

    /**
     * Quick method to re-enable camera scaling
     * Call this from the console: EmitterGizmo.enableAllCameraScaling()
     */
    public static enableAllCameraScaling(): void {
        try {
            const { GizmoManager } = require('./GizmoManager');
            const drawer = GizmoManager.getDrawer("EmitterGizmo") as EmitterGizmo;
            if (drawer) {
                drawer.disableCameraScaling = false;
            }
        } catch (error) {
            // Silent fallback
        }
    }
}
