import csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';
import { _decorator, Button, Component, Label, Sprite } from 'cc';
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { ResItem } from 'db://assets/bundles/common/script/autogen/luban/schema';
import { MessageBox } from 'db://assets/scripts/core/base/MessageBox';
import { DataMgr } from '../../data/DataManager';

const { ccclass, property } = _decorator;

@ccclass('MailCellUI')
export class MailCellUI extends Component {

    @property(Sprite)
    mailIcon: Sprite | null = null;

    @property(Label)
    mailTitle: Label | null = null;

    @property(Label)
    mailContent: Label | null = null;

    @property(Button)
    btnClick: Button | null = null;

    itemID: number = 0;

    mailInfo: csproto.comm.IMail | null = null;

    start() {

    }

    update(deltaTime: number) {

    }
    onButtonClick() {
        MessageBox.show('物品ID：' + this.itemID);
        if (this.itemID > 0) {
            DataMgr.mail.cmdMailGetAttachment([this.mailInfo!.guid!]);
        } else {
            DataMgr.mail.cmdMailDelete([this.mailInfo!.guid!]);
        }
    }
    public setData(mailInfo: csproto.comm.IMail): void {
        this.mailInfo = mailInfo;
        if (!mailInfo.attachments || mailInfo.attachments.length === 0) {
            this.itemID = 0;
        } else {
            this.itemID = mailInfo.attachments![0].item_id!;
        }
        let item: ResItem | undefined = MyApp.lubanTables.TbResItem.get(this.itemID);
        this.mailTitle!.string = mailInfo?.title || "";
        //this.mailContent!.string = mailInfo?.content || "";
        MyApp.resMgr.loadCoin(this.mailIcon!);
    }
}


