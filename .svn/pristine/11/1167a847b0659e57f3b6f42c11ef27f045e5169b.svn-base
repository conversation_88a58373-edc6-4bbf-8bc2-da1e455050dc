import { _decorator, Graphics, Color, Node, Vec3 } from 'cc';
import { GizmoDrawer, RegisterGizmoDrawer } from './GizmoDrawer';
import { GizmoUtils } from './GizmoUtils';
import { LevelEditorEventUI } from 'db://assets/editor/level/LevelEditorEventUI';

/**
 * Gizmo drawer for LevelEditorEventUI components
 * Draws simple geometric shapes to represent event triggers
 */
@RegisterGizmoDrawer
export class LevelEditorEventUIGizmo extends GizmoDrawer<LevelEditorEventUI> {

    public readonly componentType = LevelEditorEventUI;
    public readonly drawerName = "LevelEditorEventUIGizmo";

    // Display settings
    public iconSize: number = 200;
    public circleColor: Color = new Color(255, 165, 0, 255); // Orange
    public flashColor: Color = new Color(255, 255, 0, 255);  // Yellow

    public drawGizmos(levelEvent: LevelEditorEventUI, graphics: Graphics, node: Node): void {
        // Only draw if we have triggers
        const hasTriggers = levelEvent.triggers && levelEvent.triggers.length > 0;
        if (!hasTriggers) return;

        // Convert world position to gizmo graphics coordinate space
        const worldPos = node.worldPosition;
        const gizmoPos = this.worldToGizmoSpace(worldPos, graphics.node);

        // Draw the event icon: circle + flash symbol
        this.drawEventIcon(graphics, gizmoPos.x, gizmoPos.y);
    }

    /**
     * Draw event icon using simple geometric shapes
     */
    private drawEventIcon(graphics: Graphics, x: number, y: number): void {
        // Draw circle background
        GizmoUtils.drawCircle(graphics, x, y, this.iconSize / 2, this.circleColor, false, 20);

        // Draw flash symbol (3 lines forming a lightning bolt)
        this.drawFlashSymbol(graphics, x, y);
    }

    /**
     * Draw a simple flash/lightning symbol using 3 lines
     */
    private drawFlashSymbol(graphics: Graphics, centerX: number, centerY: number): void {
        const size = this.iconSize * 0.6; // Flash symbol is 60% of icon size
        const halfSize = size / 2;

        graphics.strokeColor = this.flashColor;
        graphics.lineWidth = 20;

        // Flash symbol made of 3 connected lines
        graphics.moveTo(centerX + halfSize * 0.3, centerY - halfSize);     // Top left
        graphics.lineTo(centerX - halfSize * 0.3, centerY - halfSize * 0.2); // Top right
        graphics.lineTo(centerX + halfSize * 0.3, centerY + halfSize * 0.2); // Bottom right
        graphics.lineTo(centerX - halfSize * 0.3, centerY + halfSize);      // Bottom left

        graphics.stroke();
    }

    /**
     * Convert world position to gizmo graphics coordinate space
     */
    private worldToGizmoSpace(worldPos: Readonly<Vec3>, gizmoNode: Node): { x: number, y: number } {
        // Convert world position to local position of the gizmo graphics node
        const localPos = new Vec3();
        gizmoNode.inverseTransformPoint(localPos, worldPos);
        return { x: localPos.x, y: localPos.y };
    }

    public getPriority(): number {
        return 10; // Draw event gizmos with medium priority
    }

    /**
     * Configure display options
     */
    public configure(options: {
        iconSize?: number;
        circleColor?: Color;
        flashColor?: Color;
    }): void {
        if (options.iconSize !== undefined) this.iconSize = options.iconSize;
        if (options.circleColor !== undefined) this.circleColor = options.circleColor;
        if (options.flashColor !== undefined) this.flashColor = options.flashColor;
    }
}
