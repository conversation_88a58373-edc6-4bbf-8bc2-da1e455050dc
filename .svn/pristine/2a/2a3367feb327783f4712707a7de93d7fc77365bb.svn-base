import { _decorator, CCFloat, Component, Vec3 } from "cc";
import { GameIns } from "../GameIns";
import { GameEnum } from "../const/GameEnum";

const { ccclass, property } = _decorator;

@ccclass('CameraMove')
export class <PERSON><PERSON>ove extends Component {
    
    @property(CCFloat)
    public followDelay = 0.3; // 跟随延迟时间（秒）
    
    @property(CCFloat)
    maxFollowSpeed: number = 300; // 最大跟随速度（像素/秒）

    @property(CCFloat)
    moveThreshold = 50; // 移动范围阈值（像素），飞机移动超过此范围摄像机才会跟随
    
    @property(CCFloat)
    acceleration = 5.0; // 加速度系数
    
    private _targetPosition: Vec3 = new Vec3(); // 目标位置
    private _currentPosition: Vec3 = new Vec3(); // 当前位置
    private _velocity: number = 0; // 当前速度（只考虑X轴）
    private _lastTargetX: number = 0; // 上一次的目标X位置
    private _isMoving: boolean = false; // 是否正在移动
    private _deadZoneCenter: number = 0; // 死区中心位置

    start() {
        // 初始化位置
        this.node.getPosition(this._currentPosition);
        this._targetPosition.set(this._currentPosition);
        this._lastTargetX = this._currentPosition.x;
        this._deadZoneCenter = this._currentPosition.x;
        this._isMoving = false;
        this._velocity = 0;
    }

    update(deltaTime: number) {
        if (GameIns.gameStateManager.gameState != GameEnum.GameState.Battle) {
            return; // 游戏未进入战斗状态时不处理
        }

        let mainPlane = GameIns.mainPlaneManager.mainPlane;
        if (!mainPlane) return;
        
        // 获取飞机位置
        const planePos = mainPlane.node.position;
        
        // 计算摄像机应该跟随的目标位置
        this.calculateTargetPosition(planePos);
        
        // 更新摄像机位置
        this.updateCameraPosition(deltaTime);
        
        // 更新上一次的目标位置
        this._lastTargetX = this._targetPosition.x;
    }
    
    /**
     * 计算摄像机目标位置
     * @param planePos 飞机位置
     */
    private calculateTargetPosition(planePos: Vec3) {
        const sceneWidth = 950;
        const sceneCenterX = 0;
    
        const planeOffsetX = sceneCenterX - planePos.x; // 反向计算偏移量
        let cameraOffsetX = (planeOffsetX / (sceneWidth / 2)) * 100;
        cameraOffsetX = Math.max(-100, Math.min(100, cameraOffsetX));
    
        this._targetPosition.set(
            cameraOffsetX,
            this._currentPosition.y,
            this._currentPosition.z
        );
    }
    
    /**
     * 更新摄像机位置
     * @param deltaTime 帧时间
     */
    private updateCameraPosition(deltaTime: number) {
        // 获取当前位置
        this.node.getPosition(this._currentPosition);
        
        // 计算当前位置与目标位置的距离
        const distance = Math.abs(this._currentPosition.x - this._targetPosition.x);
        
        // 检查是否需要开始移动（超出死区）
        if (!this._isMoving && distance > this.moveThreshold) {
            this._isMoving = true;
            this._deadZoneCenter = this._currentPosition.x;
        }
        
        // 检查是否可以停止移动（进入死区）
        if (this._isMoving && distance < this.moveThreshold * 0.3) {
            this._isMoving = false;
            this._velocity = 0;
            this._deadZoneCenter = this._currentPosition.x;
        }
        
        // 如果正在移动，执行平滑移动
        if (this._isMoving) {
            this.smoothMoveToTarget(deltaTime);
        }
    }
    
    /**
     * 平滑移动到目标位置
     * @param deltaTime 帧时间
     */
    private smoothMoveToTarget(deltaTime: number) {
        // 计算当前位置与目标位置的距离和方向
        const distance = this._targetPosition.x - this._currentPosition.x;
        const direction = Math.sign(distance);
        const absDistance = Math.abs(distance);
        
        // 如果距离很小，直接设置到目标位置
        if (absDistance < 0.1) {
            this._currentPosition.x = this._targetPosition.x;
            this.node.setPosition(this._currentPosition);
            this._velocity = 0;
            return;
        }
        
        // 计算期望速度（基于距离和延迟时间）
        // 使用平滑的加速度曲线，避免突然加速
        const desiredSpeed = this.calculateDesiredSpeed(absDistance, direction);
        
        // 平滑调整当前速度
        this.adjustVelocity(desiredSpeed, deltaTime);
        
        // 计算新位置
        const newX = this._currentPosition.x + this._velocity * deltaTime;
        
        // 防止过冲：检查移动方向是否正确
        const newDistance = this._targetPosition.x - newX;
        const newDirection = Math.sign(newDistance);
        
        if (direction !== 0 && newDirection !== direction) {
            // 如果方向改变，说明会过冲，直接设置到目标位置
            this._currentPosition.x = this._targetPosition.x;
            this._velocity = 0;
        } else {
            // 否则正常移动
            this._currentPosition.x = newX;
        }
        
        // 设置新位置
        this.node.setPosition(this._currentPosition);
    }
    
    /**
     * 计算期望速度
     * @param distance 距离
     * @param direction 方向
     * @returns 期望速度
     */
    private calculateDesiredSpeed(distance: number, direction: number): number {
        // 使用缓动函数计算期望速度，避免突然加速
        // 当距离较远时，使用最大速度
        // 当距离较近时，逐渐减速
        
        // 计算距离比例（0到1之间）
        const distanceRatio = Math.min(distance / (this.moveThreshold * 2), 1);
        
        // 使用平方缓动函数，使加速和减速更平滑
        const easeFactor = distanceRatio < 0.5 
            ? 2 * distanceRatio * distanceRatio  // 加速阶段
            : 1 - Math.pow(-2 * distanceRatio + 2, 2) / 2;  // 减速阶段
        
        // 计算期望速度
        return direction * easeFactor * this.maxFollowSpeed;
    }
    
    /**
     * 调整速度
     * @param desiredSpeed 期望速度
     * @param deltaTime 帧时间
     */
    private adjustVelocity(desiredSpeed: number, deltaTime: number) {
        // 计算速度差
        const speedDiff = desiredSpeed - this._velocity;
        
        // 计算加速度（限制最大加速度）
        const acceleration = Math.sign(speedDiff) * 
            Math.min(Math.abs(speedDiff) * this.acceleration, this.maxFollowSpeed * 2);
        
        // 更新速度
        this._velocity += acceleration * deltaTime;
        
        // 限制速度不超过最大值
        if (Math.abs(this._velocity) > this.maxFollowSpeed) {
            this._velocity = Math.sign(this._velocity) * this.maxFollowSpeed;
        }
    }

    /**
     * 强制移动摄像机到目标位置
     */
    public forceMoveToTarget() {
        this.node.getPosition(this._currentPosition);
        this.node.setPosition(this._targetPosition);
        this._velocity = 0;
        this._isMoving = false;
        this._deadZoneCenter = this._targetPosition.x;
    }

    /**
     * 重置摄像机状态
     */
    public resetCamera() {
        this.node.getPosition(this._currentPosition);
        this._targetPosition.set(this._currentPosition);
        this._velocity = 0;
        this._isMoving = false;
        this._lastTargetX = this._currentPosition.x;
        this._deadZoneCenter = this._currentPosition.x;
    }
}