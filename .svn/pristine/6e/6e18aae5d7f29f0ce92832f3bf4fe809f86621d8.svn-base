import { randomRange, Vec2 } from "cc";
import { MyApp } from "db://assets/bundles/common/script/app/MyApp";
import { BoolOpType, CondOPType, ResBuffer, ResSkillCondition, ResSkillConditionElem, SkillConditionType, TargetType } from "db://assets/bundles/common/script/autogen/luban/schema";
import { logInfo, logWarn } from "db://assets/scripts/utils/Logger";
import BaseComp from "db://assets/bundles/common/script/game/ui/base/BaseComp";
import type PlaneBase from "db://assets/bundles/common/script/game/ui/plane/PlaneBase";
import { GameIns } from "db://assets/bundles/common/script/game/GameIns";
import { ExCondition, ExConditionNum } from "./ExCondition";
import forEachEntityByTargetType from "./SearchTarget";

class TriggerCondition {
    res: ResSkillCondition
    constructor(res: ResSkillCondition) {
        this.res = res;
    }
}

export class Buff {
    id: number;
    res: ResBuffer
    removeConditionRes: ResSkillCondition|undefined = undefined
    removeConditionElems: ResSkillConditionElem[]|null = null
    forbinConditionRes: ResSkillCondition|undefined = undefined
    forbinConditionElems: ResSkillConditionElem[]|null = null
    triggerConditionRes: ResSkillCondition|undefined = undefined
    triggerConditionElems: ResSkillConditionElem[]|null = null
    time = 0;
    cycleTimes = 0;
    isOutside: boolean;
    forbin:boolean = false;
    stack:number = 0;
    static incID = 1;
    constructor(isOutside: boolean, data: ResBuffer, target:PlaneBase) {
        this.id = Buff.incID++;
        this.res = data;
        this.isOutside = isOutside;
        if (this.res.removeCondition) {
            this.removeConditionRes = MyApp.lubanTables.TbResSkillCondition.get(this.res.removeCondition);
            this.removeConditionElems = Buff.conditionRes2Elems(target, this.removeConditionRes);
        }
        if (this.res.forbinCondition) {
            this.forbinConditionRes = MyApp.lubanTables.TbResSkillCondition.get(this.res.forbinCondition);
            this.forbinConditionElems = Buff.conditionRes2Elems(target, this.forbinConditionRes);
        }
        if (this.res.triggerCondition) {
            this.triggerConditionRes = MyApp.lubanTables.TbResSkillCondition.get(this.res.triggerCondition);
            this.triggerConditionElems = Buff.conditionRes2Elems(target, this.triggerConditionRes);
        }
    }

    static conditionRes2Elems(target: PlaneBase, res: ResSkillCondition|undefined) {
        if (!res) {
            return null;
        }
        const elems = Array.from(res.conditions);
        for (let i = 0; i < elems.length; i++) {
            switch (elems[i].type) {
                case SkillConditionType.PickDiamond:
                    elems[i] = new ExConditionNum(elems[i], target.pickDiamondNum);
                    break;
                case SkillConditionType.KillEnemyNum:
                    elems[i] = new ExConditionNum(elems[i], target.killEnemyNum);
                    break;
                case SkillConditionType.UseNuclear:
                    elems[i] = new ExConditionNum(elems[i], target.usedNuclearNum);
                    break;
                case SkillConditionType.UserSuper:
                    elems[i] = new ExConditionNum(elems[i], target.usedSuperNum);
                    break;
            }
        }
        return res.conditions;
    }

    static checkCondition(self: PlaneBase, res: ResSkillCondition, elems: ResSkillConditionElem[]|null) {
        let target: PlaneBase | null = null;
        forEachEntityByTargetType(self, res.target, (entity) => {
            target = entity;
        });
        if (!target) {
            return false
        }
        if (res.conditions.length == 0) {
            return true;
        }
        let ret = res.boolType == BoolOpType.AND ? true : false;
        elems?.forEach((condition) => {
            let value = 0;
            switch(condition.type) {
                case SkillConditionType.BuffStack:
                    {
                        if (condition.params.length < 1) {
                            break
                        }
                        value = target!.buffComp!.GetBuff(condition.params[0])?.stack || 0;
                    }
                    break
                case SkillConditionType.CurHPPer:
                    value = target!.curHp / target!.maxHp * 10000;
                    break;
                case SkillConditionType.CurHP:
                    value = target!.curHp;
                    break;
                case SkillConditionType.MaxHP:
                    value = target!.maxHp;
                    break;
                case SkillConditionType.BeAttackTime:
                    value = GameIns.battleManager._gameTime - target!.hurtTime;
                    break;
                case SkillConditionType.PickDiamond:
                    if (condition instanceof ExConditionNum) {
                        value = target!.pickDiamondNum - condition.num;
                    }
                case SkillConditionType.KillEnemyNum:
                case SkillConditionType.KillEnemy:
                    if (condition instanceof ExConditionNum) {
                        value = target!.killEnemyNum - condition.num;
                    }
                    break;
                case SkillConditionType.RemainNuclearNum:
                    value = target!.nuclearNum;
                    break;
                case SkillConditionType.UsedNuclearNum:
                    value = target!.usedNuclearNum;
                    break;
                case SkillConditionType.LevelStart:
                    value = GameIns.battleManager._gameTime == 0 ? 1 : 0;
                    break;
                case SkillConditionType.BossBeKilled:
                    // TODO ybgg
                    break;
                case SkillConditionType.UseNuclear:
                    if (condition instanceof ExConditionNum) {
                        value = target!.usedNuclearNum - condition.num;
                    }
                case SkillConditionType.UserSuper:
                    if (condition instanceof ExConditionNum) {
                        value = target!.usedSuperNum - condition.num;
                    }
                    break;
                case SkillConditionType.GameTime:
                    value = GameIns.battleManager._gameTime;
                    break;
                case SkillConditionType.EnemyCount:
                    if (condition.params.length >= 2) {
                        const radiusSqr = condition.params[1]*condition.params[1];
                        if (target?.enemy) {
                            if (Vec2.squaredDistance(target!.node.position, GameIns.mainPlaneManager.mainPlane!.node.position) <= radiusSqr) {
                                value++;
                            }
                        } else {
                            forEachEntityByTargetType(target!, TargetType.Enemy, (entity) => {
                                if (entity.isDead && Vec2.squaredDistance(entity.node.position, target!.node.position) <= radiusSqr) {
                                    value++;
                                }
                            });
                        }
                    }
                    break;
                case SkillConditionType.WaveNo:
                    // TODO ybgg
                    // value = GameIns.battleManager.waveNo;
                    break;
                case SkillConditionType.Distance:
                    value = Vec2.distance(target!.node.position, GameIns.mainPlaneManager.mainPlane!.node.position);
                    break;
                case SkillConditionType.KillEnemy:
                    if (condition instanceof ExConditionNum) {
                        value = target!.killEnemyNum - condition.num;
                    }
                    break;

            }
            let ret2 = false;
            switch(condition.op) {
                case CondOPType.EQ:
                    ret2 = value == condition.value;
                    break;
                case CondOPType.GE:
                    ret2 = value >= condition.value;
                    break;
                case CondOPType.GT:
                    ret2 = value > condition.value;
                    break;
                case CondOPType.LE:
                    ret2 = value <= condition.value;
                    break;
                case CondOPType.LT:
                    ret2 = value < condition.value;
                    break;
                case CondOPType.NE:
                    ret2 = value != condition.value;
                    break;
            }
            if (res.boolType == BoolOpType.AND) {
                ret = ret && ret2;
            } else {
                ret = ret || ret2;
            }
        })
        
        return ret;
    }

    checkRemoveCondition(self: PlaneBase) {
        if (this.res.duration != -1 && this.time >= this.res.duration) {
            return true;
        }
        if (!this.removeConditionRes) {
            return false;
        }
        return Buff.checkCondition(self, this.removeConditionRes, this.removeConditionElems);
    }
    resetTriggerCondition() {
        this.triggerConditionElems?.forEach((condition) => {
            if (condition instanceof ExCondition) {
                condition.reset()
            }
        })
    }
}

export default class BuffComp extends BaseComp {
    private buffs: Map<number, Buff> = new Map();

    ApplyBuff(isOutside:boolean, buffID: number) {
        logInfo("Buff", `apply buff ${buffID}`)
        let buffData = MyApp.lubanTables.TbResBuffer.get(buffID);
        if (!buffData) {
            logWarn("Buff", `apply buff ${buffID} but config not found`)
            return;
        }
        const removeConditionRes = buffData.removeCondition ? MyApp.lubanTables.TbResSkillCondition.get(buffData.removeCondition) : undefined;
        if (removeConditionRes) {
            if (Buff.checkCondition(this.entity as PlaneBase, removeConditionRes, removeConditionRes.conditions)) {
                return;
            }
        }
        let buff: Buff | null = null
        if (buffData.duration != 0) {
            let buff = this.buffs.get(buffID)
            if (!buff) {
                buff = new Buff(isOutside, buffData, this.entity as PlaneBase)!;
                this.buffs.set(buffID, buff);
            }
            if (buffData.refreshType && buffData.duration != -1) {
                buff.time = 0;
            }
            let stack = buffData.maxStack < 1 ? 1 : buffData.maxStack;
            buff.stack = Math.min(stack, buff.stack + 1);
        }
        buffData.effects.forEach((applyEffect) => {
            forEachEntityByTargetType(this.entity as PlaneBase, applyEffect.target, (entity) => {
                entity.ApplyBuffEffect(buff, applyEffect);
            })
        })
    }

    HasBuff(buffID: number) {
        return this.buffs.has(buffID);
    }

    GetBuff(buffID: number) {
        return this.buffs.get(buffID);
    }

    updateGameLogic(dt: number): void {
        this.buffs.forEach((buff, buffID) => {
            buff.time += dt * 1000;
            if (buff.checkRemoveCondition(this.entity as PlaneBase)) {
                this.removeBuff(buff, buffID);
            }
            if (buff.forbinConditionRes && Buff.checkCondition(this.entity as PlaneBase, buff.forbinConditionRes, buff.forbinConditionElems)) {
                if (!buff.forbin) {
                    buff.res.effects.forEach((applyEffect) => {
                        forEachEntityByTargetType(this.entity as PlaneBase, applyEffect.target, (entity) => {
                            entity.RemoveBuffEffect(buff, applyEffect);
                        })
                    })
                }
                buff.forbin = true;
            } else {
                if (buff.forbin) {
                    buff.res.effects.forEach((applyEffect) => {
                        forEachEntityByTargetType(this.entity as PlaneBase, applyEffect.target, (entity) => {
                            entity.ApplyBuffEffect(buff, applyEffect);
                        })
                    })
                }
                buff.forbin = false;
            }
            
            if ((buff.res.cycle > 0 &&
                    buff.time >= (buff.cycleTimes + 1) * buff.res.cycle &&
                    (buff.res.cycleTimes == 0 || buff.cycleTimes < buff.res.cycleTimes))
                || (buff.triggerConditionElems && Buff.checkCondition(this.entity as PlaneBase, buff.triggerConditionRes!, buff.triggerConditionElems)
                )
            ) {
                buff.cycleTimes++;
                buff.resetTriggerCondition();
                buff.res.effects.forEach((applyEffect) => {
                    forEachEntityByTargetType(this.entity as PlaneBase, applyEffect.target, (entity) => {
                        entity.ApplyBuffEffect(buff, applyEffect);
                    })
                })
            }
        })
    }

    private removeBuff(buff: Buff, buffID: number) {
        buff.res.effects.forEach((applyEffect) => {
            // 这个地方和加的时候查出来的target会不同
            // 1. 需要保证查出来的target只多不少
            // 2. remove接口里面需要判断时候是这个buff的效果
            forEachEntityByTargetType(this.entity as PlaneBase, applyEffect.target, (entity) => {
                entity.RemoveBuffEffect(buff, applyEffect);
            })
        })
        this.buffs.delete(buffID);
    }
}