
import { Prefab, instantiate } from "cc";
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { PlaneData } from "db://assets/bundles/common/script/data/plane/PlaneData";
import { SingletonBase } from "../../../../../scripts/core/base/SingletonBase";
import GameResourceList from "../const/GameResourceList";
import { GameIns } from "../GameIns";
import BattleLayer from "../ui/layer/BattleLayer";
import { type MainPlane } from "../ui/plane/mainPlane/MainPlane";

export class MainPlaneManager extends SingletonBase<MainPlaneManager> {

    _planeData: PlaneData | null = null;//飞机数据
    mainPlane: MainPlane | null = null;//飞机战斗UI

    hurtTotal: number = 0;//造成的总伤害
    reviveCount: number = 0;//已复活次数
    maxReviveCount: number = 0;//可复活次数

    setPlaneData(planeData: PlaneData) {
        this._planeData = planeData;
    }

    async preload() {
        GameIns.battleManager.addLoadCount(1);
        await this.createMainPlane();
        GameIns.battleManager.checkLoadFinish();
        this.reset();
    }

    reset() {
        this.hurtTotal = 0;
        this.reviveCount = 0;
        this.maxReviveCount = 1; // 默认可复活1次
    }

    /**
     * 创建主飞机
     * @param isTrans 是否为特殊状态
     * @returns 主飞机对象
     */
    async createMainPlane(): Promise<MainPlane | null> {
        if (this.mainPlane) {
            this.mainPlane.resetPlane();
            return this.mainPlane;
        }

        const prefab = await MyApp.resMgr.loadAsync(GameResourceList.MainPlane, Prefab);
        let planeNode = instantiate(prefab);
        this.mainPlane = planeNode.getComponent("MainPlane") as MainPlane
        BattleLayer.me?.addMainPlane();
        this.mainPlane?.initPlane(this._planeData!);
        return this.mainPlane;
    }

    mainReset() {
        if (this.mainPlane) {
            this.mainPlane.node.destroy();
            this.mainPlane = null;
        }
    }

    checkCanRevive(): boolean {
        return this.reviveCount < this.maxReviveCount;
    }

    revive() {
        GameIns.mainPlaneManager.reviveCount += 1; // 增加复活次数
        this.mainPlane?.revive();
    }
}
