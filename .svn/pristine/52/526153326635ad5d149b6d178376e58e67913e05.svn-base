import { MyApp } from "db://assets/bundles/common/script/app/MyApp";
import { TargetType } from "db://assets/bundles/common/script/autogen/luban/schema";
import { logInfo, logWarn } from "db://assets/scripts/utils/Logger";
import { GameIns } from "../../../GameIns";
import BaseComp from "../../base/BaseComp";
import type PlaneBase from "db://assets/bundles/common/script/game/ui/plane/PlaneBase";
import forEachEntityByTargetType from "./SearchTarget";

export default class SkillComp extends BaseComp {
    Cast(caster: PlaneBase, skillID: number) {
        logInfo("Skill", `cast skill ${skillID}`);
        let skillData = MyApp.lubanTables.TbResSkill.get(skillID);
        if (!skillData) {
            logWarn("Skill", `cast skill ${skillID} but config not found`)
            return;
        }
        skillData.ApplyBuffs.forEach((applyBuff) => {
            forEachEntityByTargetType(caster, applyBuff.target, (entity) => {
                (entity as PlaneBase).buffComp.ApplyBuff(false, applyBuff.buffID);
            })
        })
    }
}