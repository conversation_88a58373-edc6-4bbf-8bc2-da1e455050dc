import { EventActionBase } from "db://assets/bundles/common/script/game/eventgroup/IEventAction";
import { IEventGroupContext } from "db://assets/bundles/common/script/game/eventgroup/IEventGroupContext";
import { eEmitterProp, ePropMask } from "../Emitter";
import { Property } from "../PropertyContainer";

export class Emitter<PERSON><PERSON><PERSON>ase extends EventActionBase {
    // this was intentionally left blank
}

export class EmitterActionBase_BoolModifier extends EmitterActionBase {
    protected get propertyType(): eEmitterProp {
        return eEmitterProp.IsActive;
    }
    protected _targetProperty: Property<boolean>|undefined = undefined;
    onLoad(context: IEventGroupContext): void {
        this._targetProperty = context.emitter!.getProperty(this.propertyType);
        super.onLoad(context);
    }
    onStart(context: IEventGroupContext): void {
        this._targetProperty!.addWriteMask(ePropMask.EventGroup);
        super.onStart(context);
    }
    onComplete(context: IEventGroupContext): void {
        this._targetProperty!.removeWriteMask(ePropMask.EventGroup);
        super.onComplete(context);
    }
    canLerp(): boolean {
        return false;
    }
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = this._targetProperty!.value ? 1 : 0;
    }
    protected onExecuteInternal(context: IEventGroupContext, value: number): void {
        this._targetProperty!.setValue(value === 1, ePropMask.EventGroup);
    }
}

export class EmitterActionBase_NumberModifier extends EmitterActionBase {
    protected get propertyType(): eEmitterProp {
        // override this
        return eEmitterProp.InitialDelay;
    }
    protected _targetProperty: Property<number>|undefined = undefined;
    onLoad(context: IEventGroupContext): void {
        this._targetProperty = context.emitter!.getProperty(this.propertyType);
        super.onLoad(context);
    }

    onStart(context: IEventGroupContext): void {
        this._targetProperty!.addWriteMask(ePropMask.EventGroup);
        super.onStart(context);
    }

    onComplete(context: IEventGroupContext): void {
        this._targetProperty!.removeWriteMask(ePropMask.EventGroup);
        super.onComplete(context);
    }

    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = this._targetProperty!.value;
    }

    protected onExecuteInternal(context: IEventGroupContext, value: number): void {
        this._targetProperty!.setValue(value, ePropMask.EventGroup);
    }    
}

// 修改发射器启用状态
export class EmitterAction_Active extends EmitterActionBase_BoolModifier {
    protected get propertyType(): eEmitterProp {
        return eEmitterProp.IsActive;
    }

    protected onExecuteInternal(context: IEventGroupContext, value: number): void {
        super.onExecuteInternal(context, value);
        this._targetProperty!.notify();
    }
}

export class EmitterAction_Prewarm extends EmitterActionBase_BoolModifier {
    protected get propertyType(): eEmitterProp {
        return eEmitterProp.IsPreWarm;
    }
}

// 修改发射器初始延迟时间
export class EmitterAction_InitialDelay extends EmitterActionBase_NumberModifier {
    protected get propertyType(): eEmitterProp {
        return eEmitterProp.InitialDelay;
    }
}

export class EmitterAction_PrewarmDuration extends EmitterActionBase_NumberModifier {
    protected get propertyType(): eEmitterProp {
        return eEmitterProp.PrewarmDuration;
    }
}

// 修改发射器持续时间
export class EmitterAction_Duration extends EmitterActionBase_NumberModifier {
    protected get propertyType(): eEmitterProp {
        return eEmitterProp.EmitDuration;
    }
}

// 修改发射器已运行时间
export class EmitterAction_ElapsedTime extends EmitterActionBase_NumberModifier {
    protected get propertyType(): eEmitterProp {
        return eEmitterProp.ElapsedTime;
    }
}

// 修改发射器是否循环(boolean)
export class EmitterAction_Loop extends EmitterActionBase_BoolModifier {
    protected get propertyType(): eEmitterProp {
        return eEmitterProp.IsLoop;
    }
}

// 循环间隔
export class EmitterAction_LoopInterval extends EmitterActionBase_NumberModifier {
    protected get propertyType(): eEmitterProp {
        return eEmitterProp.LoopInterval;
    }
}

export class EmitterAction_EmitInterval extends EmitterActionBase_NumberModifier {
    protected get propertyType(): eEmitterProp {
        return eEmitterProp.EmitInterval;
    }
}

export class EmitterAction_PerEmitCount extends EmitterActionBase_NumberModifier {
    protected get propertyType(): eEmitterProp {
        return eEmitterProp.PerEmitCount;
    }
}

export class EmitterAction_PerEmitInterval extends EmitterActionBase_NumberModifier {
    protected get propertyType(): eEmitterProp {
        return eEmitterProp.PerEmitInterval;
    }
}

export class EmitterAction_PerEmitOffsetX extends EmitterActionBase_NumberModifier {
    protected get propertyType(): eEmitterProp {
        return eEmitterProp.PerEmitOffsetX;
    }
}

export class EmitterAction_Angle extends EmitterActionBase_NumberModifier {
    protected get propertyType(): eEmitterProp {
        return eEmitterProp.Angle;
    }
}

export class EmitterAction_Count extends EmitterActionBase_NumberModifier {
    protected get propertyType(): eEmitterProp {
        return eEmitterProp.Count;
    }
}

// 以下是发射器修改子弹属性的部分
export class EmitterAction_BulletDuration extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.duration.value;
    }

    protected onExecuteInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.duration.value = value;
    }
}

export class EmitterAction_BulletDamage extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        // this._startValue = context.emitter!.bulletProp.damage.value;
    }

    protected onExecuteInternal(context: IEventGroupContext, value: number): void {
        // context.emitter!.bulletProp.damage.value = value;
    }
}

export class EmitterAction_BulletSpeed extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.speed.value;
    }

    protected onExecuteInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.speed.value = value;
    }
}

export class EmitterAction_BulletSpeedAngle extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.speedAngle.value;
    }

    protected onExecuteInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.speedAngle.value = value;
    }
}

export class EmitterAction_BulletAcceleration extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.acceleration.value;
    }

    protected onExecuteInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.acceleration.value = value;
    }
}

export class EmitterAction_BulletAccelerationAngle extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.accelerationAngle.value;
    }

    protected onExecuteInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.accelerationAngle.value = value;
    }
}

export class EmitterAction_BulletScale extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.scale.value;
    }

    protected onExecuteInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.scale.value = value;
    }
}

export class EmitterAction_BulletColorR extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.color.value.r;
    }

    protected onExecuteInternal(context: IEventGroupContext, value: number): void {
        let color = context.emitter!.bulletProp.color.value;
        color.r = value;
        context.emitter!.bulletProp.color.value = color;
    }
}

export class EmitterAction_BulletColorG extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.color.value.g;
    }

    protected onExecuteInternal(context: IEventGroupContext, value: number): void {
        let color = context.emitter!.bulletProp.color.value;
        color.g = value;
        context.emitter!.bulletProp.color.value = color;
    }
}

export class EmitterAction_BulletColorB extends EmitterActionBase {
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.color.value.b;
    }

    protected onExecuteInternal(context: IEventGroupContext, value: number): void {
        let color = context.emitter!.bulletProp.color.value;
        color.b = value;
        context.emitter!.bulletProp.color.value = color;
    }
}

export class EmitterAction_BulletFacingMoveDir extends EmitterActionBase {
    canLerp(): boolean {
        return false;
    }
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.isFacingMoveDir.value ? 1 : 0;
    }
    protected onExecuteInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.isFacingMoveDir.value = value === 1;
    }
}

export class EmitterAction_BulletTrackingTarget extends EmitterActionBase {
    canLerp(): boolean {
        return false;
    }
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.isTrackingTarget.value ? 1 : 0;
    }
    protected onExecuteInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.isTrackingTarget.value = value === 1;
    }
}

export class EmitterAction_BulletDestructive extends EmitterActionBase {
    canLerp(): boolean {
        return false;
    }
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.isDestructive.value ? 1 : 0;
    }
    protected onExecuteInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.isDestructive.value = value === 1;
    }
}

export class EmitterAction_BulletDestructiveOnHit extends EmitterActionBase {
    canLerp(): boolean {
        return false;
    }
    protected resetStartValue(context: IEventGroupContext): void {
        this._startValue = context.emitter!.bulletProp.isDestructiveOnHit.value ? 1 : 0;
    }
    protected onExecuteInternal(context: IEventGroupContext, value: number): void {
        context.emitter!.bulletProp.isDestructiveOnHit.value = value === 1;
    }
}