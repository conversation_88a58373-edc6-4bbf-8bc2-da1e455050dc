import { _decorator, Label, math, Node, Sprite, tween, Tween } from 'cc';
const { ccclass, property } = _decorator;

import { DamageType, EffectParam, EffectType } from 'db://assets/bundles/common/script/autogen/luban/schema';
import { AttributeConst } from 'db://assets/bundles/common/script/const/AttributeConst';
import { AttributeData } from 'db://assets/bundles/common/script/data/base/AttributeData';
import FBoxCollider from 'db://assets/bundles/common/script/game/collider-system/FBoxCollider';
import FCircleCollider from 'db://assets/bundles/common/script/game/collider-system/FCircleCollider';
import FPolygonCollider from 'db://assets/bundles/common/script/game/collider-system/FPolygonCollider';
import Entity from 'db://assets/bundles/common/script/game/ui/base/Entity';
import { GameEnum } from '../../const/GameEnum';
import BuffComp, { Buff } from './skill/BuffComp';
import SkillComp from './skill/SkillComp';


@ccclass('PlaneBase')
export default class PlaneBase extends Entity {

    @property(Node)
    hpNode: Node | null = null;
    @property(Sprite)
    hpBar: Sprite | null = null; // 血条
    @property(Sprite)
    hpAniSprite: Sprite | null = null; // 血条动画条
    @property(Label)
    hpfont: Label | null = null; // 血条文本

    enemy = true; // 是否为敌机
    type = 0; // 敌人类型
    bDamageable: boolean = true; // 是否可以被造成伤害

    get maxHp(): number{
        return  this.attribute.getMaxHP();
    };
    curHp: number = 0;

    collideComp: FCircleCollider | FBoxCollider | FPolygonCollider | null = null; // 碰撞组件

    private _skillComp: SkillComp | null = null;
    private _buffComp: BuffComp | null = null;

    // TODO 临时做法，后续应该挪到 PlaneBase
    private _attributeData: AttributeData = new AttributeData();

    init() {
        this._skillComp = new SkillComp();
        this.addComp("skill", this._skillComp);
        this._buffComp = new BuffComp();
        this.addComp("buff", this._buffComp)
        super.init();
    }

    get skillComp() {
        return this._skillComp!;
    }

    get buffComp() {
        return this._buffComp!;
    }

    get attribute(): AttributeData {
        return this._attributeData;
    }

    set colliderEnabled(value: boolean) {
        if (this.collideComp) {
            this.collideComp.isEnable = value;
        }
    }
    get colliderEnabled(): boolean {
        return this.collideComp ? this.collideComp.isEnable : false;
    }

    CastSkill(skillID: number) {
        this.skillComp.Cast(this, skillID);
    }

    addHp(heal: number) {
        this.curHp = Math.min(
            this.maxHp,
            this.curHp + heal
        );
        this.updateHpUI();;
    }

    hurt(damage: number) {
        if (this.isDead) {
            return;
        }
        this.cutHp(damage);
        this.playHurtAnim();
        if (this.curHp <= 0) {
            this.toDie();
        }
    }

    get collisionLevel() {
        return 0;
    }
    get collisionHurt() {
        return 0;
    }

    // 撞机
    collisionPlane(plane: PlaneBase) {
        if (this.isDead || plane.isDead) {
            return;
        }
        if (this.collisionLevel > plane.collisionLevel) {
            return
        }
        let hurt:number
        if (this.collisionLevel < plane.collisionLevel) {
            hurt = Math.max(this.maxHp, plane.collisionHurt)
        } else {
            hurt = plane.collisionHurt == -1 ? this.maxHp : plane.collisionHurt
        }

        hurt = (hurt - this.attribute.getFinialAttributeByOutInKey(
                AttributeConst.CollisionHurtResistanceOutAdd, AttributeConst.CollisionHurtResistanceOutPer, 
                AttributeConst.CollisionHurtResistanceInAdd, AttributeConst.CollisionHurtResistanceInPer))
            * (1 - this.attribute.getFinalAttributeByKey(AttributeConst.CollisionHurtDerateOut))
            * (1 - this.attribute.getFinalAttributeByKey(AttributeConst.CollisionHurtDerateIn));
        this.hurt(hurt);
    }

    /**
     * 减少血量
     * @param {number} damage 受到的伤害值
     */
    cutHp(damage: number) {
        const newHp = this.curHp - damage;
        this.curHp = Math.max(0, newHp);

        this.updateHpUI();
    }

    toDie(destroyType: GameEnum.EnemyDestroyType = GameEnum.EnemyDestroyType.Die): boolean {
        if (this.isDead) {
            return false
        }
        this.isDead = true;
        this.colliderEnabled = false;
        return true
    }
    /**
     * 更新血量显示
     */
    updateHpUI() {
        if (this.hpBar) {
            // 更新血条前景的填充范围
            this.hpBar.fillRange = this.curHp / this.maxHp;

            if (this.hpAniSprite) {
                // 计算血条动画时间
                const duration = Math.abs(this.hpAniSprite.fillRange - this.hpBar.fillRange);

                Tween.stopAllByTarget(this.hpAniSprite);
                // 血条中间部分的动画
                tween(this.hpAniSprite)
                    .to(duration, { fillRange: this.hpBar.fillRange })
                    .call(() => {

                    })
                    .start();
            }
        }

        // 更新血量文字
        this.hpfont && (this.hpfont!.string = this.curHp.toFixed(0));
    }

    playHurtAnim() {
        // 子类实现
    }

    ApplyBuffEffect(buff: Buff | null, effectData: EffectParam) {
        switch (effectData.type) {
            case EffectType.AttrMaxHPPer:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.MaxHPOutPer, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.MaxHPInPer, effectData);
                }
                break;
            case EffectType.AttrMaxHPAdd:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.MaxHPOutAdd, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.MaxHPInAdd, effectData);
                }
                break;
            case EffectType.AttrHPRecoveryPer:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.HPRecoveryOutPer, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.HPRecoveryInPer, effectData);
                }
                break;
            case EffectType.AttrHPRecoveryAdd:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.HPRecoveryOutAdd, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.HPRecoveryInAdd, effectData);
                }
                break;
            case EffectType.AttrHPRecoveryMaxHPPerAdd:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.MaxHPRecoveryRateOut, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.MaxHPRecoveryRateIn, effectData);
                }
                break;
            case EffectType.HealMaxHPPer:
                if (effectData.param.length >= 1) {
                    this.addHp(this.maxHp * effectData.param[0]/10000);
                }
                break;
            case EffectType.HealLoseHPPer:
                if (effectData.param.length >= 1) {
                    this.addHp((this.maxHp - this.curHp) * effectData.param[0]/10000);
                }
                break;
            case EffectType.HealHP:
                if (effectData.param.length >= 1) {
                    this.addHp(effectData.param[0]);
                }
                break;
            case EffectType.AttrAttackPer:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.AttackOutPer, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.AttackInPer, effectData);
                }
                break;
            case EffectType.AttrAttackAdd:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.AttackOutAdd, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.AttackInAdd, effectData);
                }
                break;
            case EffectType.AttrAttackBossPer:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.BossHurtBonusOut, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.BossHurtBonusIn, effectData);
                }
                break;
            case EffectType.AttrAttackNormalPer:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.NormalHurtBonusOut, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.NormalHurtBonusIn, effectData);
                }
                break;
            case EffectType.AttrFortunatePer:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.FortunateOutPer, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.FortunateInPer, effectData);
                }
                break;
            case EffectType.AttrFortunateAdd:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.FortunateOutAdd, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.FortunateInAdd, effectData);
                }
                break;
            case EffectType.AttrMissAdd:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.MissRateOut, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.MissRateIn, effectData);
                }
                break;
            case EffectType.AttrBulletHurtResistancePer:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtResistanceOutPer, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtResistanceInPer, effectData);
                }
                break;
            case EffectType.AttrBulletHurtResistanceAdd:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtResistanceOutAdd, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtResistanceInAdd, effectData);
                }
                break;
            case EffectType.AttrBulletHurtDerateAdd:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtDerateOut, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtDerateIn, effectData);
                }
                break;
            case EffectType.AttrCollisionHurtResistancePer:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.CollisionHurtResistanceOutPer, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.CollisionHurtResistanceInPer, effectData);
                }
                break;
            case EffectType.AttrCollisionHurtResistanceAdd:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.CollisionHurtResistanceOutAdd, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.CollisionHurtResistanceInAdd, effectData);
                }
                break;
            case EffectType.AttrCollisionHurtDerateAdd:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.CollisionHurtDerateOut, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.CollisionHurtDerateIn, effectData);
                }
                break;
            case EffectType.AttrFinalScoreAdd:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.FinalScoreRateOut, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.FinalScoreRateIn, effectData);
                }
                break;
            case EffectType.AttrKillScoreAdd:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.KillScoreRateOut, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.KillScoreRateIn, effectData);
                }
                break;
            case EffectType.AttrEnergyRecoveryPerAdd:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergyRecoveryOutPer, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergyRecoveryInPer, effectData);
                }
                break;
            case EffectType.AttrEnergyRecoveryAdd:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergyRecoveryOutAdd, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergyRecoveryInAdd, effectData);
                }
                break;
            case EffectType.AttrPickRadiusPer:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.PickRadiusOutPer, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.PickRadiusInPer, effectData);
                }
                break;
            case EffectType.AttrPickRadiusAdd:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.PickRadiusOutAdd, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.PickRadiusInAdd, effectData);
                }
                break;
            case EffectType.ApplyBuff:
                if (effectData.param.length < 2) {
                    return;
                }
                const buffID = effectData.param[0];
                const target = effectData.param[1];
                SkillComp.forEachByTargetType(this, target, (entity) => {
                    entity.buffComp.ApplyBuff(buff?.isOutside||false, buffID);
                })
                break;
            case EffectType.ImmuneBulletHurt:
                if (buff) {
                    this.attribute.addModify(buff.id, AttributeConst.StatusImmuneBulletHurt, 1);
                }
                break;
            case EffectType.ImmuneCollisionHurt:
                if (buff) {
                    this.attribute.addModify(buff.id, AttributeConst.StatusImmuneCollisionHurt, 1);
                }
                break;
            case EffectType.IgnoreBullet:
                if (buff) {
                    this.attribute.addModify(buff.id, AttributeConst.StatusIgnoreBullet, 1);
                }
                break;
            case EffectType.IgnoreCollision:
                if (buff) {
                    this.attribute.addModify(buff.id, AttributeConst.StatusIgnoreCollision, 1);
                }
                break;
            case EffectType.ImmuneNuclearHurt:
                if (buff) {
                    this.attribute.addModify(buff.id, AttributeConst.StatusImmuneNuclearHurt, 1);
                }
                break;
            case EffectType.ImmuneActiveSkillHurt:
                if (buff) {
                    this.attribute.addModify(buff.id, AttributeConst.StatusImmuneActiveSkillHurt, 1);
                }
                break;
            case EffectType.Invincible:
                if (buff) {
                    this.attribute.addModify(buff.id, AttributeConst.StatusInvincible, 1);
                }
                break;
            case EffectType.AttrNuclearMax:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.NuclearMax, effectData);
                break;
            case EffectType.AttrBulletAttackAdd:
                let damageType = DamageType.ALL;
                if (effectData.param.length > 1) {
                    damageType = effectData.param[1];
                }
                if (buff?.isOutside) {
                    switch(damageType) {
                        case DamageType.ALL:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletAttackOutAdd, effectData);
                            break;
                        case DamageType.EXPLOSIVE:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.ExplosiveBulletAttackOutAdd, effectData);
                            break;
                        case DamageType.NORMAL:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.NormalBulletAttackOutAdd, effectData);
                            break;
                        case DamageType.ENERGETIC:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergeticBulletAttackOutAdd, effectData);
                            break;
                        case DamageType.PHYSICAL:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.PhysicsBulletAttackOutAdd, effectData);
                            break;
                    }
                } else {
                    switch(damageType) {
                        case DamageType.ALL:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletAttackInAdd, effectData);
                            break;
                        case DamageType.EXPLOSIVE:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.ExplosiveBulletAttackInAdd, effectData);
                            break;
                        case DamageType.NORMAL:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.NormalBulletAttackInAdd, effectData);
                            break;
                        case DamageType.ENERGETIC:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergeticBulletAttackInAdd, effectData);
                            break;
                        case DamageType.PHYSICAL:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.PhysicsBulletAttackInAdd, effectData);
                            break;
                    }
                }
                break;
            case EffectType.AttrBulletAttackPer:
                damageType = DamageType.ALL;
                if (effectData.param.length > 1) {
                    damageType = effectData.param[1];
                }
                if (buff?.isOutside) {
                    switch(damageType) {
                        case DamageType.ALL:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletAttackOutPer, effectData);
                            break;
                        case DamageType.EXPLOSIVE:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.ExplosiveBulletAttackOutPer, effectData);
                            break;
                        case DamageType.NORMAL:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.NormalBulletAttackOutPer, effectData);
                            break;
                        case DamageType.ENERGETIC:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergeticBulletAttackOutPer, effectData);
                            break;
                        case DamageType.PHYSICAL:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.PhysicsBulletAttackOutPer, effectData);
                            break;
                    }
                } else {
                    switch(damageType) {
                        case DamageType.ALL:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletAttackInPer, effectData);
                            break;
                        case DamageType.EXPLOSIVE:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.ExplosiveBulletAttackInPer, effectData);
                            break;
                        case DamageType.NORMAL:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.NormalBulletAttackInPer, effectData);
                            break;
                        case DamageType.ENERGETIC:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergeticBulletAttackInPer, effectData);
                            break;
                        case DamageType.PHYSICAL:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.PhysicsBulletAttackInPer, effectData);
                            break;
                    }
                }
                break;
            case EffectType.AttrBulletHurtFix:
                damageType = DamageType.ALL;
                if (effectData.param.length > 1) {
                    damageType = effectData.param[1];
                }
                if (buff?.isOutside) {
                    switch(damageType) {
                        case DamageType.ALL:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtFixOut, effectData);
                            break;
                        case DamageType.EXPLOSIVE:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.ExplosiveBulletHurtFixOut, effectData);
                            break;
                        case DamageType.NORMAL:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.NormalBulletHurtFixOut, effectData);
                            break;
                        case DamageType.ENERGETIC:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergeticBulletHurtFixOut, effectData);
                            break;
                        case DamageType.PHYSICAL:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.PhysicsBulletHurtFixOut, effectData);
                            break;
                    }
                } else {
                    switch(damageType) {
                        case DamageType.ALL:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtFixIn, effectData);
                            break;
                        case DamageType.EXPLOSIVE:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.ExplosiveBulletHurtFixIn, effectData);
                            break;
                        case DamageType.NORMAL:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.NormalBulletHurtFixIn, effectData);
                            break;
                        case DamageType.ENERGETIC:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergeticBulletHurtFixIn, effectData);
                            break;
                        case DamageType.PHYSICAL:
                            this.ApplyBuffAttributeEffect(buff, AttributeConst.PhysicsBulletHurtFixIn, effectData);
                            break;
                    }
                }
                break;
            case EffectType.HurtMaxHPPer:
                if (effectData.param.length < 1) {
                    return;
                }
                this.hurt(this.maxHp * effectData.param[0]);
                break;
            case EffectType.HurtCurHPPer:
                if (effectData.param.length < 1) {
                    return;
                }
                this.hurt(this.curHp * effectData.param[0]);
                break;
            case EffectType.AttrNuclearAttackPer:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.NuclearAttackOutPer, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.NuclearAttackInPer, effectData);
                }
                break;
            case EffectType.AttrNuclearAttackAdd:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.NuclearAttackOutAdd, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.NuclearAttackInAdd, effectData);
                }
                break;
            case EffectType.FireBullet:
                // TODO not implement
                break;
            case EffectType.AttrNuclearHurtFix:
                if (buff?.isOutside) {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.NuclearHurtFixOut, effectData);
                } else {
                    this.ApplyBuffAttributeEffect(buff, AttributeConst.NuclearHurtFixIn, effectData);
                }
                break;
            default:
                break;
        }
    }
    private ApplyBuffAttributeEffect(buff: Buff | null, key: number, effectData: EffectParam) {
        if (!buff) {
            return;
        }
        if (effectData.param.length < 1) {
            return;
        }
        this.attribute.addModify(buff.id, key, effectData.param[0]);
    }
    RemoveBuffEffect(buff: Buff, effectData: EffectParam) {
        this.attribute.removeModify(buff.id);
    }

    setAnimSpeed(speed: number) {
        // 子类实现
    }

    // 获取当前的攻击目标
    // 对于敌机，返回玩家飞机；
    // 对于玩家飞机，可以按策划规则（距离或者其他规则）
    public getTarget(): PlaneBase|null {
        // 子类实现
        return null;
    }
}