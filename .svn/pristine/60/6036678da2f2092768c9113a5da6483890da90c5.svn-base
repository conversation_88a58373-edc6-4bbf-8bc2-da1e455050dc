import { MyApp } from "db://assets/bundles/common/script/app/MyApp";
import csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';
import { IData } from "db://assets/bundles/common/script/data/DataManager";
import { logError } from "db://assets/scripts/utils/Logger";
import { ResGoalType, ResTask, ResTaskClass } from '../../autogen/luban/schema';
import { DataEvent } from "../../event/DataEvent";
import { EventMgr } from "../../event/EventManager";

export class Task implements IData {
    // 任务合集
    taskMap: Map<ResTaskClass, csproto.cs.ICSTaskInfo[]> = new Map();

    public init(): void {
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_TASK_GET_INFO, this.onGetTaskInfoMsg, this)
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_TASK_GET_REWARD, this.onGetTaskRewardMsg, this)
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_TASK_GET_LIST, this.onTaskListMsg, this)
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_TASK_UPDATE_DATA, this.onTaskUpdateDataMsg, this)
        this.refreshAllTasks();
    }

    refreshAllTasks() {
        this.refreshTaskByClass(ResTaskClass.DAILY_TASK)
        this.refreshTaskByClass(ResTaskClass.WEEKLY_TASK)
        this.refreshTaskByClass(ResTaskClass.TASK_ORBIT)
    }

    refreshTaskByClass(taskClass: ResTaskClass) {
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_TASK_GET_LIST, { task_get_list: { task_class: taskClass } })
    }

    getTaskListByClass(taskClass: ResTaskClass): csproto.cs.ICSTaskInfo[] {
        return this.taskMap.get(taskClass) || [];
    }
    getTaskByTaskId(taskId: number, taskClass?: ResTaskClass): csproto.cs.ICSTaskInfo | undefined {
        if (taskClass) {
            const taskList = this.taskMap.get(taskClass) || [];
            return taskList.find(t => t.task_id === taskId);
        }
        for (const taskList of this.taskMap.values()) {
            const task = taskList.find(t => t.task_id === taskId);
            if (task) {
                return task;
            }
        }
        return undefined;
    }

    /**
     * 获取任务描述和进度最大值
     * @param taskCfg 任务配置
     * @returns 任务描述和进度最大值
     */
    getTaskDescAndProgress(taskCfg: ResTask): { desc: string, progressMax: number } {
        let taskDesc: string = taskCfg.taskGoal.desc || "";
        let progressMax: number = 0;
        if (taskCfg.taskGoal.params.length == 1) {
            progressMax = taskCfg.taskGoal.params[0];
            taskDesc = taskDesc.replace("N", taskCfg.taskGoal.params[0].toString());
        } else if (taskCfg.taskGoal.params.length == 2) {
            //暂时不读表了，表配置还没出来,先直接替换param[0]
            taskDesc = taskDesc.replace("XX", taskCfg.taskGoal.params[0].toString());
            taskDesc = taskDesc.replace("N", taskCfg.taskGoal.params[1].toString());
            progressMax = taskCfg.taskGoal.params[1];
            // switch (taskCfg.taskGoal.goalType) {
            //     case ResGoalType.MODE_PASS_TIMES:
            //         const gameMode = MyApp.lubanTables.TbResGameMode.get(taskCfg.taskGoal.params[0]);
            //         replaceName = gameMode?.description || "";
            //         break;
            //     default:
            //         break;
            // }
        }
        return { desc: taskDesc, progressMax };
    }

    /**
     * 上报任务目标进度
     * @param goalType 任务目标类型
     * @param params 任务目标参数 任务目标参数根据goalType不同而不同 比如关卡目标参数为关卡id
     * @returns 
     */
    commitTaskProgress(goalType: ResGoalType, ...params: number[]) {
        const goldTypeTasks = [];
        this.taskMap.forEach((taskList, taskClass) => {
            const tasks = taskList.filter(t => {
                if (t.status !== csproto.comm.TASK_STATUS.TASK_STATUS_NORMAL) return false;
                const taskCfg = MyApp.lubanTables.TbResTask.get(t.task_id!);
                return taskCfg && taskCfg.taskGoal.goalType === goalType
            }) || [];
            if (tasks.length > 0) {
                goldTypeTasks.push(...tasks);
            }
        })
        if (goldTypeTasks.length === 0) return;
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_TASK_GOAL_UPDATE, {
            task_goal_update: {
                goal_type: goalType,
                values: params,
            }
        })
    }

    private onTaskListMsg(msg: csproto.cs.IS2CMsg) {
        const taskList = msg.body?.task_get_list?.task_list || [];
        const taskClass = msg.body?.task_get_list?.task_class;
        if (taskClass) {
            this.taskMap.set(taskClass, taskList);
        }
        EventMgr.emit(DataEvent.TaskRefresh, taskClass)
    }

    private onTaskUpdateDataMsg(msg: csproto.cs.IS2CMsg) {
        const taskInfo = msg.body?.task_update_data?.task_info;
        const taskClass = msg.body?.task_update_data?.task_class;
        if (!taskInfo || !taskInfo.task_id || !taskClass) {
            logError("Task", `task update data task_id is undefined`)
            return
        }
        const taskList = this.taskMap.get(taskClass) || [];
        const taskIndex = taskList.findIndex(t => t.task_id === taskInfo?.task_id);
        if (taskIndex !== -1) {
            taskList[taskIndex] = taskInfo;
        } else {
            taskList.push(taskInfo);
        }
        EventMgr.emit(DataEvent.TaskRefresh, taskClass)
    }

    // 任务奖励
    private onGetTaskRewardMsg(msg: csproto.cs.IS2CMsg) {
        const rewardList = msg.body?.task_get_reward?.reward_list || []
        // if (taskId) {
        //     const taskCfg = MyApp.lubanTables.TbResTask.get();
        //     if (taskCfg) {
        //         this.taskMap.set(taskCfg.taskClass, t);
        //     }
        // }
    }

    // 全任务信息
    private onGetTaskInfoMsg(msg: csproto.cs.IS2CMsg) {
        const taskList = msg.body?.task_get_info?.task_list || [];
        taskList.forEach(t => {
            const taskCfg = MyApp.lubanTables.TbResTask.get(t.task_id!)
            if (!taskCfg) {
                logError("Task", `task id ${t.task_id} not found`)
                return
            }
            let taskList = this.taskMap.get(taskCfg.taskClass) || [];
            taskList.push(t);
            this.taskMap.set(taskCfg.taskClass, taskList);
        })
    }



    update(): void {
    }
}
