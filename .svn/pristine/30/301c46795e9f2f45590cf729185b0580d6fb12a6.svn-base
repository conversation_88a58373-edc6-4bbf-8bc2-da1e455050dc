'use strict';

const { updatePropByDump, disconnectGroup } = require('./../../prop');
import path from "path";

type Selector<$> = { $: Record<keyof $, any | null> }
type PanelThis = Selector<typeof $> & { dump: any };

export const template = `
<div class="component-container"></div>

<ui-prop>
    <ui-label slot="label">请从下拉框中选择:</ui-label>
    <ui-select slot="content" class="prefab-dropdown"></ui-select>
</ui-prop>
<ui-prop>
    <ui-button class="btn-preview" style="display: none;">添加到场景</ui-button>
    <ui-button class="btn-new">新建emitter</ui-button>
    <ui-button class="btn-copy" style="display: none;">从选择复制新emitter</ui-button>
    <ui-button class="btn-reset" style="display: none;">重置预览</ui-button>
</ui-prop>
`;

export const $ = {
    componentContainer: '.component-container',
    prefabDropdown: '.prefab-dropdown',
    btnPreview: '.btn-preview',
    btnNew: '.btn-new',
    btnCopy: '.btn-copy',
    btnReset: '.btn-reset',
};

const emitterDir = 'db://assets/resources/game/prefabs/emitter/';

interface PrefabInfo {
    name: string;
    path: string;
    uuid: string;
}

let prefabList: PrefabInfo[] = [];
let selectedPrefab: PrefabInfo | null = null;

async function loadPrefabList(): Promise<PrefabInfo[]> {
    const pattern = `${emitterDir}**/*.prefab`;
    try {
        // @ts-ignore
        const res: any = await Editor.Message.request('asset-db', 'query-assets', { pattern });
        const arr: any[] = Array.isArray(res) ? res : (Array.isArray(res?.[0]) ? res[0] : []);
        const prefabs = arr
            .filter((a: any) => a && !a.isDirectory && a.name.endsWith('.prefab'))
            .map((a: any) => ({
                name: String(a.name || '').replace(/\.prefab$/i, ''),
                path: a.path || '',
                uuid: a.uuid || ''
            }))
            .filter(p => p.name)
            .sort((a, b) => a.name.localeCompare(b.name));
        return prefabs;
    } catch (e) {
        console.warn('loadPrefabList failed', e);
        return [];
    }
}

function updateButtonVisibility(this: PanelThis) {
    const hasSelection = selectedPrefab !== null;
    this.$.btnPreview.style.display = hasSelection ? 'inline-block' : 'none';
    this.$.btnCopy.style.display = hasSelection ? 'inline-block' : 'none';
    this.$.btnReset.style.display = this.dump?.value?.uuid ? 'inline-block' : 'none';
}

export function update(this: PanelThis, dump: any) {
    updatePropByDump(this, dump);
    this.dump = dump;

    
}

export async function ready(this: PanelThis) {
    disconnectGroup(this);
    // Load prefab list
    prefabList = await loadPrefabList();

    // Setup dropdown options
    const dropdown = this.$.prefabDropdown;
    dropdown.innerHTML = '';

    if (prefabList.length === 0) {
        const option = document.createElement('option');
        option.value = '';
        option.textContent = '(无可用的prefab文件)';
        option.disabled = true;
        dropdown.appendChild(option);
    } else {
        // Add prefab options
        prefabList.forEach(prefab => {
            const option = document.createElement('option');
            option.value = prefab.uuid;
            option.textContent = prefab.name;
            dropdown.appendChild(option);
        });
    }

    // Handle dropdown selection change
    dropdown.addEventListener('change', () => {
        const selectedUuid = dropdown.value;
        selectedPrefab = prefabList.find(p => p.uuid === selectedUuid) || null;
        updateButtonVisibility.call(this);
    });

    // Handle preview button
    this.$.btnPreview.addEventListener('click', () => {
        if (selectedPrefab) {
            // @ts-ignore
            Editor.Message.request('scene', 'execute-scene-script', {
                name: 'emitter-editor',
                method: 'instantiatePrefab',
                args: [this.dump?.value.uuid, selectedPrefab.uuid]
            });
        }
    });

    // Handle new emitter button
    this.$.btnNew.addEventListener('click', async () => {
        console.log('Create new emitter');
        // @ts-ignore
        const dirPath = path.join(Editor.Project.path, "assets", "resources", "game", "prefabs", "emitter");
        // @ts-ignore
        const retData = await Editor.Dialog.save({
            path: dirPath,
            filters: [
                { name: 'Prefab', extensions: ['prefab'] },
            ],
        });
        if (retData.canceled || !retData.filePath) {
            return;
        }
        const name = path.relative(dirPath, retData.filePath);
        const nameWithoutExt = name.replace(/\.prefab$/i, '');
        console.log('New emitter name:', name);
        const filePath = `${emitterDir}${name}`;

        try {
            // @ts-ignore
            Editor.Message.request('scene', 'execute-scene-script', {
                name: 'emitter-editor',
                method: 'createNewEmitter',
                args: [nameWithoutExt, filePath]
            });

            // Refresh prefab list
            prefabList = await loadPrefabList();
        } catch (e) {
            console.error('Failed to create new emitter:', e);
        }
    });

    // Handle copy emitter button
    this.$.btnCopy.addEventListener('click', async () => {
        if (!selectedPrefab) return;

        const sourceUrl = selectedPrefab.path;
        const targetUrl = sourceUrl + '_copy';
        const nameWithoutExt = selectedPrefab.name + '_copy';

        console.log('Copy emitter from ', sourceUrl, ' to ', targetUrl);

        try {
            // @ts-ignore
            await Editor.Message.request('asset-db', 'copy-asset', sourceUrl + '.prefab', targetUrl + '.prefab');
            // Refresh prefab list
            prefabList = await loadPrefabList();
            selectedPrefab = prefabList.find(p => p.name === nameWithoutExt) || null;
            if (selectedPrefab) {
                // @ts-ignore
                Editor.Message.request('scene', 'execute-scene-script', {
                    name: 'emitter-editor',
                    method: 'instantiatePrefab',
                    args: [this.dump?.value.uuid, selectedPrefab.uuid]
                });
            }
        } catch (e) {
            console.error('Failed to copy emitter:', e);
        }
    });

    // Handle reset preview button
    this.$.btnReset.addEventListener('click', () => {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'reset',
            args: [this.dump?.value.uuid]
        });
    });

    // Initial button visibility update
    updateButtonVisibility.call(this);
}