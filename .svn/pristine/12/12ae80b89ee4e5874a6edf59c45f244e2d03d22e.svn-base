import { _decorator, CCFloat, Component, JsonAsset, CCBoolean, CCString, assetManager, log, native } from 'cc';
const { ccclass, property, executeInEditMode } = _decorator;
import { LevelData } from 'db://assets/bundles/common/script/leveldata/leveldata';
import { LevelEditorBaseUI } from './LevelEditorBaseUI';

@ccclass('LevelEditorUI')
@executeInEditMode()
export class LevelEditorUI extends Component {

    @property({
        type:CCFloat,
        range: [0, 1, 0.01],
        slide: true,
        visible: true,
    })
    progress: number = 0;

    private _levelPrefab: JsonAsset | null = null;
    private config: any = {}

    @property({
        type:JsonAsset,
    })
    public set levelPrefab(value: JsonAsset | null) {
        if (this._levelPrefab?.uuid != value?.uuid) {
            if (this._levelPrefab != null) {
                this.save = true;
            }
            var levelData = LevelData.fromJSON(value?.json);
            this.baseCom!.initByLevelData(levelData);
            this._levelPrefab = value;
            this.setConfig("levelPrefabUUID", value?.uuid ?? "")
        }
    }
    public get levelPrefab() : JsonAsset|null{
        return this._levelPrefab;
    }

    @property(CCString) 
    public set levelPrefabUUID(value: string) {
        log("LevelEditorUI set levelPrefabUUID", value)
        if (value == null || value == "") {
            this.levelPrefab = null;
            return;
        }
        assetManager.loadAny({uuid:value}, (err, asset) => {
            if (err) {
                log("LevelEditor set levelPrefabUUID but load err:", err);
                return
            }
            this.levelPrefab = asset;
        });
    }
    public get levelPrefabUUID() : string|undefined {
        return this._levelPrefab?.uuid;
    }

    @property(CCBoolean)
    public get save(){
        return false;
    }
    public set save(value) {
        if (!value) {
            return;
        }
        if (this._levelPrefab == null) {
            return;
        }
        log(`json:[${JSON.stringify(this._levelPrefab.json)}]`);
        var data = this.genLevelData();
        //@ts-ignore
        Editor.Message.send('asset-db', 'save-asset', this._levelPrefab.uuid, JSON.stringify(data, null, 2));
        log("LevelEditorUI save success",data);

        setTimeout(() => {
            this.reloadLevelPrefab();
        }, 100);
    }

    public _play = false
    @property(CCBoolean)
    public get play(){
        return this._play;
    }
    public set play(value) {
        this._play = !this._play;
        log("LevelEditorUI play success", this._play)
    }
    
    private baseCom:LevelEditorBaseUI|null = null;

    _levelElapsedTime: number = 0;
    public get levelElapsedTime(): number {
        return this._levelElapsedTime;
    }

    start():void {
        log("LevelUI start")
        this.baseCom = this.node.getComponent<LevelEditorBaseUI>(LevelEditorBaseUI);
        this.levelPrefab = null;
        var fs = require('fs')
        // @ts-ignore
        var data = fs.readFile(Editor.App.home + "/level-editor.json", (err, data)=>{
            if (!err) {
                this.config = JSON.parse(data);
                if (this.config.levelPrefabUUID != null) {
                    this.levelPrefabUUID = this.config.levelPrefabUUID;
                }
            }
        });


    }

    private setConfig(key:string, value:any) {
        this.config[key] = value;
        var fs = require('fs')
        // @ts-ignore
        fs.writeFile(Editor.App.home + "/level-editor.json", JSON.stringify(this.config), (err) => {
            if (err) {
                log("LevelEditorUI setConfig err:", err);
            }
        });
    }
    
    private _lastTime = new Date().getTime();
    update(dt: number) : void {
        var d = new Date().getTime();
        var dt1 = d - this._lastTime;
        this._lastTime = d;
        if (this._levelPrefab && this._play && this.baseCom!.totalTime > 0) {
            this.progress += dt1/1000/(this.baseCom!.totalTime / 1000);
            if (this.progress >= 1) {
                this.progress = 1;
                this._play = false;
            }
            // @ts-ignore
            const compIndex = this.node.components.indexOf(this);
            if (compIndex != -1) { 
                const propertyPath = `__comps__.${compIndex}.progress`;
                // @ts-ignore
                Editor.Message.request('scene', 'set-property', {
                    uuid: this.node.uuid,
                    path: propertyPath,
                    dump: {
                        type: 'CCFloat',
                        value: this.progress,
                    },
                });
            }
        }
        this.tick();
    }
    private tick():void {
        if (this._levelPrefab == null) {
            return;
        }
        this.baseCom!.tick(this.progress);

        if (this._play) {
            this.baseCom!.playLevel(true,this.progress);
        } else {
            this.baseCom!.playLevel(false,this.progress);
        }
    }

    private genLevelData() : LevelData {
        var data = new LevelData();
        this.baseCom!.fillLevelData(data);
        return data;
    }

    protected onDestroy(): void {
        this.save = true;
    }

    private reloadLevelPrefab(): void {
        if (this._levelPrefab && this._levelPrefab.uuid) {
            const uuid = this._levelPrefab.uuid;
            // 清空当前引用
            this._levelPrefab = null;
            // 重新加载资源
            assetManager.loadAny({uuid: uuid}, (err, asset: JsonAsset) => {
                if (err) {
                    log("LevelEditorUI reloadLevelPrefab error:", err);
                    return;
                }
                // 重新设置levelPrefab，这将触发initByLevelData
                this.levelPrefab = asset;
                log("LevelEditorUI reloadLevelPrefab success");
            });
        }
    }
}
