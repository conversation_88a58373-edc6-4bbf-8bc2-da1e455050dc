import { _decorator, RichText, tween, UITransform, v3 } from 'cc';
import { BaseUI, UILayer } from "db://assets/scripts/core/base/UIMgr";
import { BundleName } from '../../const/BundleConst';

const { ccclass, property } = _decorator;

@ccclass('MarqueeUI')
export class MarqueeUI extends BaseUI {
    @property(RichText)
    info: RichText | null = null;
    public toClose: Function | undefined = undefined;
    public static getUrl(): string { return "prefab/ui/MarqueeUI"; }
    public static getLayer(): UILayer { return UILayer.Top }
    public static getBundleName(): string { return BundleName.Home }

    protected onLoad(): void {

    }
    async onShow(content: string): Promise<void> {
        this.info!.string = content;
        let textWidth = this.info!.node.getComponent(UITransform)!.contentSize.width;
        const scrollSpeed = 100; // 滚动速度（像素/秒）
        const fixedOffset = 350; // 固定偏移量
        const duration = (fixedOffset + textWidth) / scrollSpeed;
        tween(this.info!.node)
            .to(duration, { position: v3(-350 - Math.ceil(textWidth), 0, 0) })
            .call(() => {
                this.executeClose();
            })
            .start();
    }
    async onHide(): Promise<void> {
    }
    close() {
        this.executeClose();
    }
    async onClose(): Promise<void> {
        this.executeClose();
    }
    private executeClose() {
        if (this.toClose) {
            this.toClose();
            this.toClose = undefined;
        }
    }
}
