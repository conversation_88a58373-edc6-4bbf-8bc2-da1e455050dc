import { _decorator, assetManager, misc, Prefab, Quat, Vec3, Enum, CCString } from 'cc';
import { EDITOR } from 'cc/env';
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { ResEmitter } from 'db://assets/bundles/common/script/autogen/luban/schema';
import { LubanMgr } from 'db://assets/bundles/common/script/luban/LubanMgr';
import Entity from 'db://assets/bundles/common/script/game/ui/base/Entity';
import { BulletData } from '../data/bullet/BulletData';
import { EmitterData } from '../data/bullet/EmitterData';
import { Bullet, BulletProperty } from './Bullet';
import { BulletSystem } from './BulletSystem';
import { EventGroup, EventGroupContext } from "./EventGroup";
import { ObjectPool } from './ObjectPool';
import { Property, PropertyContainerComponent } from './PropertyContainer';
import type PlaneBase from '../ui/plane/PlaneBase';
import { ExpressionValue } from '../data/bullet/ExpressionValue';

// // 这个import仅用于编辑功能
// import { BulletEnum } from 'db://assets/editor/enum-gen/BulletEnum'

const { ccclass, executeInEditMode, property, disallowMultiple, menu } = _decorator;
const { degreesToRadians, radiansToDegrees } = misc;

/**
 * 发射器状态变换
 * [None] -> <InitialDelay> -> [Prewarm] -> <PrewarmDuration> -> [Emitting]
 *                                 ^                                 |
 *                                 |                                 v
 *                                 |                           <EmitDuration>
 *                                 |                                 |
 *                                 |                              isLoop? ---no---> [Completed] 
 *                                 |                                 |
 *                                 |                                 |yes
 *                                 |                                 |
 *                                 |                                 v
 *                                 -------<LoopInterval>------[LoopEndReached]
 */
export enum eEmitterStatus {
    None, Prewarm, Emitting, LoopEndReached, Completed
}

// 用枚举定义属性
export enum eEmitterProp {
    IsActive = 1, IsOnlyInScreen, IsPreWarm, IsLoop,
    InitialDelay, PrewarmDuration, EmitDuration, EmitInterval, EmitPower, LoopInterval,
    PerEmitCount, PerEmitInterval, PerEmitOffsetX,
    Angle, Count, Arc, Radius,
    ElapsedTime,
}

/**
 * 说明：
 * 因为发射器属性可能需要从emitterData里的公式计算，如: randi(0,360);
 * 但同时也可能被事件组修改，参考: EmitterEventActions
 * 事件组的优先级要高于emitterData的公式计算, 因此, 如果一个属性带了ePropMask.EventGroup标记, 后续ReEval时就直接跳过
 */
export enum ePropMask {
    ReEval = 1 << 0, // 需要重新从公式计算
    EventGroup = 1 << 1, // 需要被事件组修改
}

export type onBulletCreatedDelegate = (bullet: Bullet) => void;
export type onEmitterStatusChangedDelegate = (emitter: Emitter, oldStatus: eEmitterStatus, newStatus: eEmitterStatus) => void;

/**
 * 目前Emitter,EventGroup,BulletSystem的状态管理还是比较混乱
 * 需要看下怎么调整，使代码不论是运行时，还是编辑器下，都更加健壮
 * - young
 */
@ccclass('Emitter')
// @inspector('editor/inspector/components/emitter')
@menu('子弹系统/发射器')
@executeInEditMode(true)
@disallowMultiple(true)
export class Emitter extends PropertyContainerComponent<eEmitterProp> {

    static kBulletNameInEditor: string = "_bullet_";

    @property({displayName: '名称', editorOnly: true})
    emitterName: string = '';                // 备注(策划用)

    // @property({ type: Enum(BulletEnum), displayName: "子弹ID" })
    // readonly bulletID: number = 0;
    @property({ type: EmitterData, displayName: "发射器属性" })
    readonly emitterData: EmitterData = new EmitterData();

    @property({ type: BulletData, displayName: "子弹属性" })
    readonly bulletData: BulletData = new BulletData();

    // callbacks
    onBulletCreatedCallback: onBulletCreatedDelegate | null = null;
    onEmitterStatusChangedCallback: onEmitterStatusChangedDelegate | null = null;

    // 以下属性缓存为了性能优化(减少this.getProperty<T>的调用)
    public isActive!: Property<boolean>;
    public isOnlyInScreen!: Property<boolean>;
    public isPreWarm!: Property<boolean>;
    public isLoop!: Property<boolean>;
    public initialDelay!: Property<number>;
    public preWarmDuration!: Property<number>;
    public emitDuration!: Property<number>;
    public emitInterval!: Property<number>;
    public emitPower!: Property<number>;
    public loopInterval!: Property<number>;
    public perEmitCount!: Property<number>;
    public perEmitInterval!: Property<number>;
    public perEmitOffsetX!: Property<number>;
    public angle!: Property<number>;
    public count!: Property<number>;
    public arc!: Property<number>;
    public radius!: Property<number>;
    public elapsedTime!: Property<number>; 
    // 以下用于事件组修改子弹的属性，（不直接修改bulletData)
    public bulletProp!: BulletProperty;

    // 发射器自己的事件组
    public eventGroups: EventGroup[] = [];

    // 私有变量
    protected _emitterId: number = 0;
    protected _status: eEmitterStatus = eEmitterStatus.None;
    protected _statusElapsedTime: number = 0;
    protected _totalElapsedTime: number = 0;
    protected _isEmitting: boolean = false;
    protected _nextEmitTime: number = 0;

    protected _bulletPrefab: Prefab | null = null;
    protected _prewarmEffectPrefab: Prefab | null = null;
    protected _emitEffectPrefab: Prefab | null = null;
    protected _entity: PlaneBase | null = null;
    protected _emitterConfig: ResEmitter | undefined = undefined;
    
    // Per-emit timing tracking
    protected _perEmitBulletQueue: Array<{ index: number, perEmitIndex: number, targetTime: number }> = [];

    get isEmitting(): boolean { return this._isEmitting; }
    get status(): eEmitterStatus { return this._status; }
    get statusElapsedTime(): number { return this._statusElapsedTime; }
    get totalElapsedTime(): number { return this._totalElapsedTime; }
    get emitterId(): number { return this._emitterId; }
    get config(): ResEmitter | undefined { return this._emitterConfig; }
    set emitterId(id: number) {
        this._emitterId = id;
        this.loadConfigByID(id);
    }

    protected onLoad(): void {
        this.createProperties();
        this.createEventGroups();

        // 更新属性
        this.resetProperties();
    }

    //#region "Editor Region"
    public onLostFocusInEditor(): void {
        this.updatePropertiesInEditor();
        this.createEventGroups();
    }

    public updatePropertiesInEditor() {
        if (!this.emitterData) return;

        this.isActive.value = true;
        this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;
        this.isPreWarm.value = this.emitterData.isPreWarm;
        this.isLoop.value = this.emitterData.isLoop;

        this.initialDelay.value = this.emitterData.initialDelay.eval(null, true);
        this.preWarmDuration.value = this.emitterData.preWarmDuration.eval(null, true);
        this.emitDuration.value = this.emitterData.emitDuration.eval(null, true);
        this.emitInterval.value = this.emitterData.emitInterval.eval(null, true);
        this.emitPower.value = this.emitterData.emitPower.eval(null, true);
        this.loopInterval.value = this.emitterData.loopInterval.eval(null, true);
        this.perEmitCount.value = this.emitterData.perEmitCount.eval(null, true);
        this.perEmitInterval.value = this.emitterData.perEmitInterval.eval(null, true);
        this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval(null, true);
        this.angle.value = this.emitterData.angle.eval(null, true);
        this.count.value = this.emitterData.count.eval(null, true);
        this.arc.value = this.emitterData.arc.eval(null, true);
        this.radius.value = this.emitterData.radius.eval(null, true);

        this.notifyAll(true);
    }
    //#endregion "Editor Region"

    // 通过这个接口来启用和禁用发射器
    public setIsActive(active: boolean) {
        this.isActive.value = active;
        this.isActive.notify();
    }

    // 这个接口清理发射器的状态，全部从头开始
    public reset() {
        this._isEmitting = false;
        this.changeStatus(eEmitterStatus.None);
        this.resetProperties();
        if (this.eventGroups.length > 0) {
            this.eventGroups.forEach(group => group.reset());
        }
    }

    public setEntity(entity: PlaneBase) {
        this._entity = entity;
    }

    public getEntity(): PlaneBase | null {
        return this._entity;
    }

    protected createProperties() {
        this.clear();

        this.isActive = this.addProperty(eEmitterProp.IsActive, false);
        this.elapsedTime = this.addProperty(eEmitterProp.ElapsedTime, 0);
        this.isOnlyInScreen = this.addProperty(eEmitterProp.IsOnlyInScreen, true);
        this.isPreWarm = this.addProperty(eEmitterProp.IsPreWarm, true);
        this.isLoop = this.addProperty(eEmitterProp.IsLoop, true);

        this.initialDelay = this.addProperty(eEmitterProp.InitialDelay, 0);
        this.preWarmDuration = this.addProperty(eEmitterProp.PrewarmDuration, 0);
        this.emitDuration = this.addProperty(eEmitterProp.EmitDuration, 0);
        this.emitInterval = this.addProperty(eEmitterProp.EmitInterval, 0);
        this.emitPower = this.addProperty(eEmitterProp.EmitPower, 1);
        this.loopInterval = this.addProperty(eEmitterProp.LoopInterval, 0);
        this.perEmitCount = this.addProperty(eEmitterProp.PerEmitCount, 1);
        this.perEmitInterval = this.addProperty(eEmitterProp.PerEmitInterval, 0);
        this.perEmitOffsetX = this.addProperty(eEmitterProp.PerEmitOffsetX, 0);
        this.angle = this.addProperty(eEmitterProp.Angle, 0);
        this.count = this.addProperty(eEmitterProp.Count, 1);
        this.arc = this.addProperty(eEmitterProp.Arc, 0);
        this.radius = this.addProperty(eEmitterProp.Radius, 0);

        // 子弹相关属性
        this.bulletProp = new BulletProperty();

        this.isActive.on((value) => {
            if (value) {
                BulletSystem.onCreateEmitter(this);
            } else {
                this.changeStatus(eEmitterStatus.None);
                BulletSystem.onDestroyEmitter(this);
            }
        });
    }

    protected createEventGroups() {
        if (!this.emitterData || this.emitterData.eventGroupData.length <= 0) return;

        this.eventGroups = [];
        let ctx = new EventGroupContext();
        ctx.emitter = this;
        ctx.playerPlane = BulletSystem.playerPlane;
        for (const eventGroup of this.emitterData.eventGroupData) {
            BulletSystem.createEmitterEventGroup(ctx, eventGroup);
        }
    }

    // reset properties from emitterData
    protected resetProperties() {
        if (!this.emitterData) return;

        this.isActive.value = false;
        this.elapsedTime.value = 0;
        this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;
        this.isPreWarm.value = this.emitterData.isPreWarm;
        this.isLoop.value = this.emitterData.isLoop;

        this.initialDelay.value = this.emitterData.initialDelay.eval(null, true);
        this.preWarmDuration.value = this.emitterData.preWarmDuration.eval(null, true);
        this.emitDuration.value = this.emitterData.emitDuration.eval(null, true);
        this.emitInterval.value = this.emitterData.emitInterval.eval(null, true);
        this.emitPower.value = this.emitterData.emitPower.eval(null, true);
        this.loopInterval.value = this.emitterData.loopInterval.eval(null, true);
        this.perEmitCount.value = this.emitterData.perEmitCount.eval(null, true);
        this.perEmitInterval.value = this.emitterData.perEmitInterval.eval(null, true);
        this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval(null, true);
        this.angle.value = this.emitterData.angle.eval(null, true);
        this.count.value = this.emitterData.count.eval(null, true);
        this.arc.value = this.emitterData.arc.eval(null, true);
        this.radius.value = this.emitterData.radius.eval(null, true);

        this.bulletProp.resetFromData(this.bulletData);

        this.notifyAll(true);
    }

    protected evalProperty(prop: Property<number>, value: ExpressionValue) {
        // 为什么这样写，而不是直接：prop.setValue(value.eval(), ePropMask.ReEval);
        // 是为了避免非必要的eval()调用
        if (prop.canWrite(ePropMask.ReEval) && !value.isFixedValue) {
            prop.value = value.eval();
        }
    }

    /**
     * public apis
     */
    changeStatus(status: eEmitterStatus) {
        if (this._status === status) return;

        const oldStatus = this._status;
        this._status = status;
        this._statusElapsedTime = 0;
        this._nextEmitTime = 0;
        // Clear per-emit queue when changing status
        this._perEmitBulletQueue = [];

        if (status === eEmitterStatus.Prewarm) {
            this.elapsedTime.value = 0;
            // emitInterval可能是一个序列，每次尽量从第一个序列从头开始
            this.emitterData.emitInterval.reset();
        }
        
        if (status === eEmitterStatus.None) {
            if (this.eventGroups.length > 0) {
                this.eventGroups.forEach(group => group.tryStop());
            }
        }
        else {
            // 所有其他状态，都尝试开始执行eventGroup
            if (this.eventGroups.length > 0) {
                this.eventGroups.forEach(group => group.tryStart());
            }
        }

        if (this.onEmitterStatusChangedCallback != null) {
            this.onEmitterStatusChangedCallback(this, oldStatus, status);
        }
    }

    protected scheduleNextEmit() {
        // re-eval
        this.evalProperty(this.emitInterval, this.emitterData.emitInterval);

        // Schedule the next emit after emitInterval
        this._nextEmitTime = this._statusElapsedTime + this.emitInterval.value;
        // console.log('scheduleNextEmit: ', this._nextEmitTime, ', ', this._statusElapsedTime, ', ', this.emitInterval.value);
    }

    protected startEmitting() {
        this._isEmitting = true;
        // 下一次update时触发发射
        // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射
        // this.tryEmit();
        // if (this.perEmitInterval.value <= 0) {
        //     this.scheduleNextEmit();
        // }
        // else {
        //     // 开始这一波
        //     this._nextEmitTime = this._statusElapsedTime + 10000000;
        // }
        // // reset status time 
        // this._statusElapsedTime = 0;
    }

    protected stopEmitting() {
        this._isEmitting = false;
        // Clear the per-emit bullet queue
        this._perEmitBulletQueue = [];
        this.unscheduleAllCallbacks();
    }

    protected canEmit(): boolean {
        // 检查是否可以触发发射
        // Override this method in subclasses to add custom trigger conditions
        return true;
    }

    protected emit(): void {
        // re-eval
        this.evalProperty(this.count, this.emitterData.count);
        this.evalProperty(this.arc, this.emitterData.arc);
        this.evalProperty(this.radius, this.emitterData.radius);
        this.evalProperty(this.perEmitCount, this.emitterData.perEmitCount);
        
        if (this.perEmitInterval.value > 0) {
            // Generate bullets in time-sorted order directly
            for (let j = 0; j < this.perEmitCount.value; j++) {
                this.evalProperty(this.perEmitInterval, this.emitterData.perEmitInterval);
                const targetTime = this._statusElapsedTime + (this.perEmitInterval.value * j);
                for (let i = 0; i < this.count.value; i++) {
                    this._perEmitBulletQueue.push({
                        index: i,
                        perEmitIndex: j,
                        targetTime: targetTime
                    });
                }
            }
        }
        else {
            // Immediate emission - no timing needed
            for (let i = 0; i < this.count.value; i++) {
                for (let j = 0; j < this.perEmitCount.value; j++) {
                    this.emitSingle(i, j);
                }
            }
        }
    }

    protected processPerEmitQueue(): void {
        // Process bullets that should be emitted based on current time
        while (this._perEmitBulletQueue.length > 0) {
            const nextBullet = this._perEmitBulletQueue[0];

            // Check if it's time to emit this bullet
            if (this._statusElapsedTime >= nextBullet.targetTime) {
                // Remove from queue and emit
                this._perEmitBulletQueue.shift();
                this.emitSingle(nextBullet.index, nextBullet.perEmitIndex);
            } else {
                // No more bullets ready to emit yet
                break;
            }
        }
    }

    protected tryEmit(): boolean {
        if (this.canEmit()) {
            this.emit();
            return true;
        }
        return false;
    }

    protected emitSingle(index: number, perEmitIndex: number) {
        const direction = this.getSpawnDirection(index);
        const position = this.getSpawnPosition(index, perEmitIndex);
        this.createBullet(direction, position);
    }

    /**
     * Calculate the direction for a bullet at the given index
     * @param index The index of the bullet (0 to count-1)
     * @returns Direction vector {x, y}
     */
    getSpawnDirection(index: number): { x: number, y: number } {
        // 期望如果配了公式，每次发射方向都随机下
        this.evalProperty(this.angle, this.emitterData.angle);
        // 计算发射方向
        const angleOffset = this.count.value > 1 ? (this.arc.value / (this.count.value - 1)) * index - this.arc.value / 2 : 0;
        const radian = degreesToRadians(this.angle.value + angleOffset);

        return {
            x: Math.cos(radian),
            y: Math.sin(radian)
        };
    }
    
    /**
     * Get the spawn position for a bullet at the given index
     * odd number to the right, even number to the left
     * @param index The index of the bullet (0 to count-1)
     * @returns Position offset from emitter center
     */
    getSpawnPosition(index: number, perEmitIndex: number): { x: number, y: number } {
        // add perEmitOffsetX by perEmitIndex, with the rules:
        // by the following order:0, 1; 2, 0, 1; 2, 0, 1, 3;
        const getEmitOffsetX = (perEmitIndex: number, perEmitCount: number, perEmitOffsetX: number) => {
            if (perEmitCount <= 1 || perEmitOffsetX === 0) return 0;
            const interval = perEmitOffsetX / (perEmitCount - 1);
            //const middle = 0;

            if (perEmitCount % 2 === 1) {
                // 奇数情况
                if (perEmitIndex === 0) return 0;
                if (perEmitIndex % 2 === 0) {
                    // 偶数索引在左边
                    const stepsFromMiddle = Math.floor(perEmitIndex / 2);
                    return -stepsFromMiddle * interval;
                }
                else {
                    // 奇数索引在右边
                    const stepsFromMiddle = Math.ceil(perEmitIndex / 2);
                    return stepsFromMiddle * interval;
                }
            } else {
                // 偶数情况
                if (perEmitIndex === 0) return -interval / 2;
                if (perEmitIndex % 2 === 0) {
                    // 偶数索引在左边
                    const stepsFromMiddle = Math.floor(perEmitIndex / 2);
                    return -interval / 2 - stepsFromMiddle * interval;
                }
                else {
                    // 奇数索引在右边
                    const stepsFromMiddle = Math.floor(perEmitIndex / 2);
                    return interval / 2 + stepsFromMiddle * interval;
                }
            }
        }

        this.evalProperty(this.perEmitOffsetX, this.emitterData.perEmitOffsetX);
        const perEmitOffsetX = getEmitOffsetX(perEmitIndex, this.perEmitCount.value, this.perEmitOffsetX.value);
        if (this.radius.value <= 0) {
            return { x: perEmitOffsetX, y: 0 };
        }

        const direction = this.getSpawnDirection(index);
        // 计算垂直于发射方向的向量（逆时针90度旋转）
        const perpendicular = { x: -direction.y, y: direction.x };
        if (this.radius.value <= 0) {
            return {
                x: perpendicular.x * perEmitOffsetX,
                y: perpendicular.y * perEmitOffsetX
            };
        }

        return {
            x: direction.x * this.radius.value + perpendicular.x * perEmitOffsetX,
            y: direction.y * this.radius.value + perpendicular.y * perEmitOffsetX
        };
    }

    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }): void {
        if (!this._bulletPrefab) {
            this._bulletPrefab = this.bulletData.prefab;
            if (!this._bulletPrefab) {
                if (EDITOR) {
                    this.createBulletInEditor(direction, position);
                }
                return;
            }
        }

        const bullet = this.instantiateBullet();
        if (!bullet) return;

        BulletSystem.onCreateBullet(this, bullet);
        // Set bullet position relative to emitter
        const emitterPos = this.node.getWorldPosition();
        bullet.node.setWorldPosition(
            emitterPos.x + position.x,
            emitterPos.y + position.y,
            emitterPos.z
        );
        bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));
        bullet.prop.speed.value *= this.emitPower.value;
        // 为什么需要在这里resetEventGroups?
        // 因为EventGroups的条件初始化依赖上面先初始化子弹的属性
        bullet.onReady();

        if (this.onBulletCreatedCallback != null) {
            this.onBulletCreatedCallback(bullet);
        }
    }

    protected async createBulletInEditor(direction: { x: number, y: number }, position: { x: number, y: number }) {
        // use a default bullet prefab
        const prefabPath = 'db://assets/resources/game/prefabs/Bullet_New.prefab';
        // @ts-ignore
        Editor.Message.request('asset-db', 'query-uuid', prefabPath)
            .then((uuid: string) => {
                assetManager.loadAny({ uuid: uuid }, (err, prefab) => {
                    if (err) {
                        console.error(err);
                        return;
                    }
                    this._bulletPrefab = prefab;
                    const bullet = this.instantiateBullet();
                    if (!bullet) return;

                    BulletSystem.onCreateBullet(this, bullet);
                    // Set bullet position relative to emitter
                    const emitterPos = this.node.getWorldPosition();
                    bullet.node.setWorldPosition(
                        emitterPos.x + position.x,
                        emitterPos.y + position.y,
                        emitterPos.z
                    );
                    bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));
                    bullet.prop.speed.value *= this.emitPower.value;
                    bullet.onReady();
                });
            });
    }

    protected instantiateBullet(): Bullet | null {
        const bulletNode = ObjectPool.getNode(BulletSystem.bulletParent, this._bulletPrefab!);
        if (!bulletNode) {
            console.error("Emitter: Failed to instantiate bullet prefab");
            return null;
        }

        // Get the bullet component
        const bullet = bulletNode.getComponent(Bullet);
        if (!bullet) {
            console.error("Emitter: Bullet prefab does not have Bullet component");
            bulletNode.destroy();
            return null;
        }

        if (EDITOR) {
            bulletNode.name = Emitter.kBulletNameInEditor;
        }

        return bullet;
    }

    protected loadConfigByID(emitterId: number) {
        if (emitterId > 0 && MyApp.GetInstance() && MyApp.lubanMgr) {
            this._emitterConfig = MyApp.lubanTables.TbResEmitter.get(emitterId);
            // if (this._bulletConfig) {
            //     MyApp.resMgr.load(this._bulletConfig.prefab, Prefab, (error: any, prefab: Prefab) => {
            //         if (error) {
            //             console.error("Emitter load bullet prefab err", error);
            //             return;
            //         }
            //         this._bulletPrefab = prefab;
            //     });
            // }
        }
        // else if (EDITOR) {
        //     let lubanMgr = new LubanMgr();
        //     lubanMgr.initInEditor().then(() => {
        //         this._bulletConfig = lubanMgr.table.TbResBullet.get(bulletID);
        //         if (this._bulletConfig) {
        //             const prefabPath = 'db://assets/resources/' + this._bulletConfig.prefab + '.prefab';
        //             // @ts-ignore
        //             Editor.Message.request('asset-db', 'query-uuid', prefabPath)
        //                 .then((uuid: string) => {
        //                     assetManager.loadAny({ uuid: uuid }, (err, prefab) => {
        //                         if (err) {
        //                             console.error(err);
        //                             return;
        //                         }
        //                         this._bulletPrefab = prefab;
        //                     });
        //                 });
        //         }
        //      });
        // }
    }

    playEffect(prefab: Prefab, position: Vec3, rotation: Quat, duration: number) {
        if (!prefab) return;

        const effectNode = ObjectPool.getNode(this.node, prefab);
        if (!effectNode) return;

        effectNode.setWorldPosition(position);
        effectNode.setWorldRotation(rotation);
        // Play the effect and destroy it after duration
        // effectNode.getComponent(ParticleSystem)?.play();
        this.scheduleOnce(() => {
            ObjectPool.returnNode(effectNode);
        }, duration);
    }

    /**
     * Return true if this.node is in screen
     */
    protected isInScreen(): boolean {
        // TODO: Get mainCamera.containsNode(this.node)
        return true;
    }

    public tick(deltaTime: number): void {
        if (!this.isActive || !this.isActive.value) {
            return;
        }

        switch (this._status) {
            case eEmitterStatus.None:
                this.updateStatusNone();
                break;
            case eEmitterStatus.Prewarm:
                this.updateStatusPrewarm();
                break;
            case eEmitterStatus.Emitting:
                this.updateStatusEmitting();
                break;
            case eEmitterStatus.LoopEndReached:
                this.updateStatusLoopEndReached();
                break;
            case eEmitterStatus.Completed:
                this.updateStatusCompleted();
                break;
            default:
                break;
        }

        this.elapsedTime.value += deltaTime;
        this._statusElapsedTime += deltaTime;
        this._totalElapsedTime += deltaTime;

        this.notifyAll();
    }

    protected updateStatusNone() {
        if (this._statusElapsedTime >= this.initialDelay.value) {
            this.changeStatus(eEmitterStatus.Prewarm);
        }
    }

    protected updateStatusPrewarm() {
        if (!this.isPreWarm.value)
            this.changeStatus(eEmitterStatus.Emitting);
        else {
            if (this._statusElapsedTime >= this.preWarmDuration.value) {
                this.changeStatus(eEmitterStatus.Emitting);
            }
        }
    }

    protected updateStatusEmitting() {
        if (this._statusElapsedTime > this.emitDuration.value) {
            this.stopEmitting();
            if (this.isLoop.value)
                this.changeStatus(eEmitterStatus.LoopEndReached);
            else
                this.changeStatus(eEmitterStatus.Completed);
            return;
        }

        // Start emitting if not already started
        if (!this._isEmitting) {
            this.startEmitting();
        }
        else if (this._statusElapsedTime >= this._nextEmitTime) {
            this.tryEmit();
            if (this.perEmitInterval.value <= 0) {
                this.scheduleNextEmit();
            }
            else {
                // 开始这一波
                this._nextEmitTime = this._statusElapsedTime + 10000000;
            }
        }

        let wasEmitting = this._perEmitBulletQueue.length > 0;
        // Process per-emit bullet queue based on precise timing
        this.processPerEmitQueue();
        if (wasEmitting && this._perEmitBulletQueue.length <= 0) {
            this.scheduleNextEmit();
        }
    }

    protected updateStatusLoopEndReached() {
        if (this._statusElapsedTime >= this.loopInterval.value) {
            this.changeStatus(eEmitterStatus.Prewarm);
        }
    }

    protected updateStatusCompleted() {
        // Do nothing or cleanup if needed
        this.isActive.value = false;
        this.isActive.notify();
    }
}
