2025-9-23 11:58:03-debug: start **** info
2025-9-23 11:58:04-log: Cannot access game frame or container.
2025-9-23 11:58:04-debug: asset-db:require-engine-code (524ms)
2025-9-23 11:58:04-log: meshopt wasm decoder initialized
2025-9-23 11:58:04-log: [bullet]:bullet wasm lib loaded.
2025-9-23 11:58:04-log: [box2d]:box2d wasm lib loaded.
2025-9-23 11:58:04-log: Cocos Creator v3.8.6
2025-9-23 11:58:04-debug: [Assets Memory track]: asset-db-plugin-register: programming start:81.01MB, end 88.65MB, increase: 7.64MB
2025-9-23 11:58:04-log: Forward render pipeline initialized.
2025-9-23 11:58:05-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:224.42MB, end 227.27MB, increase: 2.84MB
2025-9-23 11:58:05-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.88MB, end 227.43MB, increase: 146.55MB
2025-9-23 11:58:05-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.17MB, end 227.92MB, increase: 147.75MB
2025-9-23 11:58:04-log: Using legacy pipeline
2025-9-23 11:58:04-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:29.64MB, end 80.15MB, increase: 50.51MB
2025-9-23 11:58:05-debug: [Assets Memory track]: asset-db-plugin-register: builder start:88.67MB, end 224.22MB, increase: 135.54MB
2025-9-23 11:58:05-debug: run package(native) handler(enable) start
2025-9-23 11:58:05-debug: run package(native) handler(enable) success!
2025-9-23 11:58:05-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-23 11:58:05-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-23 11:58:05-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-23 11:58:05-debug: run package(taobao-mini-game) handler(enable) start
2025-9-23 11:58:05-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-23 11:58:05-debug: run package(vivo-mini-game) handler(enable) start
2025-9-23 11:58:05-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-23 11:58:05-debug: run package(web-desktop) handler(enable) start
2025-9-23 11:58:05-debug: run package(web-desktop) handler(enable) success!
2025-9-23 11:58:05-debug: run package(web-mobile) handler(enable) start
2025-9-23 11:58:05-debug: run package(web-mobile) handler(enable) success!
2025-9-23 11:58:05-debug: run package(wechatgame) handler(enable) start
2025-9-23 11:58:05-debug: run package(wechatgame) handler(enable) success!
2025-9-23 11:58:05-debug: run package(wechatprogram) handler(enable) start
2025-9-23 11:58:05-debug: run package(wechatprogram) handler(enable) success!
2025-9-23 11:58:05-debug: run package(windows) handler(enable) start
2025-9-23 11:58:05-debug: run package(windows) handler(enable) success!
2025-9-23 11:58:05-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-23 11:58:05-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-23 11:58:05-debug: run package(cocos-service) handler(enable) start
2025-9-23 11:58:05-debug: run package(cocos-service) handler(enable) success!
2025-9-23 11:58:05-debug: run package(im-plugin) handler(enable) success!
2025-9-23 11:58:05-debug: run package(im-plugin) handler(enable) start
2025-9-23 11:58:05-debug: run package(ohos) handler(enable) start
2025-9-23 11:58:05-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-23 11:58:05-debug: run package(emitter-editor) handler(enable) start
2025-9-23 11:58:05-debug: run package(emitter-editor) handler(enable) success!
2025-9-23 11:58:05-debug: run package(oppo-mini-game) handler(enable) start
2025-9-23 11:58:05-debug: start refresh asset from db://assets/editor/enum-gen/EmitterEnum.ts...
2025-9-23 11:58:05-debug: refresh asset db://assets/editor/enum-gen success
2025-9-23 11:58:05-debug: start refresh asset from db://assets/editor/enum-gen/EnemyEnum.ts...
2025-9-23 11:58:05-debug: run package(level-editor) handler(enable) success!
2025-9-23 11:58:05-debug: run package(level-editor) handler(enable) start
2025-9-23 11:58:05-debug: refresh asset db://assets/editor/enum-gen success
2025-9-23 11:58:05-debug: run package(ohos) handler(enable) success!
2025-9-23 11:58:05-debug: run package(wave-editor) handler(enable) success!
2025-9-23 11:58:05-debug: run package(placeholder) handler(enable) start
2025-9-23 11:58:05-debug: run package(placeholder) handler(enable) success!
2025-9-23 11:58:05-debug: run package(wave-editor) handler(enable) start
2025-9-23 11:58:05-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-23 11:58:05-debug: asset-db:worker-init: initPlugin (1120ms)
2025-9-23 11:58:05-debug: Run asset db hook programming:beforePreStart ...
2025-9-23 11:58:05-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-23 11:58:05-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-23 11:58:05-debug: Run asset db hook programming:beforePreStart success!
2025-9-23 11:58:05-debug: [Assets Memory track]: asset-db:worker-init start:29.63MB, end 228.78MB, increase: 199.15MB
2025-9-23 11:58:05-debug: asset-db:worker-init (1763ms)
2025-9-23 11:58:05-debug: asset-db-hook-programming-beforePreStart (29ms)
2025-9-23 11:58:05-debug: asset-db-hook-engine-extends-beforePreStart (29ms)
2025-9-23 11:58:05-debug: Preimport db internal success
2025-9-23 11:58:05-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/editor/enum-gen/EmitterEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 11:58:05-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/editor/enum-gen/EnemyEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 11:58:05-debug: Preimport db assets success
2025-9-23 11:58:05-debug: Run asset db hook programming:afterPreStart ...
2025-9-23 11:58:05-debug: starting packer-driver...
2025-9-23 11:58:09-debug: initialize scripting environment...
2025-9-23 11:58:09-debug: [[Executor]] prepare before lock
2025-9-23 11:58:09-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-9-23 11:58:09-debug: [[Executor]] prepare after unlock
2025-9-23 11:58:09-debug: [Assets Memory track]: asset-db:worker-init: preStart start:228.79MB, end 236.36MB, increase: 7.57MB
2025-9-23 11:58:09-debug: Start up the 'internal' database...
2025-9-23 11:58:09-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-23 11:58:09-debug: Run asset db hook programming:afterPreStart success!
2025-9-23 11:58:09-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-23 11:58:10-debug: asset-db-hook-programming-afterPreStart (4362ms)
2025-9-23 11:58:10-debug: asset-db:worker-effect-data-processing (164ms)
2025-9-23 11:58:10-debug: asset-db-hook-engine-extends-afterPreStart (164ms)
2025-9-23 11:58:10-debug: [Assets Memory track]: asset-db:worker-startup-database[internal] start:228.87MB, end 244.56MB, increase: 15.69MB
2025-9-23 11:58:10-debug: Start up the 'assets' database...
2025-9-23 11:58:10-debug: asset-db:worker-startup-database[internal] (4503ms)
2025-9-23 11:58:10-debug: [Assets Memory track]: asset-db:worker-startup-database[assets] start:230.87MB, end 246.53MB, increase: 15.66MB
2025-9-23 11:58:10-debug: lazy register asset handler text
2025-9-23 11:58:10-debug: lazy register asset handler json
2025-9-23 11:58:10-debug: lazy register asset handler spine-data
2025-9-23 11:58:10-debug: lazy register asset handler dragonbones
2025-9-23 11:58:10-debug: lazy register asset handler dragonbones-atlas
2025-9-23 11:58:10-debug: lazy register asset handler terrain
2025-9-23 11:58:10-debug: lazy register asset handler javascript
2025-9-23 11:58:10-debug: lazy register asset handler typescript
2025-9-23 11:58:10-debug: lazy register asset handler scene
2025-9-23 11:58:10-debug: lazy register asset handler *
2025-9-23 11:58:10-debug: lazy register asset handler sprite-frame
2025-9-23 11:58:10-debug: lazy register asset handler tiled-map
2025-9-23 11:58:10-debug: lazy register asset handler directory
2025-9-23 11:58:10-debug: lazy register asset handler image
2025-9-23 11:58:10-debug: lazy register asset handler sign-image
2025-9-23 11:58:10-debug: lazy register asset handler alpha-image
2025-9-23 11:58:10-debug: lazy register asset handler texture
2025-9-23 11:58:10-debug: lazy register asset handler texture-cube
2025-9-23 11:58:10-debug: lazy register asset handler erp-texture-cube
2025-9-23 11:58:10-debug: lazy register asset handler render-texture
2025-9-23 11:58:10-debug: lazy register asset handler buffer
2025-9-23 11:58:10-debug: lazy register asset handler rt-sprite-frame
2025-9-23 11:58:10-debug: lazy register asset handler gltf
2025-9-23 11:58:10-debug: lazy register asset handler gltf-mesh
2025-9-23 11:58:10-debug: lazy register asset handler gltf-animation
2025-9-23 11:58:10-debug: [Assets Memory track]: asset-db:worker-init: startup start:236.56MB, end 246.54MB, increase: 9.98MB
2025-9-23 11:58:10-debug: lazy register asset handler gltf-material
2025-9-23 11:58:10-debug: lazy register asset handler gltf-scene
2025-9-23 11:58:10-debug: lazy register asset handler gltf-embeded-image
2025-9-23 11:58:10-debug: lazy register asset handler fbx
2025-9-23 11:58:10-debug: lazy register asset handler prefab
2025-9-23 11:58:10-debug: lazy register asset handler physics-material
2025-9-23 11:58:10-debug: lazy register asset handler effect
2025-9-23 11:58:10-debug: lazy register asset handler effect-header
2025-9-23 11:58:10-debug: lazy register asset handler audio-clip
2025-9-23 11:58:10-debug: lazy register asset handler animation-clip
2025-9-23 11:58:10-debug: lazy register asset handler texture-cube-face
2025-9-23 11:58:10-debug: lazy register asset handler material
2025-9-23 11:58:10-debug: lazy register asset handler animation-mask
2025-9-23 11:58:10-debug: lazy register asset handler animation-graph
2025-9-23 11:58:10-debug: lazy register asset handler animation-graph-variant
2025-9-23 11:58:10-debug: lazy register asset handler particle
2025-9-23 11:58:10-debug: lazy register asset handler sprite-atlas
2025-9-23 11:58:10-debug: lazy register asset handler bitmap-font
2025-9-23 11:58:10-debug: lazy register asset handler label-atlas
2025-9-23 11:58:10-debug: lazy register asset handler render-pipeline
2025-9-23 11:58:10-debug: lazy register asset handler render-stage
2025-9-23 11:58:10-debug: lazy register asset handler render-flow
2025-9-23 11:58:10-debug: lazy register asset handler instantiation-material
2025-9-23 11:58:10-debug: lazy register asset handler instantiation-mesh
2025-9-23 11:58:10-debug: lazy register asset handler instantiation-skeleton
2025-9-23 11:58:10-debug: lazy register asset handler instantiation-animation
2025-9-23 11:58:10-debug: lazy register asset handler video-clip
2025-9-23 11:58:10-debug: lazy register asset handler auto-atlas
2025-9-23 11:58:10-debug: lazy register asset handler gltf-skeleton
2025-9-23 11:58:10-debug: lazy register asset handler ttf-font
2025-9-23 11:58:10-debug: asset-db:worker-startup-database[assets] (4496ms)
2025-9-23 11:58:10-debug: asset-db:start-database (4540ms)
2025-9-23 11:58:10-debug: asset-db:ready (7718ms)
2025-9-23 11:58:10-debug: fix the bug of updateDefaultUserData
2025-9-23 11:58:10-debug: init worker message success
2025-9-23 11:58:10-debug: programming:execute-script (1ms)
2025-9-23 11:58:10-debug: [Build Memory track]: builder:worker-init start:250.30MB, end 260.94MB, increase: 10.64MB
2025-9-23 11:58:10-debug: builder:worker-init (246ms)
2025-9-23 12:04:44-debug: refresh db internal success
2025-9-23 12:04:44-debug: refresh db assets success
2025-9-23 12:04:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:04:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:04:44-debug: asset-db:refresh-all-database (69ms)
2025-9-23 12:06:08-debug: refresh db internal success
2025-9-23 12:06:08-debug: refresh db assets success
2025-9-23 12:06:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:06:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:06:08-debug: asset-db:refresh-all-database (64ms)
2025-9-23 12:06:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 12:06:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 12:11:12-debug: refresh db internal success
2025-9-23 12:11:12-debug: refresh db assets success
2025-9-23 12:11:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:11:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:11:12-debug: asset-db:refresh-all-database (56ms)
2025-9-23 12:11:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 12:11:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 12:12:41-debug: refresh db internal success
2025-9-23 12:12:41-debug: refresh db assets success
2025-9-23 12:12:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:12:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:12:41-debug: asset-db:refresh-all-database (70ms)
2025-9-23 12:12:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 12:12:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 12:15:15-debug: refresh db internal success
2025-9-23 12:15:16-debug: refresh db assets success
2025-9-23 12:15:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:15:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:15:16-debug: asset-db:refresh-all-database (51ms)
2025-9-23 12:16:36-debug: refresh db internal success
2025-9-23 12:16:36-debug: refresh db assets success
2025-9-23 12:16:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:16:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:16:36-debug: asset-db:refresh-all-database (62ms)
2025-9-23 12:16:36-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 12:16:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 12:17:03-debug: refresh db internal success
2025-9-23 12:17:03-debug: refresh db assets success
2025-9-23 12:17:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:17:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:17:03-debug: asset-db:refresh-all-database (57ms)
2025-9-23 12:17:03-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 12:17:58-debug: refresh db internal success
2025-9-23 12:17:58-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/weapon/Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 12:17:58-debug: refresh db assets success
2025-9-23 12:17:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:17:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:17:58-debug: asset-db:refresh-all-database (94ms)
2025-9-23 12:28:15-debug: refresh db internal success
2025-9-23 12:28:15-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/weapon/Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 12:28:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:28:15-debug: refresh db assets success
2025-9-23 12:28:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:28:15-debug: asset-db:refresh-all-database (75ms)
2025-9-23 12:28:35-debug: refresh db internal success
2025-9-23 12:28:35-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/weapon/Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 12:28:35-debug: refresh db assets success
2025-9-23 12:28:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:28:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:28:35-debug: asset-db:refresh-all-database (71ms)
2025-9-23 12:28:40-debug: refresh db internal success
2025-9-23 12:28:40-debug: refresh db assets success
2025-9-23 12:28:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:28:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:28:40-debug: asset-db:refresh-all-database (65ms)
2025-9-23 12:28:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 12:28:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 12:28:50-debug: refresh db internal success
2025-9-23 12:28:50-debug: refresh db assets success
2025-9-23 12:28:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:28:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:28:50-debug: asset-db:refresh-all-database (66ms)
2025-9-23 12:28:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 12:31:17-debug: refresh db internal success
2025-9-23 12:31:17-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/weapon/Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 12:31:17-debug: refresh db assets success
2025-9-23 12:31:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:31:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:31:17-debug: asset-db:refresh-all-database (70ms)
2025-9-23 12:31:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 12:31:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 12:31:42-debug: refresh db internal success
2025-9-23 12:31:42-debug: refresh db assets success
2025-9-23 12:31:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:31:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:31:42-debug: asset-db:refresh-all-database (67ms)
2025-9-23 12:31:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 12:35:27-debug: refresh db internal success
2025-9-23 12:35:27-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/weapon/Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 12:35:27-debug: refresh db assets success
2025-9-23 12:35:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:35:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:35:27-debug: asset-db:refresh-all-database (52ms)
2025-9-23 12:35:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 12:35:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 12:38:23-debug: refresh db internal success
2025-9-23 12:38:23-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/weapon/Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 12:38:23-debug: refresh db assets success
2025-9-23 12:38:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:38:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:38:23-debug: asset-db:refresh-all-database (64ms)
2025-9-23 12:38:39-debug: refresh db internal success
2025-9-23 12:38:39-debug: refresh db assets success
2025-9-23 12:38:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:38:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:38:39-debug: asset-db:refresh-all-database (84ms)
2025-9-23 12:38:39-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 12:38:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 12:41:13-debug: refresh db internal success
2025-9-23 12:41:13-debug: refresh db assets success
2025-9-23 12:41:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:41:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:41:13-debug: asset-db:refresh-all-database (69ms)
2025-9-23 12:41:13-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 12:41:13-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 12:55:57-debug: refresh db internal success
2025-9-23 12:55:57-debug: refresh db assets success
2025-9-23 12:55:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:55:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:55:57-debug: asset-db:refresh-all-database (76ms)
2025-9-23 12:55:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 12:55:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 12:56:10-debug: refresh db internal success
2025-9-23 12:56:10-debug: refresh db assets success
2025-9-23 12:56:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:56:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:56:10-debug: asset-db:refresh-all-database (50ms)
2025-9-23 12:56:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 12:56:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 12:56:20-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/resources/game/prefabs/enemy/EnemyPlane.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-23 12:56:20-debug: asset-db:reimport-assetf22ea656-6b22-4569-95bf-8be5766bba40 (4ms)
2025-9-23 12:56:24-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/resources/game/prefabs/enemy/EnemyPlane.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-23 12:56:24-debug: asset-db:reimport-assetf22ea656-6b22-4569-95bf-8be5766bba40 (5ms)
2025-9-23 12:57:10-debug: refresh db internal success
2025-9-23 12:57:10-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/weapon/Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 12:57:10-debug: refresh db assets success
2025-9-23 12:57:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:57:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:57:10-debug: asset-db:refresh-all-database (50ms)
2025-9-23 12:57:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 12:57:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 12:58:35-debug: refresh db internal success
2025-9-23 12:58:35-debug: refresh db assets success
2025-9-23 12:58:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 12:58:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 12:58:35-debug: asset-db:refresh-all-database (64ms)
2025-9-23 12:58:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 12:58:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 13:05:25-debug: refresh db internal success
2025-9-23 13:05:25-debug: refresh db assets success
2025-9-23 13:05:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 13:05:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 13:05:25-debug: asset-db:refresh-all-database (57ms)
2025-9-23 13:05:25-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 13:05:25-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 13:43:57-debug: refresh db internal success
2025-9-23 13:43:57-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/PlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 13:43:57-debug: refresh db assets success
2025-9-23 13:43:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 13:43:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 13:43:57-debug: asset-db:refresh-all-database (89ms)
2025-9-23 13:43:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 13:43:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 13:47:44-debug: refresh db internal success
2025-9-23 13:47:44-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/wave/WaveEventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 13:47:44-debug: refresh db assets success
2025-9-23 13:47:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 13:47:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 13:47:44-debug: asset-db:refresh-all-database (70ms)
2025-9-23 16:13:48-debug: refresh db internal success
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlaneStat.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/skill/ExCondition.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/skill/SearchTarget.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/luban/tbresskillcondition.json
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/luban/tbresstore.json
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/resources/game/prefabs/emitter/Bullet_211200131.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/luban
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/Game.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/luban/tbresbuffer.json
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/luban/tbglobalattr.json
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/luban/tbresgamemode.json
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/luban/tbrestaskorbit.json
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/resources/game/prefabs/emitter
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/autogen/luban
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/data/task
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/manager
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/move
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/autogen/luban/schema.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/data/task/Task.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/manager/MainPlaneManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/move/CameraMove.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/base
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/wave/Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/base/Controller.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/enemy
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/PlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/mainPlane
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/skill
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/skill/SkillComp.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/skill/BuffComp.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:13:48-debug: refresh db assets success
2025-9-23 16:13:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 16:13:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 16:13:48-debug: asset-db:refresh-all-database (111ms)
2025-9-23 16:13:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 16:13:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 16:16:28-debug: refresh db internal success
2025-9-23 16:16:28-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/bullet/actions/EmitterEventActions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:16:28-debug: refresh db assets success
2025-9-23 16:16:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 16:16:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 16:16:28-debug: asset-db:refresh-all-database (56ms)
2025-9-23 16:19:21-debug: refresh db internal success
2025-9-23 16:19:21-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/bullet/Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:19:21-debug: refresh db assets success
2025-9-23 16:19:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 16:19:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 16:19:21-debug: asset-db:refresh-all-database (65ms)
2025-9-23 16:22:49-debug: refresh db internal success
2025-9-23 16:22:49-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/bullet/Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:22:49-debug: refresh db assets success
2025-9-23 16:22:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 16:22:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 16:22:49-debug: asset-db:refresh-all-database (61ms)
2025-9-23 16:23:35-debug: refresh db internal success
2025-9-23 16:23:35-debug: refresh db assets success
2025-9-23 16:23:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 16:23:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 16:23:35-debug: asset-db:refresh-all-database (45ms)
2025-9-23 16:24:47-debug: refresh db internal success
2025-9-23 16:24:47-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/bullet/BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:24:47-debug: refresh db assets success
2025-9-23 16:24:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 16:24:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 16:24:47-debug: asset-db:refresh-all-database (91ms)
2025-9-23 16:25:58-debug: refresh db internal success
2025-9-23 16:25:58-debug: refresh db assets success
2025-9-23 16:25:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 16:25:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 16:25:58-debug: asset-db:refresh-all-database (60ms)
2025-9-23 16:25:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 16:25:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 16:26:43-debug: refresh db internal success
2025-9-23 16:26:43-debug: refresh db assets success
2025-9-23 16:26:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 16:26:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 16:26:43-debug: asset-db:refresh-all-database (69ms)
2025-9-23 16:27:40-debug: refresh db internal success
2025-9-23 16:27:40-debug: refresh db assets success
2025-9-23 16:27:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 16:27:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 16:27:40-debug: asset-db:refresh-all-database (66ms)
2025-9-23 16:27:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 16:27:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 16:28:17-debug: refresh db internal success
2025-9-23 16:28:17-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/bullet/PropertyContainer.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:28:17-debug: refresh db assets success
2025-9-23 16:28:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 16:28:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 16:28:17-debug: asset-db:refresh-all-database (74ms)
2025-9-23 16:29:06-debug: start refresh asset from /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/resources/game/prefabs/emitter/Bullet_211200131.prefab...
2025-9-23 16:29:06-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/resources/game/prefabs/emitter/Bullet_211200131.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:29:06-debug: refresh asset /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/resources/game/prefabs/emitter success
2025-9-23 16:29:38-debug: refresh db internal success
2025-9-23 16:29:38-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/bullet/BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:29:38-debug: refresh db assets success
2025-9-23 16:29:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 16:29:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 16:29:38-debug: asset-db:refresh-all-database (67ms)
2025-9-23 16:32:48-debug: refresh db internal success
2025-9-23 16:32:48-debug: refresh db assets success
2025-9-23 16:32:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 16:32:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 16:32:48-debug: asset-db:refresh-all-database (66ms)
2025-9-23 16:32:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 16:32:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 16:33:37-debug: refresh db internal success
2025-9-23 16:33:37-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/editor/emitter/EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:33:37-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/bullet/Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:33:37-debug: refresh db assets success
2025-9-23 16:33:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 16:33:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 16:33:37-debug: asset-db:refresh-all-database (66ms)
2025-9-23 16:33:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 16:35:09-debug: refresh db internal success
2025-9-23 16:35:09-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/bullet/conditions/EmitterEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:35:09-debug: refresh db assets success
2025-9-23 16:35:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 16:35:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 16:35:09-debug: asset-db:refresh-all-database (63ms)
2025-9-23 16:35:55-debug: refresh db internal success
2025-9-23 16:35:55-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/bullet/Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:35:55-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/bullet/PropertyContainer.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:35:55-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/bullet/BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:35:55-debug: refresh db assets success
2025-9-23 16:35:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 16:35:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 16:35:55-debug: asset-db:refresh-all-database (70ms)
2025-9-23 16:42:27-debug: refresh db internal success
2025-9-23 16:42:27-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/bullet
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:42:27-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/bullet/Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 16:42:27-debug: refresh db assets success
2025-9-23 16:42:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 16:42:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 16:42:27-debug: asset-db:refresh-all-database (60ms)
2025-9-23 16:42:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 16:42:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 16:59:12-debug: refresh db internal success
2025-9-23 16:59:12-debug: refresh db assets success
2025-9-23 16:59:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 16:59:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 16:59:12-debug: asset-db:refresh-all-database (55ms)
2025-9-23 16:59:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 16:59:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 17:11:02-debug: refresh db internal success
2025-9-23 17:11:02-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/bullet/Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:11:02-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/move/Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:11:02-debug: refresh db assets success
2025-9-23 17:11:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 17:11:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 17:11:02-debug: asset-db:refresh-all-database (56ms)
2025-9-23 17:23:27-debug: refresh db internal success
2025-9-23 17:23:27-debug: refresh db assets success
2025-9-23 17:23:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 17:23:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 17:23:27-debug: asset-db:refresh-all-database (66ms)
2025-9-23 17:23:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 17:23:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 17:23:38-debug: start refresh asset from /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/editor/emitter/Test.ts...
2025-9-23 17:23:38-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/editor/emitter/Test.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:23:38-debug: refresh asset /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/editor/emitter success
2025-9-23 17:23:38-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/editor/emitter
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:23:53-debug: programming:execute-script (2ms)
2025-9-23 17:23:55-debug: start remove asset /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/editor/emitter/Test.ts...
2025-9-23 17:23:55-debug: start refresh asset from /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/editor/emitter/Test.ts...
2025-9-23 17:23:55-debug: %cDestroy%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/editor/emitter/Test.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-23 17:23:55-debug: refresh asset /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/editor/emitter success
2025-9-23 17:23:55-debug: remove asset /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/editor/emitter/Test.ts success
2025-9-23 17:23:55-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/editor/emitter
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:26:22-debug: refresh db internal success
2025-9-23 17:26:22-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/editor/emitter/EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:26:22-debug: refresh db assets success
2025-9-23 17:26:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 17:26:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 17:26:22-debug: asset-db:refresh-all-database (105ms)
2025-9-23 17:26:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 17:26:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 17:26:46-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:26:46-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (6ms)
2025-9-23 17:27:40-debug: refresh db internal success
2025-9-23 17:27:40-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/move/Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:27:40-debug: refresh db assets success
2025-9-23 17:27:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 17:27:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 17:27:40-debug: asset-db:refresh-all-database (56ms)
2025-9-23 17:28:15-debug: refresh db internal success
2025-9-23 17:28:15-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/move/Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:15-debug: refresh db assets success
2025-9-23 17:28:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 17:28:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 17:28:15-debug: asset-db:refresh-all-database (68ms)
2025-9-23 17:28:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 17:28:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-23 17:28:34-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:34-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (5ms)
2025-9-23 17:28:36-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:36-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:37-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:37-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:37-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:37-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:37-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:37-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-23 17:28:38-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:38-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-23 17:28:39-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:39-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:39-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:39-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:39-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:39-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:39-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:39-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (4ms)
2025-9-23 17:28:39-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:39-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-23 17:28:39-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:39-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:39-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:39-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (4ms)
2025-9-23 17:28:41-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:41-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:41-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:41-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (4ms)
2025-9-23 17:28:41-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:41-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:41-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:41-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:41-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:41-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:42-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:42-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:42-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:42-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:42-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:42-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:42-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:42-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-23 17:28:42-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:42-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:42-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:42-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:42-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:42-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:42-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:42-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:42-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:42-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (4ms)
2025-9-23 17:28:42-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:42-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:42-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:42-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:42-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:42-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:43-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:43-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:43-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:43-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:43-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:43-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:43-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:43-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:43-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:43-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:52-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:52-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:52-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:52-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:52-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:52-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-23 17:28:52-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:52-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:53-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:53-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:53-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:53-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-23 17:28:53-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:53-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:53-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:53-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:53-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:53-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:53-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:53-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:53-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:53-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:53-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:53-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:53-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:53-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:53-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:53-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:53-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:53-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:53-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:53-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-23 17:28:54-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:54-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (2ms)
2025-9-23 17:28:57-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:57-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:28:59-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:28:59-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-23 17:29:03-debug: %cImport%c: /Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/scenes/BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-23 17:29:03-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (9ms)
2025-9-23 18:26:43-debug: refresh db internal success
2025-9-23 18:26:43-debug: refresh db assets success
2025-9-23 18:26:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-23 18:26:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-23 18:26:43-debug: asset-db:refresh-all-database (77ms)
2025-9-23 18:26:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-23 18:26:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-25 10:06:34-debug: refresh db internal success
2025-9-25 10:06:34-debug: refresh db assets success
2025-9-25 10:06:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-25 10:06:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-25 10:06:34-debug: asset-db:refresh-all-database (84ms)
2025-9-25 10:06:34-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-25 10:16:52-debug: refresh db internal success
2025-9-25 10:16:52-debug: refresh db assets success
2025-9-25 10:16:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-25 10:16:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-25 10:16:52-debug: asset-db:refresh-all-database (94ms)
2025-9-25 10:16:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-25 10:16:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
