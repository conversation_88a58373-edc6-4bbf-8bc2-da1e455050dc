{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/move/CameraMove.ts"], "names": ["_decorator", "CCFloat", "Component", "Vec3", "GameIns", "GameEnum", "ccclass", "property", "CameraMove", "_targetPosition", "_currentPosition", "_velocity", "_lastTargetX", "_isMoving", "_deadZoneCenter", "start", "node", "getPosition", "set", "x", "update", "deltaTime", "gameStateManager", "gameState", "GameState", "Battle", "mainPlane", "mainPlaneManager", "planePos", "position", "calculateTargetPosition", "updateCameraPosition", "sceneWidth", "sceneCenterX", "planeOffsetX", "cameraOffsetX", "Math", "max", "min", "y", "z", "distance", "abs", "moveT<PERSON><PERSON>old", "smoothMoveToTarget", "direction", "sign", "absDistance", "setPosition", "desiredSpeed", "calculateDesiredSpeed", "adjustVelocity", "newX", "newDistance", "newDirection", "distanceRatio", "easeFactor", "pow", "maxFollowSpeed", "speedDiff", "acceleration", "forceMoveToTarget", "resetCamera"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AAChCC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;4BAGjBQ,U,WADZF,OAAO,CAAC,YAAD,C,UAGHC,QAAQ,CAACN,OAAD,C,UAGRM,QAAQ,CAACN,OAAD,C,UAGRM,QAAQ,CAACN,OAAD,C,UAGRM,QAAQ,CAACN,OAAD,C,2BAZb,MACaO,UADb,SACgCN,SADhC,CAC0C;AAAA;AAAA;;AAAA;;AAGZ;AAHY;;AAMR;AANQ;;AASlB;AATkB;;AAYlB;AAZkB,eAc9BO,eAd8B,GAcN,IAAIN,IAAJ,EAdM;AAcM;AAdN,eAe9BO,gBAf8B,GAeL,IAAIP,IAAJ,EAfK;AAeO;AAfP,eAgB9BQ,SAhB8B,GAgBV,CAhBU;AAgBP;AAhBO,eAiB9BC,YAjB8B,GAiBP,CAjBO;AAiBJ;AAjBI,eAkB9BC,SAlB8B,GAkBT,KAlBS;AAkBF;AAlBE,eAmB9BC,eAnB8B,GAmBJ,CAnBI;AAAA;;AAmBD;AAErCC,QAAAA,KAAK,GAAG;AACJ;AACA,eAAKC,IAAL,CAAUC,WAAV,CAAsB,KAAKP,gBAA3B;;AACA,eAAKD,eAAL,CAAqBS,GAArB,CAAyB,KAAKR,gBAA9B;;AACA,eAAKE,YAAL,GAAoB,KAAKF,gBAAL,CAAsBS,CAA1C;AACA,eAAKL,eAAL,GAAuB,KAAKJ,gBAAL,CAAsBS,CAA7C;AACA,eAAKN,SAAL,GAAiB,KAAjB;AACA,eAAKF,SAAL,GAAiB,CAAjB;AACH;;AAEDS,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI;AAAA;AAAA,kCAAQC,gBAAR,CAAyBC,SAAzB,IAAsC;AAAA;AAAA,oCAASC,SAAT,CAAmBC,MAA7D,EAAqE;AACjE,mBADiE,CACzD;AACX;;AAED,cAAIC,SAAS,GAAG;AAAA;AAAA,kCAAQC,gBAAR,CAAyBD,SAAzC;AACA,cAAI,CAACA,SAAL,EAAgB,OANM,CAQtB;;AACA,cAAME,QAAQ,GAAGF,SAAS,CAACV,IAAV,CAAea,QAAhC,CATsB,CAWtB;;AACA,eAAKC,uBAAL,CAA6BF,QAA7B,EAZsB,CActB;;AACA,eAAKG,oBAAL,CAA0BV,SAA1B,EAfsB,CAiBtB;;AACA,eAAKT,YAAL,GAAoB,KAAKH,eAAL,CAAqBU,CAAzC;AACH;AAED;AACJ;AACA;AACA;;;AACYW,QAAAA,uBAAuB,CAACF,QAAD,EAAiB;AAC5C,cAAMI,UAAU,GAAG,GAAnB;AACA,cAAMC,YAAY,GAAG,CAArB;AAEA,cAAMC,YAAY,GAAGD,YAAY,GAAGL,QAAQ,CAACT,CAA7C,CAJ4C,CAII;;AAChD,cAAIgB,aAAa,GAAID,YAAY,IAAIF,UAAU,GAAG,CAAjB,CAAb,GAAoC,GAAxD;AACAG,UAAAA,aAAa,GAAGC,IAAI,CAACC,GAAL,CAAS,CAAC,GAAV,EAAeD,IAAI,CAACE,GAAL,CAAS,GAAT,EAAcH,aAAd,CAAf,CAAhB;;AAEA,eAAK1B,eAAL,CAAqBS,GAArB,CACIiB,aADJ,EAEI,KAAKzB,gBAAL,CAAsB6B,CAF1B,EAGI,KAAK7B,gBAAL,CAAsB8B,CAH1B;AAKH;AAED;AACJ;AACA;AACA;;;AACYT,QAAAA,oBAAoB,CAACV,SAAD,EAAoB;AAC5C;AACA,eAAKL,IAAL,CAAUC,WAAV,CAAsB,KAAKP,gBAA3B,EAF4C,CAI5C;;AACA,cAAM+B,QAAQ,GAAGL,IAAI,CAACM,GAAL,CAAS,KAAKhC,gBAAL,CAAsBS,CAAtB,GAA0B,KAAKV,eAAL,CAAqBU,CAAxD,CAAjB,CAL4C,CAO5C;;AACA,cAAI,CAAC,KAAKN,SAAN,IAAmB4B,QAAQ,GAAG,KAAKE,aAAvC,EAAsD;AAClD,iBAAK9B,SAAL,GAAiB,IAAjB;AACA,iBAAKC,eAAL,GAAuB,KAAKJ,gBAAL,CAAsBS,CAA7C;AACH,WAX2C,CAa5C;;;AACA,cAAI,KAAKN,SAAL,IAAkB4B,QAAQ,GAAG,KAAKE,aAAL,GAAqB,GAAtD,EAA2D;AACvD,iBAAK9B,SAAL,GAAiB,KAAjB;AACA,iBAAKF,SAAL,GAAiB,CAAjB;AACA,iBAAKG,eAAL,GAAuB,KAAKJ,gBAAL,CAAsBS,CAA7C;AACH,WAlB2C,CAoB5C;;;AACA,cAAI,KAAKN,SAAT,EAAoB;AAChB,iBAAK+B,kBAAL,CAAwBvB,SAAxB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACYuB,QAAAA,kBAAkB,CAACvB,SAAD,EAAoB;AAC1C;AACA,cAAMoB,QAAQ,GAAG,KAAKhC,eAAL,CAAqBU,CAArB,GAAyB,KAAKT,gBAAL,CAAsBS,CAAhE;AACA,cAAM0B,SAAS,GAAGT,IAAI,CAACU,IAAL,CAAUL,QAAV,CAAlB;AACA,cAAMM,WAAW,GAAGX,IAAI,CAACM,GAAL,CAASD,QAAT,CAApB,CAJ0C,CAM1C;;AACA,cAAIM,WAAW,GAAG,GAAlB,EAAuB;AACnB,iBAAKrC,gBAAL,CAAsBS,CAAtB,GAA0B,KAAKV,eAAL,CAAqBU,CAA/C;AACA,iBAAKH,IAAL,CAAUgC,WAAV,CAAsB,KAAKtC,gBAA3B;AACA,iBAAKC,SAAL,GAAiB,CAAjB;AACA;AACH,WAZyC,CAc1C;AACA;;;AACA,cAAMsC,YAAY,GAAG,KAAKC,qBAAL,CAA2BH,WAA3B,EAAwCF,SAAxC,CAArB,CAhB0C,CAkB1C;;AACA,eAAKM,cAAL,CAAoBF,YAApB,EAAkC5B,SAAlC,EAnB0C,CAqB1C;;AACA,cAAM+B,IAAI,GAAG,KAAK1C,gBAAL,CAAsBS,CAAtB,GAA0B,KAAKR,SAAL,GAAiBU,SAAxD,CAtB0C,CAwB1C;;AACA,cAAMgC,WAAW,GAAG,KAAK5C,eAAL,CAAqBU,CAArB,GAAyBiC,IAA7C;AACA,cAAME,YAAY,GAAGlB,IAAI,CAACU,IAAL,CAAUO,WAAV,CAArB;;AAEA,cAAIR,SAAS,KAAK,CAAd,IAAmBS,YAAY,KAAKT,SAAxC,EAAmD;AAC/C;AACA,iBAAKnC,gBAAL,CAAsBS,CAAtB,GAA0B,KAAKV,eAAL,CAAqBU,CAA/C;AACA,iBAAKR,SAAL,GAAiB,CAAjB;AACH,WAJD,MAIO;AACH;AACA,iBAAKD,gBAAL,CAAsBS,CAAtB,GAA0BiC,IAA1B;AACH,WAnCyC,CAqC1C;;;AACA,eAAKpC,IAAL,CAAUgC,WAAV,CAAsB,KAAKtC,gBAA3B;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACYwC,QAAAA,qBAAqB,CAACT,QAAD,EAAmBI,SAAnB,EAA8C;AACvE;AACA;AACA;AAEA;AACA,cAAMU,aAAa,GAAGnB,IAAI,CAACE,GAAL,CAASG,QAAQ,IAAI,KAAKE,aAAL,GAAqB,CAAzB,CAAjB,EAA8C,CAA9C,CAAtB,CANuE,CAQvE;;AACA,cAAMa,UAAU,GAAGD,aAAa,GAAG,GAAhB,GACb,IAAIA,aAAJ,GAAoBA,aADP,CACsB;AADtB,YAEb,IAAInB,IAAI,CAACqB,GAAL,CAAS,CAAC,CAAD,GAAKF,aAAL,GAAqB,CAA9B,EAAiC,CAAjC,IAAsC,CAFhD,CATuE,CAWnB;AAEpD;;AACA,iBAAOV,SAAS,GAAGW,UAAZ,GAAyB,KAAKE,cAArC;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACYP,QAAAA,cAAc,CAACF,YAAD,EAAuB5B,SAAvB,EAA0C;AAC5D;AACA,cAAMsC,SAAS,GAAGV,YAAY,GAAG,KAAKtC,SAAtC,CAF4D,CAI5D;;AACA,cAAMiD,YAAY,GAAGxB,IAAI,CAACU,IAAL,CAAUa,SAAV,IACjBvB,IAAI,CAACE,GAAL,CAASF,IAAI,CAACM,GAAL,CAASiB,SAAT,IAAsB,KAAKC,YAApC,EAAkD,KAAKF,cAAL,GAAsB,CAAxE,CADJ,CAL4D,CAQ5D;;AACA,eAAK/C,SAAL,IAAkBiD,YAAY,GAAGvC,SAAjC,CAT4D,CAW5D;;AACA,cAAIe,IAAI,CAACM,GAAL,CAAS,KAAK/B,SAAd,IAA2B,KAAK+C,cAApC,EAAoD;AAChD,iBAAK/C,SAAL,GAAiByB,IAAI,CAACU,IAAL,CAAU,KAAKnC,SAAf,IAA4B,KAAK+C,cAAlD;AACH;AACJ;AAED;AACJ;AACA;;;AACWG,QAAAA,iBAAiB,GAAG;AACvB,eAAK7C,IAAL,CAAUC,WAAV,CAAsB,KAAKP,gBAA3B;AACA,eAAKM,IAAL,CAAUgC,WAAV,CAAsB,KAAKvC,eAA3B;AACA,eAAKE,SAAL,GAAiB,CAAjB;AACA,eAAKE,SAAL,GAAiB,KAAjB;AACA,eAAKC,eAAL,GAAuB,KAAKL,eAAL,CAAqBU,CAA5C;AACH;AAED;AACJ;AACA;;;AACW2C,QAAAA,WAAW,GAAG;AACjB,eAAK9C,IAAL,CAAUC,WAAV,CAAsB,KAAKP,gBAA3B;;AACA,eAAKD,eAAL,CAAqBS,GAArB,CAAyB,KAAKR,gBAA9B;;AACA,eAAKC,SAAL,GAAiB,CAAjB;AACA,eAAKE,SAAL,GAAiB,KAAjB;AACA,eAAKD,YAAL,GAAoB,KAAKF,gBAAL,CAAsBS,CAA1C;AACA,eAAKL,eAAL,GAAuB,KAAKJ,gBAAL,CAAsBS,CAA7C;AACH;;AApNqC,O;;;;;iBAGjB,G;;;;;;;iBAGI,G;;;;;;;iBAGT,E;;;;;;;iBAGD,G", "sourcesContent": ["import { _decorator, CCFloat, Component, Vec3 } from \"cc\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport { GameEnum } from \"../const/GameEnum\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('CameraMove')\r\nexport class <PERSON><PERSON>ove extends Component {\r\n    \r\n    @property(CCFloat)\r\n    public followDelay = 0.3; // 跟随延迟时间（秒）\r\n    \r\n    @property(CCFloat)\r\n    maxFollowSpeed: number = 300; // 最大跟随速度（像素/秒）\r\n\r\n    @property(CCFloat)\r\n    moveThreshold = 50; // 移动范围阈值（像素），飞机移动超过此范围摄像机才会跟随\r\n    \r\n    @property(CCFloat)\r\n    acceleration = 5.0; // 加速度系数\r\n    \r\n    private _targetPosition: Vec3 = new Vec3(); // 目标位置\r\n    private _currentPosition: Vec3 = new Vec3(); // 当前位置\r\n    private _velocity: number = 0; // 当前速度（只考虑X轴）\r\n    private _lastTargetX: number = 0; // 上一次的目标X位置\r\n    private _isMoving: boolean = false; // 是否正在移动\r\n    private _deadZoneCenter: number = 0; // 死区中心位置\r\n\r\n    start() {\r\n        // 初始化位置\r\n        this.node.getPosition(this._currentPosition);\r\n        this._targetPosition.set(this._currentPosition);\r\n        this._lastTargetX = this._currentPosition.x;\r\n        this._deadZoneCenter = this._currentPosition.x;\r\n        this._isMoving = false;\r\n        this._velocity = 0;\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        if (GameIns.gameStateManager.gameState != GameEnum.GameState.Battle) {\r\n            return; // 游戏未进入战斗状态时不处理\r\n        }\r\n\r\n        let mainPlane = GameIns.mainPlaneManager.mainPlane;\r\n        if (!mainPlane) return;\r\n        \r\n        // 获取飞机位置\r\n        const planePos = mainPlane.node.position;\r\n        \r\n        // 计算摄像机应该跟随的目标位置\r\n        this.calculateTargetPosition(planePos);\r\n        \r\n        // 更新摄像机位置\r\n        this.updateCameraPosition(deltaTime);\r\n        \r\n        // 更新上一次的目标位置\r\n        this._lastTargetX = this._targetPosition.x;\r\n    }\r\n    \r\n    /**\r\n     * 计算摄像机目标位置\r\n     * @param planePos 飞机位置\r\n     */\r\n    private calculateTargetPosition(planePos: Vec3) {\r\n        const sceneWidth = 950;\r\n        const sceneCenterX = 0;\r\n    \r\n        const planeOffsetX = sceneCenterX - planePos.x; // 反向计算偏移量\r\n        let cameraOffsetX = (planeOffsetX / (sceneWidth / 2)) * 100;\r\n        cameraOffsetX = Math.max(-100, Math.min(100, cameraOffsetX));\r\n    \r\n        this._targetPosition.set(\r\n            cameraOffsetX,\r\n            this._currentPosition.y,\r\n            this._currentPosition.z\r\n        );\r\n    }\r\n    \r\n    /**\r\n     * 更新摄像机位置\r\n     * @param deltaTime 帧时间\r\n     */\r\n    private updateCameraPosition(deltaTime: number) {\r\n        // 获取当前位置\r\n        this.node.getPosition(this._currentPosition);\r\n        \r\n        // 计算当前位置与目标位置的距离\r\n        const distance = Math.abs(this._currentPosition.x - this._targetPosition.x);\r\n        \r\n        // 检查是否需要开始移动（超出死区）\r\n        if (!this._isMoving && distance > this.moveThreshold) {\r\n            this._isMoving = true;\r\n            this._deadZoneCenter = this._currentPosition.x;\r\n        }\r\n        \r\n        // 检查是否可以停止移动（进入死区）\r\n        if (this._isMoving && distance < this.moveThreshold * 0.3) {\r\n            this._isMoving = false;\r\n            this._velocity = 0;\r\n            this._deadZoneCenter = this._currentPosition.x;\r\n        }\r\n        \r\n        // 如果正在移动，执行平滑移动\r\n        if (this._isMoving) {\r\n            this.smoothMoveToTarget(deltaTime);\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * 平滑移动到目标位置\r\n     * @param deltaTime 帧时间\r\n     */\r\n    private smoothMoveToTarget(deltaTime: number) {\r\n        // 计算当前位置与目标位置的距离和方向\r\n        const distance = this._targetPosition.x - this._currentPosition.x;\r\n        const direction = Math.sign(distance);\r\n        const absDistance = Math.abs(distance);\r\n        \r\n        // 如果距离很小，直接设置到目标位置\r\n        if (absDistance < 0.1) {\r\n            this._currentPosition.x = this._targetPosition.x;\r\n            this.node.setPosition(this._currentPosition);\r\n            this._velocity = 0;\r\n            return;\r\n        }\r\n        \r\n        // 计算期望速度（基于距离和延迟时间）\r\n        // 使用平滑的加速度曲线，避免突然加速\r\n        const desiredSpeed = this.calculateDesiredSpeed(absDistance, direction);\r\n        \r\n        // 平滑调整当前速度\r\n        this.adjustVelocity(desiredSpeed, deltaTime);\r\n        \r\n        // 计算新位置\r\n        const newX = this._currentPosition.x + this._velocity * deltaTime;\r\n        \r\n        // 防止过冲：检查移动方向是否正确\r\n        const newDistance = this._targetPosition.x - newX;\r\n        const newDirection = Math.sign(newDistance);\r\n        \r\n        if (direction !== 0 && newDirection !== direction) {\r\n            // 如果方向改变，说明会过冲，直接设置到目标位置\r\n            this._currentPosition.x = this._targetPosition.x;\r\n            this._velocity = 0;\r\n        } else {\r\n            // 否则正常移动\r\n            this._currentPosition.x = newX;\r\n        }\r\n        \r\n        // 设置新位置\r\n        this.node.setPosition(this._currentPosition);\r\n    }\r\n    \r\n    /**\r\n     * 计算期望速度\r\n     * @param distance 距离\r\n     * @param direction 方向\r\n     * @returns 期望速度\r\n     */\r\n    private calculateDesiredSpeed(distance: number, direction: number): number {\r\n        // 使用缓动函数计算期望速度，避免突然加速\r\n        // 当距离较远时，使用最大速度\r\n        // 当距离较近时，逐渐减速\r\n        \r\n        // 计算距离比例（0到1之间）\r\n        const distanceRatio = Math.min(distance / (this.moveThreshold * 2), 1);\r\n        \r\n        // 使用平方缓动函数，使加速和减速更平滑\r\n        const easeFactor = distanceRatio < 0.5 \r\n            ? 2 * distanceRatio * distanceRatio  // 加速阶段\r\n            : 1 - Math.pow(-2 * distanceRatio + 2, 2) / 2;  // 减速阶段\r\n        \r\n        // 计算期望速度\r\n        return direction * easeFactor * this.maxFollowSpeed;\r\n    }\r\n    \r\n    /**\r\n     * 调整速度\r\n     * @param desiredSpeed 期望速度\r\n     * @param deltaTime 帧时间\r\n     */\r\n    private adjustVelocity(desiredSpeed: number, deltaTime: number) {\r\n        // 计算速度差\r\n        const speedDiff = desiredSpeed - this._velocity;\r\n        \r\n        // 计算加速度（限制最大加速度）\r\n        const acceleration = Math.sign(speedDiff) * \r\n            Math.min(Math.abs(speedDiff) * this.acceleration, this.maxFollowSpeed * 2);\r\n        \r\n        // 更新速度\r\n        this._velocity += acceleration * deltaTime;\r\n        \r\n        // 限制速度不超过最大值\r\n        if (Math.abs(this._velocity) > this.maxFollowSpeed) {\r\n            this._velocity = Math.sign(this._velocity) * this.maxFollowSpeed;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 强制移动摄像机到目标位置\r\n     */\r\n    public forceMoveToTarget() {\r\n        this.node.getPosition(this._currentPosition);\r\n        this.node.setPosition(this._targetPosition);\r\n        this._velocity = 0;\r\n        this._isMoving = false;\r\n        this._deadZoneCenter = this._targetPosition.x;\r\n    }\r\n\r\n    /**\r\n     * 重置摄像机状态\r\n     */\r\n    public resetCamera() {\r\n        this.node.getPosition(this._currentPosition);\r\n        this._targetPosition.set(this._currentPosition);\r\n        this._velocity = 0;\r\n        this._isMoving = false;\r\n        this._lastTargetX = this._currentPosition.x;\r\n        this._deadZoneCenter = this._currentPosition.x;\r\n    }\r\n}"]}