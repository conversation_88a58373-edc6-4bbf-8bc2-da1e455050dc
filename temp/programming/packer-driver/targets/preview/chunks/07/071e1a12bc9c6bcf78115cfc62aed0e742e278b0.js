System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, CCFloat, Component, Vec3, GameIns, GameEnum, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, ccclass, property, CameraMove;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../const/GameEnum", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      CCFloat = _cc.CCFloat;
      Component = _cc.Component;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      GameIns = _unresolved_2.GameIns;
    }, function (_unresolved_3) {
      GameEnum = _unresolved_3.GameEnum;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "bf258c+BlNO24ox3moFQ/T+", "CameraMove", undefined);

      __checkObsolete__(['_decorator', 'CCFloat', 'Component', 'Vec3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("CameraMove", CameraMove = (_dec = ccclass('CameraMove'), _dec2 = property(CCFloat), _dec3 = property(CCFloat), _dec4 = property(CCFloat), _dec5 = property(CCFloat), _dec(_class = (_class2 = class CameraMove extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "followDelay", _descriptor, this);

          // 跟随延迟时间（秒）
          _initializerDefineProperty(this, "maxFollowSpeed", _descriptor2, this);

          // 最大跟随速度（像素/秒）
          _initializerDefineProperty(this, "moveThreshold", _descriptor3, this);

          // 移动范围阈值（像素），飞机移动超过此范围摄像机才会跟随
          _initializerDefineProperty(this, "acceleration", _descriptor4, this);

          // 加速度系数
          this._targetPosition = new Vec3();
          // 目标位置
          this._currentPosition = new Vec3();
          // 当前位置
          this._velocity = 0;
          // 当前速度（只考虑X轴）
          this._lastTargetX = 0;
          // 上一次的目标X位置
          this._isMoving = false;
          // 是否正在移动
          this._deadZoneCenter = 0;
        }

        // 死区中心位置
        start() {
          // 初始化位置
          this.node.getPosition(this._currentPosition);

          this._targetPosition.set(this._currentPosition);

          this._lastTargetX = this._currentPosition.x;
          this._deadZoneCenter = this._currentPosition.x;
          this._isMoving = false;
          this._velocity = 0;
        }

        update(deltaTime) {
          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.gameState != (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).GameState.Battle) {
            return; // 游戏未进入战斗状态时不处理
          }

          var mainPlane = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane;
          if (!mainPlane) return; // 获取飞机位置

          var planePos = mainPlane.node.position; // 计算摄像机应该跟随的目标位置

          this.calculateTargetPosition(planePos); // 更新摄像机位置

          this.updateCameraPosition(deltaTime); // 更新上一次的目标位置

          this._lastTargetX = this._targetPosition.x;
        }
        /**
         * 计算摄像机目标位置
         * @param planePos 飞机位置
         */


        calculateTargetPosition(planePos) {
          var sceneWidth = 950;
          var sceneCenterX = 0;
          var planeOffsetX = sceneCenterX - planePos.x; // 反向计算偏移量

          var cameraOffsetX = planeOffsetX / (sceneWidth / 2) * 100;
          cameraOffsetX = Math.max(-100, Math.min(100, cameraOffsetX));

          this._targetPosition.set(cameraOffsetX, this._currentPosition.y, this._currentPosition.z);
        }
        /**
         * 更新摄像机位置
         * @param deltaTime 帧时间
         */


        updateCameraPosition(deltaTime) {
          // 获取当前位置
          this.node.getPosition(this._currentPosition); // 计算当前位置与目标位置的距离

          var distance = Math.abs(this._currentPosition.x - this._targetPosition.x); // 检查是否需要开始移动（超出死区）

          if (!this._isMoving && distance > this.moveThreshold) {
            this._isMoving = true;
            this._deadZoneCenter = this._currentPosition.x;
          } // 检查是否可以停止移动（进入死区）


          if (this._isMoving && distance < this.moveThreshold * 0.3) {
            this._isMoving = false;
            this._velocity = 0;
            this._deadZoneCenter = this._currentPosition.x;
          } // 如果正在移动，执行平滑移动


          if (this._isMoving) {
            this.smoothMoveToTarget(deltaTime);
          }
        }
        /**
         * 平滑移动到目标位置
         * @param deltaTime 帧时间
         */


        smoothMoveToTarget(deltaTime) {
          // 计算当前位置与目标位置的距离和方向
          var distance = this._targetPosition.x - this._currentPosition.x;
          var direction = Math.sign(distance);
          var absDistance = Math.abs(distance); // 如果距离很小，直接设置到目标位置

          if (absDistance < 0.1) {
            this._currentPosition.x = this._targetPosition.x;
            this.node.setPosition(this._currentPosition);
            this._velocity = 0;
            return;
          } // 计算期望速度（基于距离和延迟时间）
          // 使用平滑的加速度曲线，避免突然加速


          var desiredSpeed = this.calculateDesiredSpeed(absDistance, direction); // 平滑调整当前速度

          this.adjustVelocity(desiredSpeed, deltaTime); // 计算新位置

          var newX = this._currentPosition.x + this._velocity * deltaTime; // 防止过冲：检查移动方向是否正确

          var newDistance = this._targetPosition.x - newX;
          var newDirection = Math.sign(newDistance);

          if (direction !== 0 && newDirection !== direction) {
            // 如果方向改变，说明会过冲，直接设置到目标位置
            this._currentPosition.x = this._targetPosition.x;
            this._velocity = 0;
          } else {
            // 否则正常移动
            this._currentPosition.x = newX;
          } // 设置新位置


          this.node.setPosition(this._currentPosition);
        }
        /**
         * 计算期望速度
         * @param distance 距离
         * @param direction 方向
         * @returns 期望速度
         */


        calculateDesiredSpeed(distance, direction) {
          // 使用缓动函数计算期望速度，避免突然加速
          // 当距离较远时，使用最大速度
          // 当距离较近时，逐渐减速
          // 计算距离比例（0到1之间）
          var distanceRatio = Math.min(distance / (this.moveThreshold * 2), 1); // 使用平方缓动函数，使加速和减速更平滑

          var easeFactor = distanceRatio < 0.5 ? 2 * distanceRatio * distanceRatio // 加速阶段
          : 1 - Math.pow(-2 * distanceRatio + 2, 2) / 2; // 减速阶段
          // 计算期望速度

          return direction * easeFactor * this.maxFollowSpeed;
        }
        /**
         * 调整速度
         * @param desiredSpeed 期望速度
         * @param deltaTime 帧时间
         */


        adjustVelocity(desiredSpeed, deltaTime) {
          // 计算速度差
          var speedDiff = desiredSpeed - this._velocity; // 计算加速度（限制最大加速度）

          var acceleration = Math.sign(speedDiff) * Math.min(Math.abs(speedDiff) * this.acceleration, this.maxFollowSpeed * 2); // 更新速度

          this._velocity += acceleration * deltaTime; // 限制速度不超过最大值

          if (Math.abs(this._velocity) > this.maxFollowSpeed) {
            this._velocity = Math.sign(this._velocity) * this.maxFollowSpeed;
          }
        }
        /**
         * 强制移动摄像机到目标位置
         */


        forceMoveToTarget() {
          this.node.getPosition(this._currentPosition);
          this.node.setPosition(this._targetPosition);
          this._velocity = 0;
          this._isMoving = false;
          this._deadZoneCenter = this._targetPosition.x;
        }
        /**
         * 重置摄像机状态
         */


        resetCamera() {
          this.node.getPosition(this._currentPosition);

          this._targetPosition.set(this._currentPosition);

          this._velocity = 0;
          this._isMoving = false;
          this._lastTargetX = this._currentPosition.x;
          this._deadZoneCenter = this._currentPosition.x;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "followDelay", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0.3;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "maxFollowSpeed", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 300;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "moveThreshold", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 50;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "acceleration", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 5.0;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=071e1a12bc9c6bcf78115cfc62aed0e742e278b0.js.map