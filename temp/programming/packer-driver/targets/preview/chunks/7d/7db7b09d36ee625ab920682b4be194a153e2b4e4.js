System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, MainPlaneStat, _crd;

  _export("default", void 0);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "9169cti7RtD3bC+aGkp0BYe", "MainPlaneStat", undefined);

      _export("default", MainPlaneStat = class MainPlaneStat {
        constructor() {
          this.pickDiamond = 0;
          this.killEnemy = 0;
          this.usedNuclear = 0;
          this.usedSuper = 0;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7db7b09d36ee625ab920682b4be194a153e2b4e4.js.map