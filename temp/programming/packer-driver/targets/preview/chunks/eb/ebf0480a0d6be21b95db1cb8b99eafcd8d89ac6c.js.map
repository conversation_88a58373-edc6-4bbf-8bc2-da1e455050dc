{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/bullet/conditions/EmitterEventConditions.ts"], "names": ["EmitterConditionBase", "EmitterCondition_Active", "EmitterCondition_InitialDelay", "EmitterCondition_Prewarm", "EmitterCondition_PrewarmDuration", "EmitterCondition_Duration", "EmitterCondition_ElapsedTime", "EmitterCondition_Loop", "EmitterCondition_LoopInterval", "EmitterCondition_EmitInterval", "EmitterCondition_PerEmitCount", "EmitterCondition_PerEmitInterval", "EmitterCondition_PerEmitOffsetX", "EmitterCondition_Angle", "EmitterCondition_Count", "EmitterCondition_BulletDuration", "EmitterCondition_BulletSpeed", "EmitterCondition_BulletAcceleration", "EmitterCondition_BulletAccelerationAngle", "EmitterCondition_BulletFacingMoveDir", "EmitterCondition_BulletTrackingTarget", "EmitterCondition_BulletDestructive", "EmitterCondition_BulletDestructiveOnHit", "EmitterCondition_BulletHitEffect", "EmitterCondition_BulletScale", "EmitterCondition_BulletColorR", "EmitterCondition_BulletColorG", "EmitterCondition_BulletColorB", "EmitterCondition_BulletDefaultFacing", "EmitterCondition_PlayerActLevel", "EmitterCondition_PlayerPosX", "EmitterCondition_PlayerPosY", "EmitterCondition_PlayerLifePercent", "EmitterCondition_PlayerGainBuff", "Vec3", "EventConditionBase", "Comparer", "eCompareOp", "evaluate", "context", "data", "compareOp", "Equal", "emitter", "isActive", "value", "_targetValue", "NotEqual", "compare", "initialDelay", "isPreWarm", "preWarmDuration", "emitDuration", "elapsedTime", "isLoop", "loopInterval", "emitInterval", "perEmitCount", "perEmitInterval", "perEmitOffsetX", "angle", "count", "_evalValue", "onLoad", "bulletProp", "duration", "speed", "acceleration", "accelerationAngle", "isFacingMoveDir", "isTrackingTarget", "isDestructive", "isDestructiveOnHit", "scale", "color", "r", "g", "b", "defaultFacing", "_player<PERSON>os", "ZERO", "<PERSON><PERSON><PERSON>", "node", "getPosition", "x", "y", "hp_ratio", "curHp", "maxHp", "buff<PERSON><PERSON>p", "<PERSON><PERSON><PERSON>"], "mappings": ";;;+HAKaA,oB,EAOAC,uB,EAcAC,6B,EAMAC,wB,EAaAC,gC,EAOAC,yB,EAOAC,4B,EAMAC,qB,EAaAC,6B,EAMAC,6B,EAMAC,6B,EAMAC,gC,EAMAC,+B,EAMAC,sB,EAMAC,sB,EASAC,+B,EAaAC,4B,EAaAC,mC,EAaAC,wC,EAaAC,oC,EAaAC,qC,EAaAC,kC,EAaAC,uC,EAaAC,gC,EAaAC,4B,EAaAC,6B,EAaAC,6B,EAaAC,6B,EAaAC,oC,EAiBAC,+B,EAIAC,2B,EAaAC,2B,EAWAC,kC,EAWAC,+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA5VQC,MAAAA,I,OAAAA,I;;AACZC,MAAAA,kB,iBAAAA,kB;AAAoBC,MAAAA,Q,iBAAAA,Q;;AAEAC,MAAAA,U,iBAAAA,U;;;;;;;;;sCAEhBrC,oB,GAAN,MAAMA,oBAAN;AAAA;AAAA,oDAAsD,E,GAG7D;AACA;AACA;AACA;;;yCACaC,uB,GAAN,MAAMA,uBAAN,SAAsCD,oBAAtC,CAA2D;AACvDsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,kBAAQ,KAAKC,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWC,KAAhB;AACI,qBAAOH,OAAO,CAACI,OAAR,CAAiBC,QAAjB,CAA0BC,KAA1B,MAAqC,KAAKC,YAAL,KAAsB,CAA3D,IAAgE,IAAhE,GAAuE,KAA9E;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,QAAhB;AACI,qBAAOR,OAAO,CAACI,OAAR,CAAiBC,QAAjB,CAA0BC,KAA1B,MAAqC,KAAKC,YAAL,KAAsB,CAA3D,IAAgE,IAAhE,GAAuE,KAA9E;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAV6D,O,GAalE;;;+CACa5C,6B,GAAN,MAAMA,6BAAN,SAA4CF,oBAA5C,CAAiE;AAC7DsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,iBAAO;AAAA;AAAA,oCAASS,OAAT,CAAiBT,OAAO,CAACI,OAAR,CAAiBM,YAAjB,CAA8BJ,KAA/C,EAAsD,KAAKC,YAA3D,EAAyE,KAAKN,IAAL,CAAUC,SAAnF,CAAP;AACH;;AAHmE,O;;0CAM3DtC,wB,GAAN,MAAMA,wBAAN,SAAuCH,oBAAvC,CAA4D;AACxDsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,kBAAQ,KAAKC,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWC,KAAhB;AACI,qBAAOH,OAAO,CAACI,OAAR,CAAiBO,SAAjB,CAA2BL,KAA3B,MAAsC,KAAKC,YAAL,KAAsB,CAA5D,IAAiE,IAAjE,GAAwE,KAA/E;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,QAAhB;AACI,qBAAOR,OAAO,CAACI,OAAR,CAAiBO,SAAjB,CAA2BL,KAA3B,MAAsC,KAAKC,YAAL,KAAsB,CAA5D,IAAiE,IAAjE,GAAwE,KAA/E;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAV8D,O;;kDAatD1C,gC,GAAN,MAAMA,gCAAN,SAA+CJ,oBAA/C,CAAoE;AAChEsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,iBAAO;AAAA;AAAA,oCAASS,OAAT,CAAiBT,OAAO,CAACI,OAAR,CAAiBQ,eAAjB,CAAiCN,KAAlD,EAAyD,KAAKC,YAA9D,EAA4E,KAAKN,IAAL,CAAUC,SAAtF,CAAP;AACH;;AAHsE,O,GAM3E;;;2CACapC,yB,GAAN,MAAMA,yBAAN,SAAwCL,oBAAxC,CAA6D;AACzDsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,iBAAO;AAAA;AAAA,oCAASS,OAAT,CAAiBT,OAAO,CAACI,OAAR,CAAiBS,YAAjB,CAA8BP,KAA/C,EAAsD,KAAKC,YAA3D,EAAyE,KAAKN,IAAL,CAAUC,SAAnF,CAAP;AACH;;AAH+D,O,GAMpE;;;8CACanC,4B,GAAN,MAAMA,4BAAN,SAA2CN,oBAA3C,CAAgE;AAC5DsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,iBAAO;AAAA;AAAA,oCAASS,OAAT,CAAiBT,OAAO,CAACI,OAAR,CAAiBU,WAAjB,CAA6BR,KAA9C,EAAqD,KAAKC,YAA1D,EAAwE,KAAKN,IAAL,CAAUC,SAAlF,CAAP;AACH;;AAHkE,O;;uCAM1DlC,qB,GAAN,MAAMA,qBAAN,SAAoCP,oBAApC,CAAyD;AACrDsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,kBAAQ,KAAKC,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWC,KAAhB;AACI,qBAAOH,OAAO,CAACI,OAAR,CAAiBW,MAAjB,CAAwBT,KAAxB,MAAmC,KAAKC,YAAL,KAAsB,CAAzD,IAA8D,IAA9D,GAAqE,KAA5E;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,QAAhB;AACI,qBAAOR,OAAO,CAACI,OAAR,CAAiBW,MAAjB,CAAwBT,KAAxB,MAAmC,KAAKC,YAAL,KAAsB,CAAzD,IAA8D,IAA9D,GAAqE,KAA5E;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAV2D,O;;+CAanDtC,6B,GAAN,MAAMA,6BAAN,SAA4CR,oBAA5C,CAAiE;AAC7DsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,iBAAO;AAAA;AAAA,oCAASS,OAAT,CAAiBT,OAAO,CAACI,OAAR,CAAiBY,YAAjB,CAA8BV,KAA/C,EAAsD,KAAKC,YAA3D,EAAyE,KAAKN,IAAL,CAAUC,SAAnF,CAAP;AACH;;AAHmE,O;;+CAM3DhC,6B,GAAN,MAAMA,6BAAN,SAA4CT,oBAA5C,CAAiE;AAC7DsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,iBAAO;AAAA;AAAA,oCAASS,OAAT,CAAiBT,OAAO,CAACI,OAAR,CAAiBa,YAAjB,CAA8BX,KAA/C,EAAsD,KAAKC,YAA3D,EAAyE,KAAKN,IAAL,CAAUC,SAAnF,CAAP;AACH;;AAHmE,O;;+CAM3D/B,6B,GAAN,MAAMA,6BAAN,SAA4CV,oBAA5C,CAAiE;AAC7DsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,iBAAO;AAAA;AAAA,oCAASS,OAAT,CAAiBT,OAAO,CAACI,OAAR,CAAiBc,YAAjB,CAA8BZ,KAA/C,EAAsD,KAAKC,YAA3D,EAAyE,KAAKN,IAAL,CAAUC,SAAnF,CAAP;AACH;;AAHmE,O;;kDAM3D9B,gC,GAAN,MAAMA,gCAAN,SAA+CX,oBAA/C,CAAoE;AAChEsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,iBAAO;AAAA;AAAA,oCAASS,OAAT,CAAiBT,OAAO,CAACI,OAAR,CAAiBe,eAAjB,CAAiCb,KAAlD,EAAyD,KAAKC,YAA9D,EAA4E,KAAKN,IAAL,CAAUC,SAAtF,CAAP;AACH;;AAHsE,O;;iDAM9D7B,+B,GAAN,MAAMA,+BAAN,SAA8CZ,oBAA9C,CAAmE;AAC/DsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,iBAAO;AAAA;AAAA,oCAASS,OAAT,CAAiBT,OAAO,CAACI,OAAR,CAAiBgB,cAAjB,CAAgCd,KAAjD,EAAwD,KAAKC,YAA7D,EAA2E,KAAKN,IAAL,CAAUC,SAArF,CAAP;AACH;;AAHqE,O;;wCAM7D5B,sB,GAAN,MAAMA,sBAAN,SAAqCb,oBAArC,CAA0D;AACtDsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,iBAAO;AAAA;AAAA,oCAASS,OAAT,CAAiBT,OAAO,CAACI,OAAR,CAAiBiB,KAAjB,CAAuBf,KAAxC,EAA+C,KAAKC,YAApD,EAAkE,KAAKN,IAAL,CAAUC,SAA5E,CAAP;AACH;;AAH4D,O;;wCAMpD3B,sB,GAAN,MAAMA,sBAAN,SAAqCd,oBAArC,CAA0D;AACtDsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,iBAAO;AAAA;AAAA,oCAASS,OAAT,CAAiBT,OAAO,CAACI,OAAR,CAAiBkB,KAAjB,CAAuBhB,KAAxC,EAA+C,KAAKC,YAApD,EAAkE,KAAKN,IAAL,CAAUC,SAA5E,CAAP;AACH;;AAH4D,O,GAMjE;AACA;AACA;;;iDACa1B,+B,GAAN,MAAMA,+BAAN,SAA8Cf,oBAA9C,CAAmE;AAAA;AAAA;AAAA,eAC9D8D,UAD8D,GACzC,CADyC;AAAA;;AAG/DC,QAAAA,MAAM,CAACxB,OAAD,EAAoC;AAC7C,gBAAMwB,MAAN,CAAaxB,OAAb;AACA,eAAKuB,UAAL,GAAkBvB,OAAO,CAACI,OAAR,CAAiBqB,UAAjB,CAA4BC,QAA5B,CAAqCpB,KAAvD;AACH;;AAEMP,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,iBAAO;AAAA;AAAA,oCAASS,OAAT,CAAiB,KAAKc,UAAtB,EAAkC,KAAKhB,YAAvC,EAAqD,KAAKN,IAAL,CAAUC,SAA/D,CAAP;AACH;;AAVqE,O;;8CAa7DzB,4B,GAAN,MAAMA,4BAAN,SAA2ChB,oBAA3C,CAAgE;AAAA;AAAA;AAAA,eAC3D8D,UAD2D,GACtC,CADsC;AAAA;;AAG5DC,QAAAA,MAAM,CAACxB,OAAD,EAAoC;AAC7C,gBAAMwB,MAAN,CAAaxB,OAAb;AACA,eAAKuB,UAAL,GAAkBvB,OAAO,CAACI,OAAR,CAAiBqB,UAAjB,CAA4BE,KAA5B,CAAkCrB,KAApD;AACH;;AAEMP,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,iBAAO;AAAA;AAAA,oCAASS,OAAT,CAAiB,KAAKc,UAAtB,EAAkC,KAAKhB,YAAvC,EAAqD,KAAKN,IAAL,CAAUC,SAA/D,CAAP;AACH;;AAVkE,O;;qDAa1DxB,mC,GAAN,MAAMA,mCAAN,SAAkDjB,oBAAlD,CAAuE;AAAA;AAAA;AAAA,eAClE8D,UADkE,GAC7C,CAD6C;AAAA;;AAGnEC,QAAAA,MAAM,CAACxB,OAAD,EAAoC;AAC7C,gBAAMwB,MAAN,CAAaxB,OAAb;AACA,eAAKuB,UAAL,GAAkBvB,OAAO,CAACI,OAAR,CAAiBqB,UAAjB,CAA4BG,YAA5B,CAAyCtB,KAA3D;AACH;;AAEMP,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,iBAAO;AAAA;AAAA,oCAASS,OAAT,CAAiB,KAAKc,UAAtB,EAAkC,KAAKhB,YAAvC,EAAqD,KAAKN,IAAL,CAAUC,SAA/D,CAAP;AACH;;AAVyE,O;;0DAajEvB,wC,GAAN,MAAMA,wCAAN,SAAuDlB,oBAAvD,CAA4E;AAAA;AAAA;AAAA,eACvE8D,UADuE,GAClD,CADkD;AAAA;;AAGxEC,QAAAA,MAAM,CAACxB,OAAD,EAAoC;AAC7C,gBAAMwB,MAAN,CAAaxB,OAAb;AACA,eAAKuB,UAAL,GAAkBvB,OAAO,CAACI,OAAR,CAAiBqB,UAAjB,CAA4BI,iBAA5B,CAA8CvB,KAAhE;AACH;;AAEMP,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,iBAAO;AAAA;AAAA,oCAASS,OAAT,CAAiB,KAAKc,UAAtB,EAAkC,KAAKhB,YAAvC,EAAqD,KAAKN,IAAL,CAAUC,SAA/D,CAAP;AACH;;AAV8E,O;;sDAatEtB,oC,GAAN,MAAMA,oCAAN,SAAmDnB,oBAAnD,CAAwE;AACpEsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,kBAAQ,KAAKC,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWC,KAAhB;AACI,qBAAOH,OAAO,CAACI,OAAR,CAAiBqB,UAAjB,CAA4BK,eAA5B,CAA4CxB,KAA5C,MAAuD,KAAKC,YAAL,KAAsB,CAA7E,IAAkF,IAAlF,GAAyF,KAAhG;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,QAAhB;AACI,qBAAOR,OAAO,CAACI,OAAR,CAAiBqB,UAAjB,CAA4BK,eAA5B,CAA4CxB,KAA5C,MAAuD,KAAKC,YAAL,KAAsB,CAA7E,IAAkF,IAAlF,GAAyF,KAAhG;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAV0E,O;;uDAalE1B,qC,GAAN,MAAMA,qCAAN,SAAoDpB,oBAApD,CAAyE;AACrEsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,kBAAQ,KAAKC,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWC,KAAhB;AACI,qBAAOH,OAAO,CAACI,OAAR,CAAiBqB,UAAjB,CAA4BM,gBAA5B,CAA6CzB,KAA7C,MAAwD,KAAKC,YAAL,KAAsB,CAA9E,IAAmF,IAAnF,GAA0F,KAAjG;;AACA,iBAAK;AAAA;AAAA,0CAAWC,QAAhB;AACA,qBAAOR,OAAO,CAACI,OAAR,CAAiBqB,UAAjB,CAA4BM,gBAA5B,CAA6CzB,KAA7C,MAAwD,KAAKC,YAAL,KAAsB,CAA9E,IAAmF,IAAnF,GAA0F,KAAjG;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAV2E,O;;oDAanEzB,kC,GAAN,MAAMA,kCAAN,SAAiDrB,oBAAjD,CAAsE;AAClEsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,kBAAQ,KAAKC,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWC,KAAhB;AACI,qBAAOH,OAAO,CAACI,OAAR,CAAiBqB,UAAjB,CAA4BO,aAA5B,CAA0C1B,KAA1C,MAAqD,KAAKC,YAAL,KAAsB,CAA3E,IAAgF,IAAhF,GAAuF,KAA9F;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,QAAhB;AACI,qBAAOR,OAAO,CAACI,OAAR,CAAiBqB,UAAjB,CAA4BO,aAA5B,CAA0C1B,KAA1C,MAAqD,KAAKC,YAAL,KAAsB,CAA3E,IAAgF,IAAhF,GAAuF,KAA9F;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAVwE,O;;yDAahExB,uC,GAAN,MAAMA,uCAAN,SAAsDtB,oBAAtD,CAA2E;AACvEsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,kBAAQ,KAAKC,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWC,KAAhB;AACI,qBAAOH,OAAO,CAACI,OAAR,CAAiBqB,UAAjB,CAA4BQ,kBAA5B,CAA+C3B,KAA/C,MAA0D,KAAKC,YAAL,KAAsB,CAAhF,IAAqF,IAArF,GAA4F,KAAnG;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,QAAhB;AACI,qBAAOR,OAAO,CAACI,OAAR,CAAiBqB,UAAjB,CAA4BQ,kBAA5B,CAA+C3B,KAA/C,MAA0D,KAAKC,YAAL,KAAsB,CAAhF,IAAqF,IAArF,GAA4F,KAAnG;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAV6E,O;;kDAarEvB,gC,GAAN,MAAMA,gCAAN,SAA+CvB,oBAA/C,CAAoE;AAChEsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,kBAAQ,KAAKC,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWC,KAAhB,CADJ,CAEQ;;AACJ,iBAAK;AAAA;AAAA,0CAAWK,QAAhB,CAHJ,CAIQ;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAVsE,O;;8CAa9DvB,4B,GAAN,MAAMA,4BAAN,SAA2CxB,oBAA3C,CAAgE;AAAA;AAAA;AAAA,eAC3D8D,UAD2D,GACtC,CADsC;AAAA;;AAG5DC,QAAAA,MAAM,CAACxB,OAAD,EAAoC;AAC7C,gBAAMwB,MAAN,CAAaxB,OAAb;AACA,eAAKuB,UAAL,GAAkBvB,OAAO,CAACI,OAAR,CAAiBqB,UAAjB,CAA4BS,KAA5B,CAAkC5B,KAApD;AACH;;AAEMP,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,iBAAO;AAAA;AAAA,oCAASS,OAAT,CAAiB,KAAKc,UAAtB,EAAkC,KAAKhB,YAAvC,EAAqD,KAAKN,IAAL,CAAUC,SAA/D,CAAP;AACH;;AAVkE,O;;+CAa1DhB,6B,GAAN,MAAMA,6BAAN,SAA4CzB,oBAA5C,CAAiE;AAAA;AAAA;AAAA,eAC5D8D,UAD4D,GACvC,CADuC;AAAA;;AAG7DC,QAAAA,MAAM,CAACxB,OAAD,EAAoC;AAC7C,gBAAMwB,MAAN,CAAaxB,OAAb;AACA,eAAKuB,UAAL,GAAkBvB,OAAO,CAACI,OAAR,CAAiBqB,UAAjB,CAA4BU,KAA5B,CAAkC7B,KAAlC,CAAwC8B,CAA1D;AACH;;AAEMrC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,iBAAO;AAAA;AAAA,oCAASS,OAAT,CAAiB,KAAKc,UAAtB,EAAkC,KAAKhB,YAAvC,EAAqD,KAAKN,IAAL,CAAUC,SAA/D,CAAP;AACH;;AAVmE,O;;+CAa3Df,6B,GAAN,MAAMA,6BAAN,SAA4C1B,oBAA5C,CAAiE;AAAA;AAAA;AAAA,eAC5D8D,UAD4D,GACvC,CADuC;AAAA;;AAG7DC,QAAAA,MAAM,CAACxB,OAAD,EAAoC;AAC7C,gBAAMwB,MAAN,CAAaxB,OAAb;AACA,eAAKuB,UAAL,GAAkBvB,OAAO,CAACI,OAAR,CAAiBqB,UAAjB,CAA4BU,KAA5B,CAAkC7B,KAAlC,CAAwC+B,CAA1D;AACH;;AAEMtC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,iBAAO;AAAA;AAAA,oCAASS,OAAT,CAAiB,KAAKc,UAAtB,EAAkC,KAAKhB,YAAvC,EAAqD,KAAKN,IAAL,CAAUC,SAA/D,CAAP;AACH;;AAVmE,O;;+CAa3Dd,6B,GAAN,MAAMA,6BAAN,SAA4C3B,oBAA5C,CAAiE;AAAA;AAAA;AAAA,eAC5D8D,UAD4D,GACvC,CADuC;AAAA;;AAG7DC,QAAAA,MAAM,CAACxB,OAAD,EAAoC;AAC7C,gBAAMwB,MAAN,CAAaxB,OAAb;AACA,eAAKuB,UAAL,GAAkBvB,OAAO,CAACI,OAAR,CAAiBqB,UAAjB,CAA4BU,KAA5B,CAAkC7B,KAAlC,CAAwCgC,CAA1D;AACH;;AAEMvC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,iBAAO;AAAA;AAAA,oCAASS,OAAT,CAAiB,KAAKc,UAAtB,EAAkC,KAAKhB,YAAvC,EAAqD,KAAKN,IAAL,CAAUC,SAA/D,CAAP;AACH;;AAVmE,O;;sDAa3Db,oC,GAAN,MAAMA,oCAAN,SAAmD5B,oBAAnD,CAAwE;AACpEsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,kBAAQ,KAAKC,IAAL,CAAUC,SAAlB;AACI,iBAAK;AAAA;AAAA,0CAAWC,KAAhB;AACI,qBAAOH,OAAO,CAACI,OAAR,CAAiBqB,UAAjB,CAA4Bc,aAA5B,CAA0CjC,KAA1C,KAAoD,KAAKC,YAAhE;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,QAAhB;AACI,qBAAOR,OAAO,CAACI,OAAR,CAAiBqB,UAAjB,CAA4Bc,aAA5B,CAA0CjC,KAA1C,KAAoD,KAAKC,YAAhE;;AACJ;AACI,qBAAO,KAAP;AANR;AAQH;;AAV0E,O,GAa/E;AACA;AACA;AACA;;;iDACajB,+B,GAAN,MAAMA,+BAAN,SAA8C7B,oBAA9C,CAAmE,CACtE;AADsE,O;;6CAI7D8B,2B,GAAN,MAAMA,2BAAN,SAA0C9B,oBAA1C,CAA+D;AAAA;AAAA;AAAA,eAClE+E,UADkE,GAC/C7C,IAAI,CAAC8C,IAD0C;AAAA;;AAG3D1C,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,cAAIA,OAAO,CAAC0C,WAAR,KAAwB,IAA5B,EAAkC;AAC9B,mBAAO,KAAP;AACH;;AAED1C,UAAAA,OAAO,CAAC0C,WAAR,CAAoBC,IAApB,CAAyBC,WAAzB,CAAqC,KAAKJ,UAA1C;AACA,iBAAO;AAAA;AAAA,oCAAS/B,OAAT,CAAiB,KAAK+B,UAAL,CAAgBK,CAAjC,EAAoC,KAAKtC,YAAzC,EAAuD,KAAKN,IAAL,CAAUC,SAAjE,CAAP;AACH;;AAViE,O;;6CAazDV,2B,GAAN,MAAMA,2BAAN,SAA0CD,2BAA1C,CAAsE;AAClEQ,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,cAAIA,OAAO,CAAC0C,WAAR,KAAwB,IAA5B,EAAkC;AAC9B,mBAAO,KAAP;AACH;;AAED1C,UAAAA,OAAO,CAAC0C,WAAR,CAAoBC,IAApB,CAAyBC,WAAzB,CAAqC,KAAKJ,UAA1C;AACA,iBAAO;AAAA;AAAA,oCAAS/B,OAAT,CAAiB,KAAK+B,UAAL,CAAgBM,CAAjC,EAAoC,KAAKvC,YAAzC,EAAuD,KAAKN,IAAL,CAAUC,SAAjE,CAAP;AACH;;AARwE,O;;oDAWhET,kC,GAAN,MAAMA,kCAAN,SAAiDhC,oBAAjD,CAAsE;AAClEsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAClD,cAAIA,OAAO,CAAC0C,WAAR,KAAwB,IAA5B,EAAkC;AAC9B,mBAAO,KAAP;AACH;;AAED,cAAMK,QAAQ,GAAG/C,OAAO,CAAC0C,WAAR,CAAoBM,KAApB,GAA4BhD,OAAO,CAAC0C,WAAR,CAAoBO,KAAhD,GAAwD,GAAzE;AACA,iBAAO;AAAA;AAAA,oCAASxC,OAAT,CAAiBsC,QAAjB,EAA2B,KAAKxC,YAAhC,EAA8C,KAAKN,IAAL,CAAUC,SAAxD,CAAP;AACH;;AARwE,O;;iDAWhER,+B,GAAN,MAAMA,+BAAN,SAA8CjC,oBAA9C,CAAmE;AAC/DsC,QAAAA,QAAQ,CAACC,OAAD,EAAuC;AAAA;;AAClD,cAAIA,OAAO,CAAC0C,WAAR,KAAwB,IAA5B,EAAkC;AAC9B,mBAAO,KAAP;AACH;;AAED,0CAAO1C,OAAO,CAAC0C,WAAR,CAAoBQ,QAA3B,qBAAO,sBAA8BC,OAA9B,CAAsC,KAAK5C,YAA3C,CAAP;AACH;;AAPqE,O", "sourcesContent": ["import { _decorator, Vec3 } from \"cc\";\r\nimport { EventConditionBase, Comparer } from \"db://assets/bundles/common/script/game/eventgroup/IEventCondition\";\r\nimport { IEventGroupContext } from \"db://assets/bundles/common/script/game/eventgroup/IEventGroupContext\";\r\nimport { EventConditionData, eCompareOp, eConditionOp } from \"../../data/bullet/EventGroupData\";\r\n\r\nexport class EmitterConditionBase extends EventConditionBase {\r\n}\r\n\r\n/////////////////////////////////////////////////////////////////////////////////\r\n// 以下是发射器相关参数\r\n/////////////////////////////////////////////////////////////////////////////////\r\n// 发射器是否启用\r\nexport class EmitterCondition_Active extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.emitter!.isActive.value === (this._targetValue === 1) ? true : false;\r\n            case eCompareOp.NotEqual:\r\n                return context.emitter!.isActive.value !== (this._targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\n// 发射器初始延迟时间\r\nexport class EmitterCondition_InitialDelay extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.initialDelay.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_Prewarm extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.emitter!.isPreWarm.value === (this._targetValue === 1) ? true : false;\r\n            case eCompareOp.NotEqual:\r\n                return context.emitter!.isPreWarm.value !== (this._targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_PrewarmDuration extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.preWarmDuration.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\n// 发射器持续时间\r\nexport class EmitterCondition_Duration extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.emitDuration.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\n// 发射器已运行时间\r\nexport class EmitterCondition_ElapsedTime extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.elapsedTime.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_Loop extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.emitter!.isLoop.value === (this._targetValue === 1) ? true : false;\r\n            case eCompareOp.NotEqual:\r\n                return context.emitter!.isLoop.value !== (this._targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_LoopInterval extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.loopInterval.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_EmitInterval extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.emitInterval.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_PerEmitCount extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.perEmitCount.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_PerEmitInterval extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.perEmitInterval.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_PerEmitOffsetX extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.perEmitOffsetX.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_Angle extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.angle.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_Count extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        return Comparer.compare(context.emitter!.count.value, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\n/////////////////////////////////////////////////////////////////////////////////\r\n// 以下是发射器配置的子弹相关参数\r\n/////////////////////////////////////////////////////////////////////////////////\r\nexport class EmitterCondition_BulletDuration extends EmitterConditionBase {\r\n    private _evalValue: number = 0;\r\n    \r\n    public onLoad(context: IEventGroupContext): void {\r\n        super.onLoad(context);\r\n        this._evalValue = context.emitter!.bulletProp.duration.value;\r\n    }\r\n\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletSpeed extends EmitterConditionBase {\r\n    private _evalValue: number = 0;\r\n\r\n    public onLoad(context: IEventGroupContext): void {\r\n        super.onLoad(context);\r\n        this._evalValue = context.emitter!.bulletProp.speed.value;\r\n    }\r\n\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletAcceleration extends EmitterConditionBase {\r\n    private _evalValue: number = 0;\r\n\r\n    public onLoad(context: IEventGroupContext): void {\r\n        super.onLoad(context);\r\n        this._evalValue = context.emitter!.bulletProp.acceleration.value;\r\n    }\r\n\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletAccelerationAngle extends EmitterConditionBase {\r\n    private _evalValue: number = 0;\r\n\r\n    public onLoad(context: IEventGroupContext): void {\r\n        super.onLoad(context);\r\n        this._evalValue = context.emitter!.bulletProp.accelerationAngle.value;\r\n    }\r\n\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletFacingMoveDir extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.emitter!.bulletProp.isFacingMoveDir.value === (this._targetValue === 1) ? true : false;\r\n            case eCompareOp.NotEqual:\r\n                return context.emitter!.bulletProp.isFacingMoveDir.value !== (this._targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletTrackingTarget extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.emitter!.bulletProp.isTrackingTarget.value === (this._targetValue === 1) ? true : false;\r\n                case eCompareOp.NotEqual:\r\n                return context.emitter!.bulletProp.isTrackingTarget.value !== (this._targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletDestructive extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.emitter!.bulletProp.isDestructive.value === (this._targetValue === 1) ? true : false;\r\n            case eCompareOp.NotEqual:\r\n                return context.emitter!.bulletProp.isDestructive.value !== (this._targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletDestructiveOnHit extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.emitter!.bulletProp.isDestructiveOnHit.value === (this._targetValue === 1) ? true : false;\r\n            case eCompareOp.NotEqual:\r\n                return context.emitter!.bulletProp.isDestructiveOnHit.value !== (this._targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletHitEffect extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                // return context.emitter!.bulletData.hitEffect === (this._targetValue === 1) ? true : false;\r\n            case eCompareOp.NotEqual:\r\n                // return context.emitter!.bulletData.hitEffect !== (this._targetValue === 1) ? true : false;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletScale extends EmitterConditionBase {\r\n    private _evalValue: number = 1;\r\n\r\n    public onLoad(context: IEventGroupContext): void {\r\n        super.onLoad(context);\r\n        this._evalValue = context.emitter!.bulletProp.scale.value;\r\n    }\r\n\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletColorR extends EmitterConditionBase {\r\n    private _evalValue: number = 0;\r\n    \r\n    public onLoad(context: IEventGroupContext): void {\r\n        super.onLoad(context);\r\n        this._evalValue = context.emitter!.bulletProp.color.value.r;\r\n    }\r\n    \r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletColorG extends EmitterConditionBase {\r\n    private _evalValue: number = 0;\r\n    \r\n    public onLoad(context: IEventGroupContext): void {\r\n        super.onLoad(context);\r\n        this._evalValue = context.emitter!.bulletProp.color.value.g;\r\n    }\r\n    \r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletColorB extends EmitterConditionBase {\r\n    private _evalValue: number = 0;\r\n    \r\n    public onLoad(context: IEventGroupContext): void {\r\n        super.onLoad(context);\r\n        this._evalValue = context.emitter!.bulletProp.color.value.b;\r\n    }\r\n    \r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        return Comparer.compare(this._evalValue, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_BulletDefaultFacing extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        switch (this.data.compareOp) {\r\n            case eCompareOp.Equal:\r\n                return context.emitter!.bulletProp.defaultFacing.value === this._targetValue;\r\n            case eCompareOp.NotEqual:\r\n                return context.emitter!.bulletProp.defaultFacing.value !== this._targetValue;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}\r\n\r\n/////////////////////////////////////////////////////////////////////////////////\r\n// Player\r\n/////////////////////////////////////////////////////////////////////////////////\r\n// 玩家account等级\r\nexport class EmitterCondition_PlayerActLevel extends EmitterConditionBase {\r\n    // TODO:\r\n}\r\n\r\nexport class EmitterCondition_PlayerPosX extends EmitterConditionBase {\r\n    _playerPos: Vec3 = Vec3.ZERO;\r\n\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        if (context.playerPlane === null) {\r\n            return false;\r\n        }\r\n\r\n        context.playerPlane.node.getPosition(this._playerPos);\r\n        return Comparer.compare(this._playerPos.x, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_PlayerPosY extends EmitterCondition_PlayerPosX {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        if (context.playerPlane === null) {\r\n            return false;\r\n        }\r\n\r\n        context.playerPlane.node.getPosition(this._playerPos);\r\n        return Comparer.compare(this._playerPos.y, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_PlayerLifePercent extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        if (context.playerPlane === null) {\r\n            return false;\r\n        }\r\n\r\n        const hp_ratio = context.playerPlane.curHp / context.playerPlane.maxHp * 100;\r\n        return Comparer.compare(hp_ratio, this._targetValue, this.data.compareOp);\r\n    }\r\n}\r\n\r\nexport class EmitterCondition_PlayerGainBuff extends EmitterConditionBase {\r\n    public evaluate(context: IEventGroupContext): boolean {\r\n        if (context.playerPlane === null) {\r\n            return false;\r\n        }\r\n\r\n        return context.playerPlane.buffComp?.HasBuff(this._targetValue);\r\n    }\r\n}"]}