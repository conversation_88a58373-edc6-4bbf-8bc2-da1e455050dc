System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, TargetType, GameIns, _crd;

  function forEachEntityByTargetType(caster, targetType, callback) {
    switch (targetType) {
      case (_crd && TargetType === void 0 ? (_reportPossibleCrUseOfTargetType({
        error: Error()
      }), TargetType) : TargetType).Self:
        callback(caster);
        break;

      case (_crd && TargetType === void 0 ? (_reportPossibleCrUseOfTargetType({
        error: Error()
      }), TargetType) : TargetType).Main:
        callback((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
          error: Error()
        }), GameIns) : GameIns).mainPlaneManager.mainPlane);
        break;

      case (_crd && TargetType === void 0 ? (_reportPossibleCrUseOfTargetType({
        error: Error()
      }), TargetType) : TargetType).MainFriendly:
        callback((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
          error: Error()
        }), GameIns) : GameIns).mainPlaneManager.mainPlane);
        break;

      case (_crd && TargetType === void 0 ? (_reportPossibleCrUseOfTargetType({
        error: Error()
      }), TargetType) : TargetType).Enemy:
        (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
          error: Error()
        }), GameIns) : GameIns).enemyManager.enemies.forEach(plane => {
          callback(plane);
        });
        (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
          error: Error()
        }), GameIns) : GameIns).bossManager.bosses.forEach(boss => {
          // boss.getUnits().forEach((unit) => {
          //     callback(unit);
          // });
          callback(boss);
        });
        break;

      case (_crd && TargetType === void 0 ? (_reportPossibleCrUseOfTargetType({
        error: Error()
      }), TargetType) : TargetType).BossEnemy:
        (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
          error: Error()
        }), GameIns) : GameIns).bossManager.bosses.forEach(boss => {
          // boss.getUnits().forEach((unit) => {
          //     callback(unit);
          // });
          callback(boss);
        });
        break;

      case (_crd && TargetType === void 0 ? (_reportPossibleCrUseOfTargetType({
        error: Error()
      }), TargetType) : TargetType).NormalEnemy:
        (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
          error: Error()
        }), GameIns) : GameIns).enemyManager.enemies.forEach(plane => {
          callback(plane);
        });
        break;

      default:
        break;
    }
  }

  function _reportPossibleCrUseOfTargetType(extras) {
    _reporterNs.report("TargetType", "../../../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "../PlaneBase", _context.meta, extras);
  }

  _export("default", forEachEntityByTargetType);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      TargetType = _unresolved_2.TargetType;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "35896sQ8ZJDQK7e5RXbocrJ", "SearchTarget", undefined);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=819b22c167b407b02d87c4784e55214b704c63f9.js.map