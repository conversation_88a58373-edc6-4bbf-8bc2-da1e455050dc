{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/skill/SearchTarget.ts"], "names": ["forEachEntityByTargetType", "caster", "targetType", "callback", "Self", "Main", "mainPlaneManager", "mainPlane", "MainFriendly", "Enemy", "enemyManager", "enemies", "for<PERSON>ach", "plane", "boss<PERSON><PERSON><PERSON>", "bosses", "boss", "BossEnemy", "NormalEnemy", "TargetType", "GameIns"], "mappings": ";;;;;AAKe,WAASA,yBAAT,CAAmCC,MAAnC,EAAsDC,UAAtD,EAA8EC,QAA9E,EAAqH;AAC5H,YAAQD,UAAR;AACI,WAAK;AAAA;AAAA,oCAAWE,IAAhB;AACID,QAAAA,QAAQ,CAACF,MAAD,CAAR;AACA;;AACJ,WAAK;AAAA;AAAA,oCAAWI,IAAhB;AACIF,QAAAA,QAAQ,CAAC;AAAA;AAAA,gCAAQG,gBAAR,CAAyBC,SAA1B,CAAR;AACA;;AACJ,WAAK;AAAA;AAAA,oCAAWC,YAAhB;AACIL,QAAAA,QAAQ,CAAC;AAAA;AAAA,gCAAQG,gBAAR,CAAyBC,SAA1B,CAAR;AACA;;AACJ,WAAK;AAAA;AAAA,oCAAWE,KAAhB;AACI;AAAA;AAAA,gCAAQC,YAAR,CAAqBC,OAArB,CAA6BC,OAA7B,CAAsCC,KAAD,IAAW;AAC5CV,UAAAA,QAAQ,CAACU,KAAD,CAAR;AACH,SAFD;AAGA;AAAA;AAAA,gCAAQC,WAAR,CAAoBC,MAApB,CAA2BH,OAA3B,CAAoCI,IAAD,IAAU;AACzC;AACA;AACA;AACAb,UAAAA,QAAQ,CAACa,IAAD,CAAR;AACH,SALD;AAMA;;AACJ,WAAK;AAAA;AAAA,oCAAWC,SAAhB;AACI;AAAA;AAAA,gCAAQH,WAAR,CAAoBC,MAApB,CAA2BH,OAA3B,CAAoCI,IAAD,IAAU;AACzC;AACA;AACA;AACAb,UAAAA,QAAQ,CAACa,IAAD,CAAR;AACH,SALD;AAMA;;AACJ,WAAK;AAAA;AAAA,oCAAWE,WAAhB;AACI;AAAA;AAAA,gCAAQR,YAAR,CAAqBC,OAArB,CAA6BC,OAA7B,CAAsCC,KAAD,IAAW;AAC5CV,UAAAA,QAAQ,CAACU,KAAD,CAAR;AACH,SAFD;AAGA;;AACJ;AACI;AAnCR;AAqCH;;;;;;;;;;;;;;qBAtCmBb,yB;;;;;;;;AALfmB,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O", "sourcesContent": ["import { TargetType } from \"../../../../autogen/luban/schema\";\r\nimport { GameIns } from \"../../../GameIns\";\r\nimport type PlaneBase from \"../PlaneBase\";\r\n\r\n\r\nexport default function forEachEntityByTargetType(caster: PlaneBase, targetType: TargetType, callback: (entity: PlaneBase) => void) {\r\n        switch (targetType) {\r\n            case TargetType.Self:\r\n                callback(caster);\r\n                break;\r\n            case TargetType.Main:\r\n                callback(GameIns.mainPlaneManager.mainPlane!);\r\n                break;\r\n            case TargetType.MainFriendly:\r\n                callback(GameIns.mainPlaneManager.mainPlane!);\r\n                break;\r\n            case TargetType.Enemy:\r\n                GameIns.enemyManager.enemies.forEach((plane) => {\r\n                    callback(plane);\r\n                });\r\n                GameIns.bossManager.bosses.forEach((boss) => {\r\n                    // boss.getUnits().forEach((unit) => {\r\n                    //     callback(unit);\r\n                    // });\r\n                    callback(boss)\r\n                });\r\n                break;\r\n            case TargetType.BossEnemy:\r\n                GameIns.bossManager.bosses.forEach((boss) => {\r\n                    // boss.getUnits().forEach((unit) => {\r\n                    //     callback(unit);\r\n                    // });\r\n                    callback(boss)\r\n                });\r\n                break;\r\n            case TargetType.NormalEnemy:\r\n                GameIns.enemyManager.enemies.forEach((plane) => {\r\n                    callback(plane);\r\n                });\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    }"]}