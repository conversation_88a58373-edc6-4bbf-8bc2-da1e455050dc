{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/bullet/actions/EmitterEventActions.ts"], "names": ["EmitterActionBase", "EmitterActionBase_BoolModifier", "EmitterActionBase_NumberModifier", "EmitterAction_Active", "EmitterAction_Prewarm", "EmitterAction_InitialDelay", "EmitterAction_PrewarmDuration", "EmitterAction_Duration", "EmitterAction_ElapsedTime", "EmitterAction_Loop", "EmitterAction_LoopInterval", "EmitterAction_EmitInterval", "EmitterAction_PerEmitCount", "EmitterAction_PerEmitInterval", "EmitterAction_PerEmitOffsetX", "EmitterAction_Angle", "EmitterAction_Count", "EmitterAction_BulletDuration", "EmitterAction_BulletDamage", "EmitterAction_BulletSpeed", "EmitterAction_BulletSpeedAngle", "EmitterAction_BulletAcceleration", "EmitterAction_BulletAccelerationAngle", "EmitterAction_BulletScale", "EmitterAction_BulletColorR", "EmitterAction_BulletColorG", "EmitterAction_BulletColorB", "EmitterAction_BulletFacingMoveDir", "EmitterAction_BulletTrackingTarget", "EmitterAction_BulletDestructive", "EmitterAction_BulletDestructiveOnHit", "EventActionBase", "eEmitterProp", "ePropMask", "_targetProperty", "undefined", "propertyType", "IsActive", "onLoad", "context", "emitter", "getProperty", "onStart", "addWriteMask", "EventGroup", "onComplete", "removeWriteMask", "canLerp", "resetStartValue", "_startValue", "value", "onExecuteInternal", "setValue", "InitialDelay", "notify", "IsPreWarm", "PrewarmDuration", "EmitDuration", "ElapsedTime", "IsLoop", "LoopInterval", "EmitInterval", "PerEmitCount", "PerEmitInterval", "PerEmitOffsetX", "<PERSON><PERSON>", "Count", "bulletProp", "duration", "speed", "speedAngle", "acceleration", "accelerationAngle", "scale", "color", "r", "g", "b", "isFacingMoveDir", "isTrackingTarget", "isDestructive", "isDestructiveOnHit"], "mappings": ";;;wEAKaA,iB,EAIAC,8B,EA4BAC,gC,EA+BAC,oB,EAWAC,qB,EAOAC,0B,EAMAC,6B,EAOAC,sB,EAOAC,yB,EAOAC,kB,EAOAC,0B,EAMAC,0B,EAMAC,0B,EAMAC,6B,EAMAC,4B,EAMAC,mB,EAMAC,mB,EAOAC,4B,EAUAC,0B,EAUAC,yB,EAUAC,8B,EAUAC,gC,EAUAC,qC,EAUAC,yB,EAUAC,0B,EAYAC,0B,EAYAC,0B,EAYAC,iC,EAYAC,kC,EAYAC,+B,EAYAC,oC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAjTJC,MAAAA,e,iBAAAA,e;;AAEAC,MAAAA,Y,iBAAAA,Y;AAAcC,MAAAA,S,iBAAAA,S;;;;;;;mCAGVjC,iB,GAAN,MAAMA,iBAAN;AAAA;AAAA,8CAAgD,CACnD;AADmD,O;;gDAI1CC,8B,GAAN,MAAMA,8BAAN,SAA6CD,iBAA7C,CAA+D;AAAA;AAAA;AAAA,eAIxDkC,eAJwD,GAITC,SAJS;AAAA;;AACxC,YAAZC,YAAY,GAAiB;AACvC,iBAAO;AAAA;AAAA,4CAAaC,QAApB;AACH;;AAEDC,QAAAA,MAAM,CAACC,OAAD,EAAoC;AACtC,eAAKL,eAAL,GAAuBK,OAAO,CAACC,OAAR,CAAiBC,WAAjB,CAA6B,KAAKL,YAAlC,CAAvB;AACA,gBAAME,MAAN,CAAaC,OAAb;AACH;;AACDG,QAAAA,OAAO,CAACH,OAAD,EAAoC;AACvC,eAAKL,eAAL,CAAsBS,YAAtB,CAAmC;AAAA;AAAA,sCAAUC,UAA7C;;AACA,gBAAMF,OAAN,CAAcH,OAAd;AACH;;AACDM,QAAAA,UAAU,CAACN,OAAD,EAAoC;AAC1C,eAAKL,eAAL,CAAsBY,eAAtB,CAAsC;AAAA;AAAA,sCAAUF,UAAhD;;AACA,gBAAMC,UAAN,CAAiBN,OAAjB;AACH;;AACDQ,QAAAA,OAAO,GAAY;AACf,iBAAO,KAAP;AACH;;AACSC,QAAAA,eAAe,CAACT,OAAD,EAAoC;AACzD,eAAKU,WAAL,GAAmB,KAAKf,eAAL,CAAsBgB,KAAtB,GAA8B,CAA9B,GAAkC,CAArD;AACH;;AACSC,QAAAA,iBAAiB,CAACZ,OAAD,EAA8BW,KAA9B,EAAmD;AAC1E,eAAKhB,eAAL,CAAsBkB,QAAtB,CAA+BF,KAAK,KAAK,CAAzC,EAA4C;AAAA;AAAA,sCAAUN,UAAtD;AACH;;AAzBiE,O;;kDA4BzD1C,gC,GAAN,MAAMA,gCAAN,SAA+CF,iBAA/C,CAAiE;AAAA;AAAA;AAAA,eAK1DkC,eAL0D,GAKZC,SALY;AAAA;;AAC1C,YAAZC,YAAY,GAAiB;AACvC;AACA,iBAAO;AAAA;AAAA,4CAAaiB,YAApB;AACH;;AAEDf,QAAAA,MAAM,CAACC,OAAD,EAAoC;AACtC,eAAKL,eAAL,GAAuBK,OAAO,CAACC,OAAR,CAAiBC,WAAjB,CAA6B,KAAKL,YAAlC,CAAvB;AACA,gBAAME,MAAN,CAAaC,OAAb;AACH;;AAEDG,QAAAA,OAAO,CAACH,OAAD,EAAoC;AACvC,eAAKL,eAAL,CAAsBS,YAAtB,CAAmC;AAAA;AAAA,sCAAUC,UAA7C;;AACA,gBAAMF,OAAN,CAAcH,OAAd;AACH;;AAEDM,QAAAA,UAAU,CAACN,OAAD,EAAoC;AAC1C,eAAKL,eAAL,CAAsBY,eAAtB,CAAsC;AAAA;AAAA,sCAAUF,UAAhD;;AACA,gBAAMC,UAAN,CAAiBN,OAAjB;AACH;;AAESS,QAAAA,eAAe,CAACT,OAAD,EAAoC;AACzD,eAAKU,WAAL,GAAmB,KAAKf,eAAL,CAAsBgB,KAAzC;AACH;;AAESC,QAAAA,iBAAiB,CAACZ,OAAD,EAA8BW,KAA9B,EAAmD;AAC1E,eAAKhB,eAAL,CAAsBkB,QAAtB,CAA+BF,KAA/B,EAAsC;AAAA;AAAA,sCAAUN,UAAhD;AACH;;AA3BmE,O,GA8BxE;;;sCACazC,oB,GAAN,MAAMA,oBAAN,SAAmCF,8BAAnC,CAAkE;AAC3C,YAAZmC,YAAY,GAAiB;AACvC,iBAAO;AAAA;AAAA,4CAAaC,QAApB;AACH;;AAESc,QAAAA,iBAAiB,CAACZ,OAAD,EAA8BW,KAA9B,EAAmD;AAC1E,gBAAMC,iBAAN,CAAwBZ,OAAxB,EAAiCW,KAAjC;;AACA,eAAKhB,eAAL,CAAsBoB,MAAtB;AACH;;AARoE,O;;uCAW5DlD,qB,GAAN,MAAMA,qBAAN,SAAoCH,8BAApC,CAAmE;AAC5C,YAAZmC,YAAY,GAAiB;AACvC,iBAAO;AAAA;AAAA,4CAAamB,SAApB;AACH;;AAHqE,O,GAM1E;;;4CACalD,0B,GAAN,MAAMA,0BAAN,SAAyCH,gCAAzC,CAA0E;AACnD,YAAZkC,YAAY,GAAiB;AACvC,iBAAO;AAAA;AAAA,4CAAaiB,YAApB;AACH;;AAH4E,O;;+CAMpE/C,6B,GAAN,MAAMA,6BAAN,SAA4CJ,gCAA5C,CAA6E;AACtD,YAAZkC,YAAY,GAAiB;AACvC,iBAAO;AAAA;AAAA,4CAAaoB,eAApB;AACH;;AAH+E,O,GAMpF;;;wCACajD,sB,GAAN,MAAMA,sBAAN,SAAqCL,gCAArC,CAAsE;AAC/C,YAAZkC,YAAY,GAAiB;AACvC,iBAAO;AAAA;AAAA,4CAAaqB,YAApB;AACH;;AAHwE,O,GAM7E;;;2CACajD,yB,GAAN,MAAMA,yBAAN,SAAwCN,gCAAxC,CAAyE;AAClD,YAAZkC,YAAY,GAAiB;AACvC,iBAAO;AAAA;AAAA,4CAAasB,WAApB;AACH;;AAH2E,O,GAMhF;;;oCACajD,kB,GAAN,MAAMA,kBAAN,SAAiCR,8BAAjC,CAAgE;AACzC,YAAZmC,YAAY,GAAiB;AACvC,iBAAO;AAAA;AAAA,4CAAauB,MAApB;AACH;;AAHkE,O,GAMvE;;;4CACajD,0B,GAAN,MAAMA,0BAAN,SAAyCR,gCAAzC,CAA0E;AACnD,YAAZkC,YAAY,GAAiB;AACvC,iBAAO;AAAA;AAAA,4CAAawB,YAApB;AACH;;AAH4E,O;;4CAMpEjD,0B,GAAN,MAAMA,0BAAN,SAAyCT,gCAAzC,CAA0E;AACnD,YAAZkC,YAAY,GAAiB;AACvC,iBAAO;AAAA;AAAA,4CAAayB,YAApB;AACH;;AAH4E,O;;4CAMpEjD,0B,GAAN,MAAMA,0BAAN,SAAyCV,gCAAzC,CAA0E;AACnD,YAAZkC,YAAY,GAAiB;AACvC,iBAAO;AAAA;AAAA,4CAAa0B,YAApB;AACH;;AAH4E,O;;+CAMpEjD,6B,GAAN,MAAMA,6BAAN,SAA4CX,gCAA5C,CAA6E;AACtD,YAAZkC,YAAY,GAAiB;AACvC,iBAAO;AAAA;AAAA,4CAAa2B,eAApB;AACH;;AAH+E,O;;8CAMvEjD,4B,GAAN,MAAMA,4BAAN,SAA2CZ,gCAA3C,CAA4E;AACrD,YAAZkC,YAAY,GAAiB;AACvC,iBAAO;AAAA;AAAA,4CAAa4B,cAApB;AACH;;AAH8E,O;;qCAMtEjD,mB,GAAN,MAAMA,mBAAN,SAAkCb,gCAAlC,CAAmE;AAC5C,YAAZkC,YAAY,GAAiB;AACvC,iBAAO;AAAA;AAAA,4CAAa6B,KAApB;AACH;;AAHqE,O;;qCAM7DjD,mB,GAAN,MAAMA,mBAAN,SAAkCd,gCAAlC,CAAmE;AAC5C,YAAZkC,YAAY,GAAiB;AACvC,iBAAO;AAAA;AAAA,4CAAa8B,KAApB;AACH;;AAHqE,O,GAM1E;;;8CACajD,4B,GAAN,MAAMA,4BAAN,SAA2CjB,iBAA3C,CAA6D;AACtDgD,QAAAA,eAAe,CAACT,OAAD,EAAoC;AACzD,eAAKU,WAAL,GAAmBV,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BC,QAA5B,CAAqClB,KAAxD;AACH;;AAESC,QAAAA,iBAAiB,CAACZ,OAAD,EAA8BW,KAA9B,EAAmD;AAC1EX,UAAAA,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BC,QAA5B,CAAqClB,KAArC,GAA6CA,KAA7C;AACH;;AAP+D,O;;4CAUvDhC,0B,GAAN,MAAMA,0BAAN,SAAyClB,iBAAzC,CAA2D;AACpDgD,QAAAA,eAAe,CAACT,OAAD,EAAoC,CACzD;AACH;;AAESY,QAAAA,iBAAiB,CAACZ,OAAD,EAA8BW,KAA9B,EAAmD,CAC1E;AACH;;AAP6D,O;;2CAUrD/B,yB,GAAN,MAAMA,yBAAN,SAAwCnB,iBAAxC,CAA0D;AACnDgD,QAAAA,eAAe,CAACT,OAAD,EAAoC;AACzD,eAAKU,WAAL,GAAmBV,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BE,KAA5B,CAAkCnB,KAArD;AACH;;AAESC,QAAAA,iBAAiB,CAACZ,OAAD,EAA8BW,KAA9B,EAAmD;AAC1EX,UAAAA,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BE,KAA5B,CAAkCnB,KAAlC,GAA0CA,KAA1C;AACH;;AAP4D,O;;gDAUpD9B,8B,GAAN,MAAMA,8BAAN,SAA6CpB,iBAA7C,CAA+D;AACxDgD,QAAAA,eAAe,CAACT,OAAD,EAAoC;AACzD,eAAKU,WAAL,GAAmBV,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BG,UAA5B,CAAuCpB,KAA1D;AACH;;AAESC,QAAAA,iBAAiB,CAACZ,OAAD,EAA8BW,KAA9B,EAAmD;AAC1EX,UAAAA,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BG,UAA5B,CAAuCpB,KAAvC,GAA+CA,KAA/C;AACH;;AAPiE,O;;kDAUzD7B,gC,GAAN,MAAMA,gCAAN,SAA+CrB,iBAA/C,CAAiE;AAC1DgD,QAAAA,eAAe,CAACT,OAAD,EAAoC;AACzD,eAAKU,WAAL,GAAmBV,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BI,YAA5B,CAAyCrB,KAA5D;AACH;;AAESC,QAAAA,iBAAiB,CAACZ,OAAD,EAA8BW,KAA9B,EAAmD;AAC1EX,UAAAA,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BI,YAA5B,CAAyCrB,KAAzC,GAAiDA,KAAjD;AACH;;AAPmE,O;;uDAU3D5B,qC,GAAN,MAAMA,qCAAN,SAAoDtB,iBAApD,CAAsE;AAC/DgD,QAAAA,eAAe,CAACT,OAAD,EAAoC;AACzD,eAAKU,WAAL,GAAmBV,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BK,iBAA5B,CAA8CtB,KAAjE;AACH;;AAESC,QAAAA,iBAAiB,CAACZ,OAAD,EAA8BW,KAA9B,EAAmD;AAC1EX,UAAAA,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BK,iBAA5B,CAA8CtB,KAA9C,GAAsDA,KAAtD;AACH;;AAPwE,O;;2CAUhE3B,yB,GAAN,MAAMA,yBAAN,SAAwCvB,iBAAxC,CAA0D;AACnDgD,QAAAA,eAAe,CAACT,OAAD,EAAoC;AACzD,eAAKU,WAAL,GAAmBV,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BM,KAA5B,CAAkCvB,KAArD;AACH;;AAESC,QAAAA,iBAAiB,CAACZ,OAAD,EAA8BW,KAA9B,EAAmD;AAC1EX,UAAAA,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BM,KAA5B,CAAkCvB,KAAlC,GAA0CA,KAA1C;AACH;;AAP4D,O;;4CAUpD1B,0B,GAAN,MAAMA,0BAAN,SAAyCxB,iBAAzC,CAA2D;AACpDgD,QAAAA,eAAe,CAACT,OAAD,EAAoC;AACzD,eAAKU,WAAL,GAAmBV,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BO,KAA5B,CAAkCxB,KAAlC,CAAwCyB,CAA3D;AACH;;AAESxB,QAAAA,iBAAiB,CAACZ,OAAD,EAA8BW,KAA9B,EAAmD;AAC1E,cAAIwB,KAAK,GAAGnC,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BO,KAA5B,CAAkCxB,KAA9C;AACAwB,UAAAA,KAAK,CAACC,CAAN,GAAUzB,KAAV;AACAX,UAAAA,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BO,KAA5B,CAAkCxB,KAAlC,GAA0CwB,KAA1C;AACH;;AAT6D,O;;4CAYrDjD,0B,GAAN,MAAMA,0BAAN,SAAyCzB,iBAAzC,CAA2D;AACpDgD,QAAAA,eAAe,CAACT,OAAD,EAAoC;AACzD,eAAKU,WAAL,GAAmBV,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BO,KAA5B,CAAkCxB,KAAlC,CAAwC0B,CAA3D;AACH;;AAESzB,QAAAA,iBAAiB,CAACZ,OAAD,EAA8BW,KAA9B,EAAmD;AAC1E,cAAIwB,KAAK,GAAGnC,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BO,KAA5B,CAAkCxB,KAA9C;AACAwB,UAAAA,KAAK,CAACE,CAAN,GAAU1B,KAAV;AACAX,UAAAA,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BO,KAA5B,CAAkCxB,KAAlC,GAA0CwB,KAA1C;AACH;;AAT6D,O;;4CAYrDhD,0B,GAAN,MAAMA,0BAAN,SAAyC1B,iBAAzC,CAA2D;AACpDgD,QAAAA,eAAe,CAACT,OAAD,EAAoC;AACzD,eAAKU,WAAL,GAAmBV,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BO,KAA5B,CAAkCxB,KAAlC,CAAwC2B,CAA3D;AACH;;AAES1B,QAAAA,iBAAiB,CAACZ,OAAD,EAA8BW,KAA9B,EAAmD;AAC1E,cAAIwB,KAAK,GAAGnC,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BO,KAA5B,CAAkCxB,KAA9C;AACAwB,UAAAA,KAAK,CAACG,CAAN,GAAU3B,KAAV;AACAX,UAAAA,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BO,KAA5B,CAAkCxB,KAAlC,GAA0CwB,KAA1C;AACH;;AAT6D,O;;mDAYrD/C,iC,GAAN,MAAMA,iCAAN,SAAgD3B,iBAAhD,CAAkE;AACrE+C,QAAAA,OAAO,GAAY;AACf,iBAAO,KAAP;AACH;;AACSC,QAAAA,eAAe,CAACT,OAAD,EAAoC;AACzD,eAAKU,WAAL,GAAmBV,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BW,eAA5B,CAA4C5B,KAA5C,GAAoD,CAApD,GAAwD,CAA3E;AACH;;AACSC,QAAAA,iBAAiB,CAACZ,OAAD,EAA8BW,KAA9B,EAAmD;AAC1EX,UAAAA,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BW,eAA5B,CAA4C5B,KAA5C,GAAoDA,KAAK,KAAK,CAA9D;AACH;;AAToE,O;;oDAY5DtB,kC,GAAN,MAAMA,kCAAN,SAAiD5B,iBAAjD,CAAmE;AACtE+C,QAAAA,OAAO,GAAY;AACf,iBAAO,KAAP;AACH;;AACSC,QAAAA,eAAe,CAACT,OAAD,EAAoC;AACzD,eAAKU,WAAL,GAAmBV,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BY,gBAA5B,CAA6C7B,KAA7C,GAAqD,CAArD,GAAyD,CAA5E;AACH;;AACSC,QAAAA,iBAAiB,CAACZ,OAAD,EAA8BW,KAA9B,EAAmD;AAC1EX,UAAAA,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4BY,gBAA5B,CAA6C7B,KAA7C,GAAqDA,KAAK,KAAK,CAA/D;AACH;;AATqE,O;;iDAY7DrB,+B,GAAN,MAAMA,+BAAN,SAA8C7B,iBAA9C,CAAgE;AACnE+C,QAAAA,OAAO,GAAY;AACf,iBAAO,KAAP;AACH;;AACSC,QAAAA,eAAe,CAACT,OAAD,EAAoC;AACzD,eAAKU,WAAL,GAAmBV,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4Ba,aAA5B,CAA0C9B,KAA1C,GAAkD,CAAlD,GAAsD,CAAzE;AACH;;AACSC,QAAAA,iBAAiB,CAACZ,OAAD,EAA8BW,KAA9B,EAAmD;AAC1EX,UAAAA,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4Ba,aAA5B,CAA0C9B,KAA1C,GAAkDA,KAAK,KAAK,CAA5D;AACH;;AATkE,O;;sDAY1DpB,oC,GAAN,MAAMA,oCAAN,SAAmD9B,iBAAnD,CAAqE;AACxE+C,QAAAA,OAAO,GAAY;AACf,iBAAO,KAAP;AACH;;AACSC,QAAAA,eAAe,CAACT,OAAD,EAAoC;AACzD,eAAKU,WAAL,GAAmBV,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4Bc,kBAA5B,CAA+C/B,KAA/C,GAAuD,CAAvD,GAA2D,CAA9E;AACH;;AACSC,QAAAA,iBAAiB,CAACZ,OAAD,EAA8BW,KAA9B,EAAmD;AAC1EX,UAAAA,OAAO,CAACC,OAAR,CAAiB2B,UAAjB,CAA4Bc,kBAA5B,CAA+C/B,KAA/C,GAAuDA,KAAK,KAAK,CAAjE;AACH;;AATuE,O", "sourcesContent": ["import { EventActionBase } from \"db://assets/bundles/common/script/game/eventgroup/IEventAction\";\r\nimport { IEventGroupContext } from \"db://assets/bundles/common/script/game/eventgroup/IEventGroupContext\";\r\nimport { eEmitterProp, ePropMask } from \"../Emitter\";\r\nimport { Property } from \"../PropertyContainer\";\r\n\r\nexport class Emitter<PERSON><PERSON><PERSON>ase extends EventActionBase {\r\n    // this was intentionally left blank\r\n}\r\n\r\nexport class EmitterActionBase_BoolModifier extends EmitterActionBase {\r\n    protected get propertyType(): eEmitterProp {\r\n        return eEmitterProp.IsActive;\r\n    }\r\n    protected _targetProperty: Property<boolean>|undefined = undefined;\r\n    onLoad(context: IEventGroupContext): void {\r\n        this._targetProperty = context.emitter!.getProperty(this.propertyType);\r\n        super.onLoad(context);\r\n    }\r\n    onStart(context: IEventGroupContext): void {\r\n        this._targetProperty!.addWriteMask(ePropMask.EventGroup);\r\n        super.onStart(context);\r\n    }\r\n    onComplete(context: IEventGroupContext): void {\r\n        this._targetProperty!.removeWriteMask(ePropMask.EventGroup);\r\n        super.onComplete(context);\r\n    }\r\n    canLerp(): boolean {\r\n        return false;\r\n    }\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = this._targetProperty!.value ? 1 : 0;\r\n    }\r\n    protected onExecuteInternal(context: IEventGroupContext, value: number): void {\r\n        this._targetProperty!.setValue(value === 1, ePropMask.EventGroup);\r\n    }\r\n}\r\n\r\nexport class EmitterActionBase_NumberModifier extends EmitterActionBase {\r\n    protected get propertyType(): eEmitterProp {\r\n        // override this\r\n        return eEmitterProp.InitialDelay;\r\n    }\r\n    protected _targetProperty: Property<number>|undefined = undefined;\r\n    onLoad(context: IEventGroupContext): void {\r\n        this._targetProperty = context.emitter!.getProperty(this.propertyType);\r\n        super.onLoad(context);\r\n    }\r\n\r\n    onStart(context: IEventGroupContext): void {\r\n        this._targetProperty!.addWriteMask(ePropMask.EventGroup);\r\n        super.onStart(context);\r\n    }\r\n\r\n    onComplete(context: IEventGroupContext): void {\r\n        this._targetProperty!.removeWriteMask(ePropMask.EventGroup);\r\n        super.onComplete(context);\r\n    }\r\n\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = this._targetProperty!.value;\r\n    }\r\n\r\n    protected onExecuteInternal(context: IEventGroupContext, value: number): void {\r\n        this._targetProperty!.setValue(value, ePropMask.EventGroup);\r\n    }    \r\n}\r\n\r\n// 修改发射器启用状态\r\nexport class EmitterAction_Active extends EmitterActionBase_BoolModifier {\r\n    protected get propertyType(): eEmitterProp {\r\n        return eEmitterProp.IsActive;\r\n    }\r\n\r\n    protected onExecuteInternal(context: IEventGroupContext, value: number): void {\r\n        super.onExecuteInternal(context, value);\r\n        this._targetProperty!.notify();\r\n    }\r\n}\r\n\r\nexport class EmitterAction_Prewarm extends EmitterActionBase_BoolModifier {\r\n    protected get propertyType(): eEmitterProp {\r\n        return eEmitterProp.IsPreWarm;\r\n    }\r\n}\r\n\r\n// 修改发射器初始延迟时间\r\nexport class EmitterAction_InitialDelay extends EmitterActionBase_NumberModifier {\r\n    protected get propertyType(): eEmitterProp {\r\n        return eEmitterProp.InitialDelay;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_PrewarmDuration extends EmitterActionBase_NumberModifier {\r\n    protected get propertyType(): eEmitterProp {\r\n        return eEmitterProp.PrewarmDuration;\r\n    }\r\n}\r\n\r\n// 修改发射器持续时间\r\nexport class EmitterAction_Duration extends EmitterActionBase_NumberModifier {\r\n    protected get propertyType(): eEmitterProp {\r\n        return eEmitterProp.EmitDuration;\r\n    }\r\n}\r\n\r\n// 修改发射器已运行时间\r\nexport class EmitterAction_ElapsedTime extends EmitterActionBase_NumberModifier {\r\n    protected get propertyType(): eEmitterProp {\r\n        return eEmitterProp.ElapsedTime;\r\n    }\r\n}\r\n\r\n// 修改发射器是否循环(boolean)\r\nexport class EmitterAction_Loop extends EmitterActionBase_BoolModifier {\r\n    protected get propertyType(): eEmitterProp {\r\n        return eEmitterProp.IsLoop;\r\n    }\r\n}\r\n\r\n// 循环间隔\r\nexport class EmitterAction_LoopInterval extends EmitterActionBase_NumberModifier {\r\n    protected get propertyType(): eEmitterProp {\r\n        return eEmitterProp.LoopInterval;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_EmitInterval extends EmitterActionBase_NumberModifier {\r\n    protected get propertyType(): eEmitterProp {\r\n        return eEmitterProp.EmitInterval;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_PerEmitCount extends EmitterActionBase_NumberModifier {\r\n    protected get propertyType(): eEmitterProp {\r\n        return eEmitterProp.PerEmitCount;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_PerEmitInterval extends EmitterActionBase_NumberModifier {\r\n    protected get propertyType(): eEmitterProp {\r\n        return eEmitterProp.PerEmitInterval;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_PerEmitOffsetX extends EmitterActionBase_NumberModifier {\r\n    protected get propertyType(): eEmitterProp {\r\n        return eEmitterProp.PerEmitOffsetX;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_Angle extends EmitterActionBase_NumberModifier {\r\n    protected get propertyType(): eEmitterProp {\r\n        return eEmitterProp.Angle;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_Count extends EmitterActionBase_NumberModifier {\r\n    protected get propertyType(): eEmitterProp {\r\n        return eEmitterProp.Count;\r\n    }\r\n}\r\n\r\n// 以下是发射器修改子弹属性的部分\r\nexport class EmitterAction_BulletDuration extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.duration.value;\r\n    }\r\n\r\n    protected onExecuteInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.duration.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletDamage extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        // this._startValue = context.emitter!.bulletProp.damage.value;\r\n    }\r\n\r\n    protected onExecuteInternal(context: IEventGroupContext, value: number): void {\r\n        // context.emitter!.bulletProp.damage.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletSpeed extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.speed.value;\r\n    }\r\n\r\n    protected onExecuteInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.speed.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletSpeedAngle extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.speedAngle.value;\r\n    }\r\n\r\n    protected onExecuteInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.speedAngle.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletAcceleration extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.acceleration.value;\r\n    }\r\n\r\n    protected onExecuteInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.acceleration.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletAccelerationAngle extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.accelerationAngle.value;\r\n    }\r\n\r\n    protected onExecuteInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.accelerationAngle.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletScale extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.scale.value;\r\n    }\r\n\r\n    protected onExecuteInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.scale.value = value;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletColorR extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.color.value.r;\r\n    }\r\n\r\n    protected onExecuteInternal(context: IEventGroupContext, value: number): void {\r\n        let color = context.emitter!.bulletProp.color.value;\r\n        color.r = value;\r\n        context.emitter!.bulletProp.color.value = color;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletColorG extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.color.value.g;\r\n    }\r\n\r\n    protected onExecuteInternal(context: IEventGroupContext, value: number): void {\r\n        let color = context.emitter!.bulletProp.color.value;\r\n        color.g = value;\r\n        context.emitter!.bulletProp.color.value = color;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletColorB extends EmitterActionBase {\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.color.value.b;\r\n    }\r\n\r\n    protected onExecuteInternal(context: IEventGroupContext, value: number): void {\r\n        let color = context.emitter!.bulletProp.color.value;\r\n        color.b = value;\r\n        context.emitter!.bulletProp.color.value = color;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletFacingMoveDir extends EmitterActionBase {\r\n    canLerp(): boolean {\r\n        return false;\r\n    }\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.isFacingMoveDir.value ? 1 : 0;\r\n    }\r\n    protected onExecuteInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.isFacingMoveDir.value = value === 1;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletTrackingTarget extends EmitterActionBase {\r\n    canLerp(): boolean {\r\n        return false;\r\n    }\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.isTrackingTarget.value ? 1 : 0;\r\n    }\r\n    protected onExecuteInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.isTrackingTarget.value = value === 1;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletDestructive extends EmitterActionBase {\r\n    canLerp(): boolean {\r\n        return false;\r\n    }\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.isDestructive.value ? 1 : 0;\r\n    }\r\n    protected onExecuteInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.isDestructive.value = value === 1;\r\n    }\r\n}\r\n\r\nexport class EmitterAction_BulletDestructiveOnHit extends EmitterActionBase {\r\n    canLerp(): boolean {\r\n        return false;\r\n    }\r\n    protected resetStartValue(context: IEventGroupContext): void {\r\n        this._startValue = context.emitter!.bulletProp.isDestructiveOnHit.value ? 1 : 0;\r\n    }\r\n    protected onExecuteInternal(context: IEventGroupContext, value: number): void {\r\n        context.emitter!.bulletProp.isDestructiveOnHit.value = value === 1;\r\n    }\r\n}"]}