System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Label, Node, Sprite, tween, Tween, DamageType, EffectType, AttributeConst, AttributeData, Entity, GameEnum, BuffComp, SkillComp, GameIns, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, ccclass, property, PlaneBase;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfDamageType(extras) {
    _reporterNs.report("DamageType", "db://assets/bundles/common/script/autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEffectParam(extras) {
    _reporterNs.report("EffectParam", "db://assets/bundles/common/script/autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEffectType(extras) {
    _reporterNs.report("EffectType", "db://assets/bundles/common/script/autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeConst(extras) {
    _reporterNs.report("AttributeConst", "db://assets/bundles/common/script/const/AttributeConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeData(extras) {
    _reporterNs.report("AttributeData", "db://assets/bundles/common/script/data/base/AttributeData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFBoxCollider(extras) {
    _reporterNs.report("FBoxCollider", "db://assets/bundles/common/script/game/collider-system/FBoxCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCircleCollider(extras) {
    _reporterNs.report("FCircleCollider", "db://assets/bundles/common/script/game/collider-system/FCircleCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFPolygonCollider(extras) {
    _reporterNs.report("FPolygonCollider", "db://assets/bundles/common/script/game/collider-system/FPolygonCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "db://assets/bundles/common/script/game/ui/base/Entity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "db://assets/bundles/common/script/game/const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBuffComp(extras) {
    _reporterNs.report("BuffComp", "./skill/BuffComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBuff(extras) {
    _reporterNs.report("Buff", "./skill/BuffComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSkillComp(extras) {
    _reporterNs.report("SkillComp", "./skill/SkillComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "db://assets/bundles/common/script/game/GameIns", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Label = _cc.Label;
      Node = _cc.Node;
      Sprite = _cc.Sprite;
      tween = _cc.tween;
      Tween = _cc.Tween;
    }, function (_unresolved_2) {
      DamageType = _unresolved_2.DamageType;
      EffectType = _unresolved_2.EffectType;
    }, function (_unresolved_3) {
      AttributeConst = _unresolved_3.AttributeConst;
    }, function (_unresolved_4) {
      AttributeData = _unresolved_4.AttributeData;
    }, function (_unresolved_5) {
      Entity = _unresolved_5.default;
    }, function (_unresolved_6) {
      GameEnum = _unresolved_6.GameEnum;
    }, function (_unresolved_7) {
      BuffComp = _unresolved_7.default;
    }, function (_unresolved_8) {
      SkillComp = _unresolved_8.default;
    }, function (_unresolved_9) {
      GameIns = _unresolved_9.GameIns;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "1b62cNPltVDzqvnXkWTOv12", "PlaneBase", undefined);

      __checkObsolete__(['_decorator', 'Label', 'math', 'Node', 'Sprite', 'tween', 'Tween']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", PlaneBase = (_dec = ccclass('PlaneBase'), _dec2 = property(Node), _dec3 = property(Sprite), _dec4 = property(Sprite), _dec5 = property(Label), _dec(_class = (_class2 = class PlaneBase extends (_crd && Entity === void 0 ? (_reportPossibleCrUseOfEntity({
        error: Error()
      }), Entity) : Entity) {
        constructor() {
          super();

          _initializerDefineProperty(this, "hpNode", _descriptor, this);

          _initializerDefineProperty(this, "hpBar", _descriptor2, this);

          // 血条
          _initializerDefineProperty(this, "hpAniSprite", _descriptor3, this);

          // 血条动画条
          _initializerDefineProperty(this, "hpfont", _descriptor4, this);

          // 血条文本
          this.enemy = true;
          // 是否为敌机
          this.type = 0;
          // 敌人类型
          this.bDamageable = true;
          this.curHp = 0;
          this.hurtTime = 0;
          this.collideComp = null;
          // 碰撞组件
          this._skillComp = null;
          this._buffComp = null;
          // TODO 临时做法，后续应该挪到 PlaneBase
          this._attributeData = new (_crd && AttributeData === void 0 ? (_reportPossibleCrUseOfAttributeData({
            error: Error()
          }), AttributeData) : AttributeData)();
        }

        // 是否可以被造成伤害
        get maxHp() {
          return this.attribute.getMaxHP();
        }

        init() {
          this._skillComp = new (_crd && SkillComp === void 0 ? (_reportPossibleCrUseOfSkillComp({
            error: Error()
          }), SkillComp) : SkillComp)();
          this.addComp("skill", this._skillComp);
          this._buffComp = new (_crd && BuffComp === void 0 ? (_reportPossibleCrUseOfBuffComp({
            error: Error()
          }), BuffComp) : BuffComp)();
          this.addComp("buff", this._buffComp);
          super.init();
        }

        get skillComp() {
          return this._skillComp;
        }

        get buffComp() {
          return this._buffComp;
        }

        get attribute() {
          return this._attributeData;
        }

        set colliderEnabled(value) {
          if (this.collideComp) {
            this.collideComp.isEnable = value;
          }
        }

        get colliderEnabled() {
          return this.collideComp ? this.collideComp.isEnable : false;
        }

        CastSkill(skillID) {
          this.skillComp.Cast(this, skillID);
        }

        addHp(heal) {
          this.curHp = Math.min(this.maxHp, this.curHp + heal);
          this.updateHpUI();
          ;
        }

        hurt(damage) {
          if (this.isDead) {
            return;
          }

          this.hurtTime = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager._gameTime;
          this.cutHp(damage);
          this.playHurtAnim();

          if (this.curHp <= 0) {
            this.toDie();
          }
        }

        get collisionLevel() {
          return 0;
        }

        get collisionHurt() {
          return 0;
        } // 撞机


        collisionPlane(plane) {
          if (this.isDead || plane.isDead) {
            return;
          }

          if (this.collisionLevel > plane.collisionLevel) {
            return;
          }

          var hurt;

          if (this.collisionLevel < plane.collisionLevel) {
            hurt = Math.max(this.maxHp, plane.collisionHurt);
          } else {
            hurt = plane.collisionHurt == -1 ? this.maxHp : plane.collisionHurt;
          }

          hurt = (hurt - this.attribute.getFinialAttributeByOutInKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).CollisionHurtResistanceOutAdd, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).CollisionHurtResistanceOutPer, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).CollisionHurtResistanceInAdd, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).CollisionHurtResistanceInPer)) * (1 - this.attribute.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).CollisionHurtDerateOut)) * (1 - this.attribute.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).CollisionHurtDerateIn));
          this.hurt(hurt);
        }
        /**
         * 减少血量
         * @param {number} damage 受到的伤害值
         */


        cutHp(damage) {
          var newHp = this.curHp - damage;
          this.curHp = Math.max(0, newHp);
          this.updateHpUI();
        }

        toDie(destroyType) {
          if (destroyType === void 0) {
            destroyType = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.Die;
          }

          if (this.isDead) {
            return false;
          }

          this.isDead = true;
          this.colliderEnabled = false;
          return true;
        }
        /**
         * 更新血量显示
         */


        updateHpUI() {
          if (this.hpBar) {
            // 更新血条前景的填充范围
            this.hpBar.fillRange = this.curHp / this.maxHp;

            if (this.hpAniSprite) {
              // 计算血条动画时间
              var duration = Math.abs(this.hpAniSprite.fillRange - this.hpBar.fillRange);
              Tween.stopAllByTarget(this.hpAniSprite); // 血条中间部分的动画

              tween(this.hpAniSprite).to(duration, {
                fillRange: this.hpBar.fillRange
              }).call(() => {}).start();
            }
          } // 更新血量文字


          this.hpfont && (this.hpfont.string = this.curHp.toFixed(0));
        }

        playHurtAnim() {// 子类实现
        }

        ApplyBuffEffect(buff, effectData) {
          switch (effectData.type) {
            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrMaxHPPer:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).MaxHPOutPer, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).MaxHPInPer, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrMaxHPAdd:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).MaxHPOutAdd, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).MaxHPInAdd, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrHPRecoveryPer:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).HPRecoveryOutPer, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).HPRecoveryInPer, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrHPRecoveryAdd:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).HPRecoveryOutAdd, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).HPRecoveryInAdd, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrHPRecoveryMaxHPPerAdd:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).MaxHPRecoveryRateOut, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).MaxHPRecoveryRateIn, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).HealMaxHPPer:
              if (effectData.param.length >= 1) {
                this.addHp(this.maxHp * effectData.param[0] / 10000);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).HealLoseHPPer:
              if (effectData.param.length >= 1) {
                this.addHp((this.maxHp - this.curHp) * effectData.param[0] / 10000);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).HealHP:
              if (effectData.param.length >= 1) {
                this.addHp(effectData.param[0]);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrAttackPer:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).AttackOutPer, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).AttackInPer, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrAttackAdd:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).AttackOutAdd, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).AttackInAdd, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrAttackBossPer:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).BossHurtBonusOut, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).BossHurtBonusIn, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrAttackNormalPer:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).NormalHurtBonusOut, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).NormalHurtBonusIn, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrFortunatePer:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).FortunateOutPer, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).FortunateInPer, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrFortunateAdd:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).FortunateOutAdd, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).FortunateInAdd, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrMissAdd:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).MissRateOut, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).MissRateIn, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrBulletHurtResistancePer:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).BulletHurtResistanceOutPer, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).BulletHurtResistanceInPer, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrBulletHurtResistanceAdd:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).BulletHurtResistanceOutAdd, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).BulletHurtResistanceInAdd, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrBulletHurtDerateAdd:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).BulletHurtDerateOut, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).BulletHurtDerateIn, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrCollisionHurtResistancePer:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).CollisionHurtResistanceOutPer, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).CollisionHurtResistanceInPer, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrCollisionHurtResistanceAdd:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).CollisionHurtResistanceOutAdd, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).CollisionHurtResistanceInAdd, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrCollisionHurtDerateAdd:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).CollisionHurtDerateOut, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).CollisionHurtDerateIn, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrFinalScoreAdd:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).FinalScoreRateOut, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).FinalScoreRateIn, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrKillScoreAdd:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).KillScoreRateOut, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).KillScoreRateIn, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrEnergyRecoveryPerAdd:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).EnergyRecoveryOutPer, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).EnergyRecoveryInPer, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrEnergyRecoveryAdd:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).EnergyRecoveryOutAdd, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).EnergyRecoveryInAdd, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrPickRadiusPer:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).PickRadiusOutPer, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).PickRadiusInPer, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrPickRadiusAdd:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).PickRadiusOutAdd, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).PickRadiusInAdd, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).ApplyBuff:
              if (effectData.param.length < 2) {
                return;
              }

              var buffID = effectData.param[0];
              var target = effectData.param[1];
              (_crd && SkillComp === void 0 ? (_reportPossibleCrUseOfSkillComp({
                error: Error()
              }), SkillComp) : SkillComp).forEachByTargetType(this, target, entity => {
                entity.buffComp.ApplyBuff((buff == null ? void 0 : buff.isOutside) || false, buffID);
              });
              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).ImmuneBulletHurt:
              if (buff) {
                this.attribute.addModify(buff.id, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).StatusImmuneBulletHurt, 1);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).ImmuneCollisionHurt:
              if (buff) {
                this.attribute.addModify(buff.id, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).StatusImmuneCollisionHurt, 1);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).IgnoreBullet:
              if (buff) {
                this.attribute.addModify(buff.id, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).StatusIgnoreBullet, 1);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).IgnoreCollision:
              if (buff) {
                this.attribute.addModify(buff.id, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).StatusIgnoreCollision, 1);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).ImmuneNuclearHurt:
              if (buff) {
                this.attribute.addModify(buff.id, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).StatusImmuneNuclearHurt, 1);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).ImmuneActiveSkillHurt:
              if (buff) {
                this.attribute.addModify(buff.id, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).StatusImmuneActiveSkillHurt, 1);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).Invincible:
              if (buff) {
                this.attribute.addModify(buff.id, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).StatusInvincible, 1);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrNuclearMax:
              this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).NuclearMax, effectData);
              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrBulletAttackAdd:
              var damageType = (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                error: Error()
              }), DamageType) : DamageType).ALL;

              if (effectData.param.length > 1) {
                damageType = effectData.param[1];
              }

              if (buff != null && buff.isOutside) {
                switch (damageType) {
                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).ALL:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).BulletAttackOutAdd, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).EXPLOSIVE:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).ExplosiveBulletAttackOutAdd, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).NORMAL:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).NormalBulletAttackOutAdd, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).ENERGETIC:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).EnergeticBulletAttackOutAdd, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).PHYSICAL:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).PhysicsBulletAttackOutAdd, effectData);
                    break;
                }
              } else {
                switch (damageType) {
                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).ALL:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).BulletAttackInAdd, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).EXPLOSIVE:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).ExplosiveBulletAttackInAdd, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).NORMAL:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).NormalBulletAttackInAdd, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).ENERGETIC:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).EnergeticBulletAttackInAdd, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).PHYSICAL:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).PhysicsBulletAttackInAdd, effectData);
                    break;
                }
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrBulletAttackPer:
              damageType = (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                error: Error()
              }), DamageType) : DamageType).ALL;

              if (effectData.param.length > 1) {
                damageType = effectData.param[1];
              }

              if (buff != null && buff.isOutside) {
                switch (damageType) {
                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).ALL:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).BulletAttackOutPer, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).EXPLOSIVE:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).ExplosiveBulletAttackOutPer, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).NORMAL:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).NormalBulletAttackOutPer, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).ENERGETIC:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).EnergeticBulletAttackOutPer, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).PHYSICAL:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).PhysicsBulletAttackOutPer, effectData);
                    break;
                }
              } else {
                switch (damageType) {
                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).ALL:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).BulletAttackInPer, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).EXPLOSIVE:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).ExplosiveBulletAttackInPer, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).NORMAL:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).NormalBulletAttackInPer, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).ENERGETIC:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).EnergeticBulletAttackInPer, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).PHYSICAL:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).PhysicsBulletAttackInPer, effectData);
                    break;
                }
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrBulletHurtFix:
              damageType = (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                error: Error()
              }), DamageType) : DamageType).ALL;

              if (effectData.param.length > 1) {
                damageType = effectData.param[1];
              }

              if (buff != null && buff.isOutside) {
                switch (damageType) {
                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).ALL:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).BulletHurtFixOut, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).EXPLOSIVE:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).ExplosiveBulletHurtFixOut, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).NORMAL:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).NormalBulletHurtFixOut, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).ENERGETIC:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).EnergeticBulletHurtFixOut, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).PHYSICAL:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).PhysicsBulletHurtFixOut, effectData);
                    break;
                }
              } else {
                switch (damageType) {
                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).ALL:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).BulletHurtFixIn, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).EXPLOSIVE:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).ExplosiveBulletHurtFixIn, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).NORMAL:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).NormalBulletHurtFixIn, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).ENERGETIC:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).EnergeticBulletHurtFixIn, effectData);
                    break;

                  case (_crd && DamageType === void 0 ? (_reportPossibleCrUseOfDamageType({
                    error: Error()
                  }), DamageType) : DamageType).PHYSICAL:
                    this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                      error: Error()
                    }), AttributeConst) : AttributeConst).PhysicsBulletHurtFixIn, effectData);
                    break;
                }
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).HurtMaxHPPer:
              if (effectData.param.length < 1) {
                return;
              }

              this.hurt(this.maxHp * effectData.param[0]);
              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).HurtCurHPPer:
              if (effectData.param.length < 1) {
                return;
              }

              this.hurt(this.curHp * effectData.param[0]);
              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrNuclearAttackPer:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).NuclearAttackOutPer, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).NuclearAttackInPer, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrNuclearAttackAdd:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).NuclearAttackOutAdd, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).NuclearAttackInAdd, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).FireBullet:
              // TODO not implement
              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AttrNuclearHurtFix:
              if (buff != null && buff.isOutside) {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).NuclearHurtFixOut, effectData);
              } else {
                this.ApplyBuffAttributeEffect(buff, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                  error: Error()
                }), AttributeConst) : AttributeConst).NuclearHurtFixIn, effectData);
              }

              break;

            case (_crd && EffectType === void 0 ? (_reportPossibleCrUseOfEffectType({
              error: Error()
            }), EffectType) : EffectType).AddNuclear:
              if (effectData.param.length > 0) {
                this.addNuclear(effectData.param[0]);
              }

              break;

            default:
              break;
          }
        }

        ApplyBuffAttributeEffect(buff, key, effectData) {
          if (!buff) {
            return;
          }

          if (effectData.param.length < 1) {
            return;
          }

          this.attribute.addModify(buff.id, key, effectData.param[0]);
        }

        RemoveBuffEffect(buff, effectData) {
          this.attribute.removeModify(buff.id);
        }

        setAnimSpeed(speed) {// 子类实现
        } // 获取当前的攻击目标
        // 对于敌机，返回玩家飞机；
        // 对于玩家飞机，可以按策划规则（距离或者其他规则）


        getTarget() {
          // 子类实现
          return null;
        } // 增加核弹


        addNuclear(num) {// 子类实现
        } // 获取已拾取宝石数量


        get pickDiamondNum() {
          return 0;
        } // 获取已击杀敌机数量


        get killEnemyNum() {
          return 0;
        } // 获取已使用核弹数量


        get usedNuclearNum() {
          return 0;
        } // 获取已使用大招数量


        get usedSuperNum() {
          return 0;
        } // 获取当前核弹数量


        get nuclearNum() {
          return 0;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "hpNode", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "hpBar", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "hpAniSprite", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "hpfont", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=db313ad01e0116f664448291f2a248d61de69eff.js.map