{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/PlaneBase.ts"], "names": ["_decorator", "Label", "Node", "Sprite", "tween", "Tween", "DamageType", "EffectType", "AttributeConst", "AttributeData", "Entity", "GameEnum", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>llComp", "GameIns", "ccclass", "property", "PlaneBase", "constructor", "enemy", "type", "bDamageable", "curHp", "hurtTime", "collide<PERSON>omp", "_skillComp", "_buffComp", "_attributeData", "maxHp", "attribute", "getMaxHP", "init", "addComp", "skillComp", "buff<PERSON><PERSON>p", "colliderEnabled", "value", "isEnable", "CastSkill", "skillID", "Cast", "addHp", "heal", "Math", "min", "updateHpUI", "hurt", "damage", "isDead", "battleManager", "_gameTime", "cutHp", "playHurtAnim", "to<PERSON><PERSON>", "collisionLevel", "collisionHurt", "collisionPlane", "plane", "max", "getFinialAttributeByOutInKey", "CollisionHurtResistanceOutAdd", "CollisionHurtResistanceOutPer", "CollisionHurtResistanceInAdd", "CollisionHurtResistanceInPer", "getFinalAttributeByKey", "CollisionHurtDerateOut", "CollisionHurtDerateIn", "newHp", "destroyType", "EnemyDestroyType", "Die", "hpBar", "fill<PERSON><PERSON><PERSON>", "hpAniSprite", "duration", "abs", "stopAllByTarget", "to", "call", "start", "hpfont", "string", "toFixed", "ApplyBuffEffect", "buff", "effectData", "AttrMaxHPPer", "isOutside", "ApplyBuffAttributeEffect", "MaxHPOutPer", "MaxHPInPer", "AttrMaxHPAdd", "MaxHPOutAdd", "MaxHPInAdd", "AttrHPRecoveryPer", "HPRecoveryOutPer", "HPRecoveryInPer", "AttrHPRecoveryAdd", "HPRecoveryOutAdd", "HPRecoveryInAdd", "AttrHPRecoveryMaxHPPerAdd", "MaxHPRecoveryRateOut", "MaxHPRecoveryRateIn", "HealMaxHPPer", "param", "length", "Heal<PERSON>ose<PERSON><PERSON><PERSON>", "HealHP", "AttrAttackPer", "AttackOutPer", "AttackInPer", "AttrAttackAdd", "AttackOutAdd", "AttackInAdd", "AttrAttackBossPer", "BossHurtBonusOut", "BossHurtBonusIn", "AttrAttackNormalPer", "NormalHurtBonusOut", "NormalHurtBonusIn", "AttrFortunatePer", "FortunateOutPer", "FortunateInPer", "AttrFortunateAdd", "FortunateOutAdd", "FortunateInAdd", "AttrMissAdd", "MissRateOut", "MissRateIn", "AttrBulletHurtResistancePer", "BulletHurtResistanceOutPer", "BulletHurtResistanceInPer", "AttrBulletHurtResistanceAdd", "BulletHurtResistanceOutAdd", "BulletHurtResistanceInAdd", "AttrBulletHurtDerateAdd", "BulletHurtDerateOut", "BulletHurtDerateIn", "AttrCollisionHurtResistancePer", "AttrCollisionHurtResistanceAdd", "AttrCollisionHurtDerateAdd", "AttrFinalScoreAdd", "FinalScoreRateOut", "FinalScoreRateIn", "AttrKillScoreAdd", "KillScoreRateOut", "KillScoreRateIn", "AttrEnergyRecoveryPerAdd", "EnergyRecoveryOutPer", "EnergyRecoveryInPer", "AttrEnergyRecoveryAdd", "EnergyRecoveryOutAdd", "EnergyRecoveryInAdd", "AttrPickRadiusPer", "PickRadiusOutPer", "PickRadiusInPer", "AttrPickRadiusAdd", "PickRadiusOutAdd", "PickRadiusInAdd", "A<PERSON><PERSON><PERSON><PERSON>", "buff<PERSON>", "target", "forEachByTargetType", "entity", "ImmuneBulletHurt", "addModify", "id", "StatusImmuneBulletHurt", "ImmuneCollisionHurt", "StatusImmuneCollisionHurt", "IgnoreBullet", "StatusIgnoreBullet", "IgnoreCollision", "StatusIgnoreCollision", "ImmuneNuclearHurt", "StatusImmuneNuclearHurt", "ImmuneActiveSkillHurt", "StatusImmuneActiveSkillHurt", "Invincible", "StatusInvincible", "AttrNuclearMax", "NuclearMax", "AttrBulletAttackAdd", "damageType", "ALL", "BulletAttackOutAdd", "EXPLOSIVE", "ExplosiveBulletAttackOutAdd", "NORMAL", "NormalBulletAttackOutAdd", "ENERGETIC", "EnergeticBulletAttackOutAdd", "PHYSICAL", "PhysicsBulletAttackOutAdd", "BulletAttackInAdd", "ExplosiveBulletAttackInAdd", "NormalBulletAttackInAdd", "EnergeticBulletAttackInAdd", "PhysicsBulletAttackInAdd", "AttrBulletAttackPer", "BulletAttackOutPer", "ExplosiveBulletAttackOutPer", "NormalBulletAttackOutPer", "EnergeticBulletAttackOutPer", "PhysicsBulletAttackOutPer", "BulletAttackInPer", "ExplosiveBulletAttackInPer", "NormalBulletAttackInPer", "EnergeticBulletAttackInPer", "PhysicsBulletAttackInPer", "AttrBulletHurtFix", "BulletHurtFixOut", "ExplosiveBulletHurtFixOut", "NormalBulletHurtFixOut", "EnergeticBulletHurtFixOut", "PhysicsBulletHurtFixOut", "BulletHurtFixIn", "ExplosiveBulletHurtFixIn", "NormalBulletHurtFixIn", "EnergeticBulletHurtFixIn", "PhysicsBulletHurtFixIn", "HurtMaxHPPer", "HurtCurHPPer", "AttrNuclearAttackPer", "NuclearAttackOutPer", "NuclearAttackInPer", "AttrNuclearAttackAdd", "NuclearAttackOutAdd", "NuclearAttackInAdd", "FireBullet", "AttrNuclearHurtFix", "NuclearHurtFixOut", "NuclearHurtFixIn", "AddNuclear", "addNuclear", "key", "RemoveBuffEffect", "removeModify", "setAnimSpeed", "speed", "get<PERSON><PERSON><PERSON>", "num", "pickDiamond<PERSON>um", "killEnemyNum", "usedNuclearNum", "usedSuperNum", "nuclearNum"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;;AAG9CC,MAAAA,U,iBAAAA,U;AAAyBC,MAAAA,U,iBAAAA,U;;AACzBC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,a,iBAAAA,a;;AAIFC,MAAAA,M;;AACEC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,Q;;AACAC,MAAAA,S;;AACEC,MAAAA,O,iBAAAA,O;;;;;;;;;OAZH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBhB,U;;yBAgBTiB,S,WADpBF,OAAO,CAAC,WAAD,C,UAKHC,QAAQ,CAACd,IAAD,C,UAERc,QAAQ,CAACb,MAAD,C,UAERa,QAAQ,CAACb,MAAD,C,UAERa,QAAQ,CAACf,KAAD,C,2BAXb,MACqBgB,SADrB;AAAA;AAAA,4BAC8C;AAC1CC,QAAAA,WAAW,GAAG;AACV;;AADU;;AAAA;;AAMe;AANf;;AAQqB;AARrB;;AAUe;AAVf,eAYdC,KAZc,GAYN,IAZM;AAYA;AAZA,eAadC,IAbc,GAaP,CAbO;AAaJ;AAbI,eAcdC,WAdc,GAcS,IAdT;AAAA,eAmBdC,KAnBc,GAmBE,CAnBF;AAAA,eAoBdC,QApBc,GAoBI,CApBJ;AAAA,eAsBdC,WAtBc,GAsB0D,IAtB1D;AAsBgE;AAtBhE,eAwBNC,UAxBM,GAwByB,IAxBzB;AAAA,eAyBNC,SAzBM,GAyBuB,IAzBvB;AA2Bd;AA3Bc,eA4BNC,cA5BM,GA4B0B;AAAA;AAAA,+CA5B1B;AAEb;;AAY4B;AAEpB,YAALC,KAAK,GAAU;AACf,iBAAQ,KAAKC,SAAL,CAAeC,QAAf,EAAR;AACH;;AAYDC,QAAAA,IAAI,GAAG;AACH,eAAKN,UAAL,GAAkB;AAAA;AAAA,uCAAlB;AACA,eAAKO,OAAL,CAAa,OAAb,EAAsB,KAAKP,UAA3B;AACA,eAAKC,SAAL,GAAiB;AAAA;AAAA,qCAAjB;AACA,eAAKM,OAAL,CAAa,MAAb,EAAqB,KAAKN,SAA1B;AACA,gBAAMK,IAAN;AACH;;AAEY,YAATE,SAAS,GAAG;AACZ,iBAAO,KAAKR,UAAZ;AACH;;AAEW,YAARS,QAAQ,GAAG;AACX,iBAAO,KAAKR,SAAZ;AACH;;AAEY,YAATG,SAAS,GAAkB;AAC3B,iBAAO,KAAKF,cAAZ;AACH;;AAEkB,YAAfQ,eAAe,CAACC,KAAD,EAAiB;AAChC,cAAI,KAAKZ,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBa,QAAjB,GAA4BD,KAA5B;AACH;AACJ;;AACkB,YAAfD,eAAe,GAAY;AAC3B,iBAAO,KAAKX,WAAL,GAAmB,KAAKA,WAAL,CAAiBa,QAApC,GAA+C,KAAtD;AACH;;AAEDC,QAAAA,SAAS,CAACC,OAAD,EAAkB;AACvB,eAAKN,SAAL,CAAeO,IAAf,CAAoB,IAApB,EAA0BD,OAA1B;AACH;;AAEDE,QAAAA,KAAK,CAACC,IAAD,EAAe;AAChB,eAAKpB,KAAL,GAAaqB,IAAI,CAACC,GAAL,CACT,KAAKhB,KADI,EAET,KAAKN,KAAL,GAAaoB,IAFJ,CAAb;AAIA,eAAKG,UAAL;AAAkB;AACrB;;AAEDC,QAAAA,IAAI,CAACC,MAAD,EAAiB;AACjB,cAAI,KAAKC,MAAT,EAAiB;AACb;AACH;;AACD,eAAKzB,QAAL,GAAgB;AAAA;AAAA,kCAAQ0B,aAAR,CAAsBC,SAAtC;AACA,eAAKC,KAAL,CAAWJ,MAAX;AACA,eAAKK,YAAL;;AACA,cAAI,KAAK9B,KAAL,IAAc,CAAlB,EAAqB;AACjB,iBAAK+B,KAAL;AACH;AACJ;;AAEiB,YAAdC,cAAc,GAAG;AACjB,iBAAO,CAAP;AACH;;AACgB,YAAbC,aAAa,GAAG;AAChB,iBAAO,CAAP;AACH,SAzFyC,CA2F1C;;;AACAC,QAAAA,cAAc,CAACC,KAAD,EAAmB;AAC7B,cAAI,KAAKT,MAAL,IAAeS,KAAK,CAACT,MAAzB,EAAiC;AAC7B;AACH;;AACD,cAAI,KAAKM,cAAL,GAAsBG,KAAK,CAACH,cAAhC,EAAgD;AAC5C;AACH;;AACD,cAAIR,IAAJ;;AACA,cAAI,KAAKQ,cAAL,GAAsBG,KAAK,CAACH,cAAhC,EAAgD;AAC5CR,YAAAA,IAAI,GAAGH,IAAI,CAACe,GAAL,CAAS,KAAK9B,KAAd,EAAqB6B,KAAK,CAACF,aAA3B,CAAP;AACH,WAFD,MAEO;AACHT,YAAAA,IAAI,GAAGW,KAAK,CAACF,aAAN,IAAuB,CAAC,CAAxB,GAA4B,KAAK3B,KAAjC,GAAyC6B,KAAK,CAACF,aAAtD;AACH;;AAEDT,UAAAA,IAAI,GAAG,CAACA,IAAI,GAAG,KAAKjB,SAAL,CAAe8B,4BAAf,CACP;AAAA;AAAA,gDAAeC,6BADR,EACuC;AAAA;AAAA,gDAAeC,6BADtD,EAEP;AAAA;AAAA,gDAAeC,4BAFR,EAEsC;AAAA;AAAA,gDAAeC,4BAFrD,CAAR,KAGA,IAAI,KAAKlC,SAAL,CAAemC,sBAAf,CAAsC;AAAA;AAAA,gDAAeC,sBAArD,CAHJ,KAIA,IAAI,KAAKpC,SAAL,CAAemC,sBAAf,CAAsC;AAAA;AAAA,gDAAeE,qBAArD,CAJJ,CAAP;AAKA,eAAKpB,IAAL,CAAUA,IAAV;AACH;AAED;AACJ;AACA;AACA;;;AACIK,QAAAA,KAAK,CAACJ,MAAD,EAAiB;AAClB,cAAMoB,KAAK,GAAG,KAAK7C,KAAL,GAAayB,MAA3B;AACA,eAAKzB,KAAL,GAAaqB,IAAI,CAACe,GAAL,CAAS,CAAT,EAAYS,KAAZ,CAAb;AAEA,eAAKtB,UAAL;AACH;;AAEDQ,QAAAA,KAAK,CAACe,WAAD,EAAkF;AAAA,cAAjFA,WAAiF;AAAjFA,YAAAA,WAAiF,GAAxC;AAAA;AAAA,sCAASC,gBAAT,CAA0BC,GAAc;AAAA;;AACnF,cAAI,KAAKtB,MAAT,EAAiB;AACb,mBAAO,KAAP;AACH;;AACD,eAAKA,MAAL,GAAc,IAAd;AACA,eAAKb,eAAL,GAAuB,KAAvB;AACA,iBAAO,IAAP;AACH;AACD;AACJ;AACA;;;AACIU,QAAAA,UAAU,GAAG;AACT,cAAI,KAAK0B,KAAT,EAAgB;AACZ;AACA,iBAAKA,KAAL,CAAWC,SAAX,GAAuB,KAAKlD,KAAL,GAAa,KAAKM,KAAzC;;AAEA,gBAAI,KAAK6C,WAAT,EAAsB;AAClB;AACA,kBAAMC,QAAQ,GAAG/B,IAAI,CAACgC,GAAL,CAAS,KAAKF,WAAL,CAAiBD,SAAjB,GAA6B,KAAKD,KAAL,CAAWC,SAAjD,CAAjB;AAEAnE,cAAAA,KAAK,CAACuE,eAAN,CAAsB,KAAKH,WAA3B,EAJkB,CAKlB;;AACArE,cAAAA,KAAK,CAAC,KAAKqE,WAAN,CAAL,CACKI,EADL,CACQH,QADR,EACkB;AAAEF,gBAAAA,SAAS,EAAE,KAAKD,KAAL,CAAWC;AAAxB,eADlB,EAEKM,IAFL,CAEU,MAAM,CAEX,CAJL,EAKKC,KALL;AAMH;AACJ,WAlBQ,CAoBT;;;AACA,eAAKC,MAAL,KAAgB,KAAKA,MAAL,CAAaC,MAAb,GAAsB,KAAK3D,KAAL,CAAW4D,OAAX,CAAmB,CAAnB,CAAtC;AACH;;AAED9B,QAAAA,YAAY,GAAG,CACX;AACH;;AAED+B,QAAAA,eAAe,CAACC,IAAD,EAAoBC,UAApB,EAA6C;AACxD,kBAAQA,UAAU,CAACjE,IAAnB;AACI,iBAAK;AAAA;AAAA,0CAAWkE,YAAhB;AACI,kBAAIF,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAeK,WAAnD,EAAgEJ,UAAhE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAeM,UAAnD,EAA+DL,UAA/D;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWM,YAAhB;AACI,kBAAIP,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAeQ,WAAnD,EAAgEP,UAAhE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAeS,UAAnD,EAA+DR,UAA/D;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWS,iBAAhB;AACI,kBAAIV,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAeW,gBAAnD,EAAqEV,UAArE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAeY,eAAnD,EAAoEX,UAApE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWY,iBAAhB;AACI,kBAAIb,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAec,gBAAnD,EAAqEb,UAArE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAee,eAAnD,EAAoEd,UAApE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWe,yBAAhB;AACI,kBAAIhB,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAeiB,oBAAnD,EAAyEhB,UAAzE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAekB,mBAAnD,EAAwEjB,UAAxE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWkB,YAAhB;AACI,kBAAIlB,UAAU,CAACmB,KAAX,CAAiBC,MAAjB,IAA2B,CAA/B,EAAkC;AAC9B,qBAAKhE,KAAL,CAAW,KAAKb,KAAL,GAAayD,UAAU,CAACmB,KAAX,CAAiB,CAAjB,CAAb,GAAiC,KAA5C;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWE,aAAhB;AACI,kBAAIrB,UAAU,CAACmB,KAAX,CAAiBC,MAAjB,IAA2B,CAA/B,EAAkC;AAC9B,qBAAKhE,KAAL,CAAW,CAAC,KAAKb,KAAL,GAAa,KAAKN,KAAnB,IAA4B+D,UAAU,CAACmB,KAAX,CAAiB,CAAjB,CAA5B,GAAgD,KAA3D;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWG,MAAhB;AACI,kBAAItB,UAAU,CAACmB,KAAX,CAAiBC,MAAjB,IAA2B,CAA/B,EAAkC;AAC9B,qBAAKhE,KAAL,CAAW4C,UAAU,CAACmB,KAAX,CAAiB,CAAjB,CAAX;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWI,aAAhB;AACI,kBAAIxB,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAeyB,YAAnD,EAAiExB,UAAjE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAe0B,WAAnD,EAAgEzB,UAAhE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAW0B,aAAhB;AACI,kBAAI3B,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAe4B,YAAnD,EAAiE3B,UAAjE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAe6B,WAAnD,EAAgE5B,UAAhE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAW6B,iBAAhB;AACI,kBAAI9B,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAe+B,gBAAnD,EAAqE9B,UAArE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAegC,eAAnD,EAAoE/B,UAApE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWgC,mBAAhB;AACI,kBAAIjC,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAekC,kBAAnD,EAAuEjC,UAAvE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAemC,iBAAnD,EAAsElC,UAAtE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWmC,gBAAhB;AACI,kBAAIpC,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAeqC,eAAnD,EAAoEpC,UAApE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAesC,cAAnD,EAAmErC,UAAnE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWsC,gBAAhB;AACI,kBAAIvC,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAewC,eAAnD,EAAoEvC,UAApE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAeyC,cAAnD,EAAmExC,UAAnE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWyC,WAAhB;AACI,kBAAI1C,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAe2C,WAAnD,EAAgE1C,UAAhE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAe4C,UAAnD,EAA+D3C,UAA/D;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAW4C,2BAAhB;AACI,kBAAI7C,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAe8C,0BAAnD,EAA+E7C,UAA/E;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAe+C,yBAAnD,EAA8E9C,UAA9E;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAW+C,2BAAhB;AACI,kBAAIhD,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAeiD,0BAAnD,EAA+EhD,UAA/E;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAekD,yBAAnD,EAA8EjD,UAA9E;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWkD,uBAAhB;AACI,kBAAInD,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAeoD,mBAAnD,EAAwEnD,UAAxE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAeqD,kBAAnD,EAAuEpD,UAAvE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWqD,8BAAhB;AACI,kBAAItD,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAevB,6BAAnD,EAAkFwB,UAAlF;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAerB,4BAAnD,EAAiFsB,UAAjF;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWsD,8BAAhB;AACI,kBAAIvD,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAexB,6BAAnD,EAAkFyB,UAAlF;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAetB,4BAAnD,EAAiFuB,UAAjF;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWuD,0BAAhB;AACI,kBAAIxD,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAenB,sBAAnD,EAA2EoB,UAA3E;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAelB,qBAAnD,EAA0EmB,UAA1E;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWwD,iBAAhB;AACI,kBAAIzD,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAe0D,iBAAnD,EAAsEzD,UAAtE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAe2D,gBAAnD,EAAqE1D,UAArE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAW2D,gBAAhB;AACI,kBAAI5D,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAe6D,gBAAnD,EAAqE5D,UAArE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAe8D,eAAnD,EAAoE7D,UAApE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAW8D,wBAAhB;AACI,kBAAI/D,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAegE,oBAAnD,EAAyE/D,UAAzE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAeiE,mBAAnD,EAAwEhE,UAAxE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWiE,qBAAhB;AACI,kBAAIlE,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAemE,oBAAnD,EAAyElE,UAAzE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAeoE,mBAAnD,EAAwEnE,UAAxE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWoE,iBAAhB;AACI,kBAAIrE,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAesE,gBAAnD,EAAqErE,UAArE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAeuE,eAAnD,EAAoEtE,UAApE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWuE,iBAAhB;AACI,kBAAIxE,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAeyE,gBAAnD,EAAqExE,UAArE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAe0E,eAAnD,EAAoEzE,UAApE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAW0E,SAAhB;AACI,kBAAI1E,UAAU,CAACmB,KAAX,CAAiBC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B;AACH;;AACD,kBAAMuD,MAAM,GAAG3E,UAAU,CAACmB,KAAX,CAAiB,CAAjB,CAAf;AACA,kBAAMyD,MAAM,GAAG5E,UAAU,CAACmB,KAAX,CAAiB,CAAjB,CAAf;AACA;AAAA;AAAA,0CAAU0D,mBAAV,CAA8B,IAA9B,EAAoCD,MAApC,EAA6CE,MAAD,IAAY;AACpDA,gBAAAA,MAAM,CAACjI,QAAP,CAAgB6H,SAAhB,CAA0B,CAAA3E,IAAI,QAAJ,YAAAA,IAAI,CAAEG,SAAN,KAAiB,KAA3C,EAAkDyE,MAAlD;AACH,eAFD;AAGA;;AACJ,iBAAK;AAAA;AAAA,0CAAWI,gBAAhB;AACI,kBAAIhF,IAAJ,EAAU;AACN,qBAAKvD,SAAL,CAAewI,SAAf,CAAyBjF,IAAI,CAACkF,EAA9B,EAAkC;AAAA;AAAA,sDAAeC,sBAAjD,EAAyE,CAAzE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,mBAAhB;AACI,kBAAIpF,IAAJ,EAAU;AACN,qBAAKvD,SAAL,CAAewI,SAAf,CAAyBjF,IAAI,CAACkF,EAA9B,EAAkC;AAAA;AAAA,sDAAeG,yBAAjD,EAA4E,CAA5E;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,YAAhB;AACI,kBAAItF,IAAJ,EAAU;AACN,qBAAKvD,SAAL,CAAewI,SAAf,CAAyBjF,IAAI,CAACkF,EAA9B,EAAkC;AAAA;AAAA,sDAAeK,kBAAjD,EAAqE,CAArE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,eAAhB;AACI,kBAAIxF,IAAJ,EAAU;AACN,qBAAKvD,SAAL,CAAewI,SAAf,CAAyBjF,IAAI,CAACkF,EAA9B,EAAkC;AAAA;AAAA,sDAAeO,qBAAjD,EAAwE,CAAxE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,iBAAhB;AACI,kBAAI1F,IAAJ,EAAU;AACN,qBAAKvD,SAAL,CAAewI,SAAf,CAAyBjF,IAAI,CAACkF,EAA9B,EAAkC;AAAA;AAAA,sDAAeS,uBAAjD,EAA0E,CAA1E;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,qBAAhB;AACI,kBAAI5F,IAAJ,EAAU;AACN,qBAAKvD,SAAL,CAAewI,SAAf,CAAyBjF,IAAI,CAACkF,EAA9B,EAAkC;AAAA;AAAA,sDAAeW,2BAAjD,EAA8E,CAA9E;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,UAAhB;AACI,kBAAI9F,IAAJ,EAAU;AACN,qBAAKvD,SAAL,CAAewI,SAAf,CAAyBjF,IAAI,CAACkF,EAA9B,EAAkC;AAAA;AAAA,sDAAea,gBAAjD,EAAmE,CAAnE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,cAAhB;AACI,mBAAK5F,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,oDAAeiG,UAAnD,EAA+DhG,UAA/D;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWiG,mBAAhB;AACI,kBAAIC,UAAU,GAAG;AAAA;AAAA,4CAAWC,GAA5B;;AACA,kBAAInG,UAAU,CAACmB,KAAX,CAAiBC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B8E,gBAAAA,UAAU,GAAGlG,UAAU,CAACmB,KAAX,CAAiB,CAAjB,CAAb;AACH;;AACD,kBAAIpB,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,wBAAOgG,UAAP;AACI,uBAAK;AAAA;AAAA,gDAAWC,GAAhB;AACI,yBAAKhG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAeqG,kBAAnD,EAAuEpG,UAAvE;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAWqG,SAAhB;AACI,yBAAKlG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAeuG,2BAAnD,EAAgFtG,UAAhF;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAWuG,MAAhB;AACI,yBAAKpG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAeyG,wBAAnD,EAA6ExG,UAA7E;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAWyG,SAAhB;AACI,yBAAKtG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAe2G,2BAAnD,EAAgF1G,UAAhF;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAW2G,QAAhB;AACI,yBAAKxG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAe6G,yBAAnD,EAA8E5G,UAA9E;AACA;AAfR;AAiBH,eAlBD,MAkBO;AACH,wBAAOkG,UAAP;AACI,uBAAK;AAAA;AAAA,gDAAWC,GAAhB;AACI,yBAAKhG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAe8G,iBAAnD,EAAsE7G,UAAtE;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAWqG,SAAhB;AACI,yBAAKlG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAe+G,0BAAnD,EAA+E9G,UAA/E;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAWuG,MAAhB;AACI,yBAAKpG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAegH,uBAAnD,EAA4E/G,UAA5E;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAWyG,SAAhB;AACI,yBAAKtG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAeiH,0BAAnD,EAA+EhH,UAA/E;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAW2G,QAAhB;AACI,yBAAKxG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAekH,wBAAnD,EAA6EjH,UAA7E;AACA;AAfR;AAiBH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWkH,mBAAhB;AACIhB,cAAAA,UAAU,GAAG;AAAA;AAAA,4CAAWC,GAAxB;;AACA,kBAAInG,UAAU,CAACmB,KAAX,CAAiBC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B8E,gBAAAA,UAAU,GAAGlG,UAAU,CAACmB,KAAX,CAAiB,CAAjB,CAAb;AACH;;AACD,kBAAIpB,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,wBAAOgG,UAAP;AACI,uBAAK;AAAA;AAAA,gDAAWC,GAAhB;AACI,yBAAKhG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAeoH,kBAAnD,EAAuEnH,UAAvE;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAWqG,SAAhB;AACI,yBAAKlG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAeqH,2BAAnD,EAAgFpH,UAAhF;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAWuG,MAAhB;AACI,yBAAKpG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAesH,wBAAnD,EAA6ErH,UAA7E;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAWyG,SAAhB;AACI,yBAAKtG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAeuH,2BAAnD,EAAgFtH,UAAhF;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAW2G,QAAhB;AACI,yBAAKxG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAewH,yBAAnD,EAA8EvH,UAA9E;AACA;AAfR;AAiBH,eAlBD,MAkBO;AACH,wBAAOkG,UAAP;AACI,uBAAK;AAAA;AAAA,gDAAWC,GAAhB;AACI,yBAAKhG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAeyH,iBAAnD,EAAsExH,UAAtE;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAWqG,SAAhB;AACI,yBAAKlG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAe0H,0BAAnD,EAA+EzH,UAA/E;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAWuG,MAAhB;AACI,yBAAKpG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAe2H,uBAAnD,EAA4E1H,UAA5E;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAWyG,SAAhB;AACI,yBAAKtG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAe4H,0BAAnD,EAA+E3H,UAA/E;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAW2G,QAAhB;AACI,yBAAKxG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAe6H,wBAAnD,EAA6E5H,UAA7E;AACA;AAfR;AAiBH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAW6H,iBAAhB;AACI3B,cAAAA,UAAU,GAAG;AAAA;AAAA,4CAAWC,GAAxB;;AACA,kBAAInG,UAAU,CAACmB,KAAX,CAAiBC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B8E,gBAAAA,UAAU,GAAGlG,UAAU,CAACmB,KAAX,CAAiB,CAAjB,CAAb;AACH;;AACD,kBAAIpB,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,wBAAOgG,UAAP;AACI,uBAAK;AAAA;AAAA,gDAAWC,GAAhB;AACI,yBAAKhG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAe+H,gBAAnD,EAAqE9H,UAArE;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAWqG,SAAhB;AACI,yBAAKlG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAegI,yBAAnD,EAA8E/H,UAA9E;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAWuG,MAAhB;AACI,yBAAKpG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAeiI,sBAAnD,EAA2EhI,UAA3E;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAWyG,SAAhB;AACI,yBAAKtG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAekI,yBAAnD,EAA8EjI,UAA9E;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAW2G,QAAhB;AACI,yBAAKxG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAemI,uBAAnD,EAA4ElI,UAA5E;AACA;AAfR;AAiBH,eAlBD,MAkBO;AACH,wBAAOkG,UAAP;AACI,uBAAK;AAAA;AAAA,gDAAWC,GAAhB;AACI,yBAAKhG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAeoI,eAAnD,EAAoEnI,UAApE;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAWqG,SAAhB;AACI,yBAAKlG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAeqI,wBAAnD,EAA6EpI,UAA7E;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAWuG,MAAhB;AACI,yBAAKpG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAesI,qBAAnD,EAA0ErI,UAA1E;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAWyG,SAAhB;AACI,yBAAKtG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAeuI,wBAAnD,EAA6EtI,UAA7E;AACA;;AACJ,uBAAK;AAAA;AAAA,gDAAW2G,QAAhB;AACI,yBAAKxG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,0DAAewI,sBAAnD,EAA2EvI,UAA3E;AACA;AAfR;AAiBH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWwI,YAAhB;AACI,kBAAIxI,UAAU,CAACmB,KAAX,CAAiBC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B;AACH;;AACD,mBAAK3D,IAAL,CAAU,KAAKlB,KAAL,GAAayD,UAAU,CAACmB,KAAX,CAAiB,CAAjB,CAAvB;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWsH,YAAhB;AACI,kBAAIzI,UAAU,CAACmB,KAAX,CAAiBC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B;AACH;;AACD,mBAAK3D,IAAL,CAAU,KAAKxB,KAAL,GAAa+D,UAAU,CAACmB,KAAX,CAAiB,CAAjB,CAAvB;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWuH,oBAAhB;AACI,kBAAI3I,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAe4I,mBAAnD,EAAwE3I,UAAxE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAe6I,kBAAnD,EAAuE5I,UAAvE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAW6I,oBAAhB;AACI,kBAAI9I,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAe+I,mBAAnD,EAAwE9I,UAAxE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAegJ,kBAAnD,EAAuE/I,UAAvE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWgJ,UAAhB;AACI;AACA;;AACJ,iBAAK;AAAA;AAAA,0CAAWC,kBAAhB;AACI,kBAAIlJ,IAAJ,YAAIA,IAAI,CAAEG,SAAV,EAAqB;AACjB,qBAAKC,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAemJ,iBAAnD,EAAsElJ,UAAtE;AACH,eAFD,MAEO;AACH,qBAAKG,wBAAL,CAA8BJ,IAA9B,EAAoC;AAAA;AAAA,sDAAeoJ,gBAAnD,EAAqEnJ,UAArE;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,0CAAWoJ,UAAhB;AACI,kBAAIpJ,UAAU,CAACmB,KAAX,CAAiBC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,qBAAKiI,UAAL,CAAgBrJ,UAAU,CAACmB,KAAX,CAAiB,CAAjB,CAAhB;AACH;;AACD;;AACJ;AACI;AAnZR;AAqZH;;AACOhB,QAAAA,wBAAwB,CAACJ,IAAD,EAAoBuJ,GAApB,EAAiCtJ,UAAjC,EAA0D;AACtF,cAAI,CAACD,IAAL,EAAW;AACP;AACH;;AACD,cAAIC,UAAU,CAACmB,KAAX,CAAiBC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B;AACH;;AACD,eAAK5E,SAAL,CAAewI,SAAf,CAAyBjF,IAAI,CAACkF,EAA9B,EAAkCqE,GAAlC,EAAuCtJ,UAAU,CAACmB,KAAX,CAAiB,CAAjB,CAAvC;AACH;;AACDoI,QAAAA,gBAAgB,CAACxJ,IAAD,EAAaC,UAAb,EAAsC;AAClD,eAAKxD,SAAL,CAAegN,YAAf,CAA4BzJ,IAAI,CAACkF,EAAjC;AACH;;AAEDwE,QAAAA,YAAY,CAACC,KAAD,EAAgB,CACxB;AACH,SA1kByC,CA4kB1C;AACA;AACA;;;AACOC,QAAAA,SAAS,GAAmB;AAC/B;AACA,iBAAO,IAAP;AACH,SAllByC,CAolB1C;;;AACAN,QAAAA,UAAU,CAACO,GAAD,EAAc,CACpB;AACH,SAvlByC,CAylB1C;;;AACkB,YAAdC,cAAc,GAAU;AAAC,iBAAO,CAAP;AAAS,SA1lBI,CA2lB1C;;;AACgB,YAAZC,YAAY,GAAU;AAAC,iBAAO,CAAP;AAAS,SA5lBM,CA6lB1C;;;AACkB,YAAdC,cAAc,GAAU;AAAC,iBAAO,CAAP;AAAS,SA9lBI,CA+lB1C;;;AACgB,YAAZC,YAAY,GAAU;AAAC,iBAAO,CAAP;AAAS,SAhmBM,CAimB1C;;;AACc,YAAVC,UAAU,GAAU;AAAC,iBAAO,CAAP;AAAS;;AAlmBQ,O;;;;;iBAKpB,I;;;;;;;iBAEC,I;;;;;;;iBAEM,I;;;;;;;iBAEN,I", "sourcesContent": ["import { _decorator, Label, math, Node, Sprite, tween, Tween } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\nimport { DamageType, EffectParam, EffectType } from 'db://assets/bundles/common/script/autogen/luban/schema';\r\nimport { AttributeConst } from 'db://assets/bundles/common/script/const/AttributeConst';\r\nimport { AttributeData } from 'db://assets/bundles/common/script/data/base/AttributeData';\r\nimport FBoxCollider from 'db://assets/bundles/common/script/game/collider-system/FBoxCollider';\r\nimport FCircleCollider from 'db://assets/bundles/common/script/game/collider-system/FCircleCollider';\r\nimport FPolygonCollider from 'db://assets/bundles/common/script/game/collider-system/FPolygonCollider';\r\nimport Entity from 'db://assets/bundles/common/script/game/ui/base/Entity';\r\nimport { GameEnum } from 'db://assets/bundles/common/script/game/const/GameEnum';\r\nimport BuffComp, { Buff } from './skill/BuffComp';\r\nimport SkillComp from './skill/SkillComp';\r\nimport { GameIns } from 'db://assets/bundles/common/script/game/GameIns';\r\n\r\n\r\n@ccclass('PlaneBase')\r\nexport default class PlaneBase extends Entity {\r\n    constructor() {\r\n        super();\r\n    }\r\n    @property(Node)\r\n    hpNode: Node | null = null;\r\n    @property(Sprite)\r\n    hpBar: Sprite | null = null; // 血条\r\n    @property(Sprite)\r\n    hpAniSprite: Sprite | null = null; // 血条动画条\r\n    @property(Label)\r\n    hpfont: Label | null = null; // 血条文本\r\n\r\n    enemy = true; // 是否为敌机\r\n    type = 0; // 敌人类型\r\n    bDamageable: boolean = true; // 是否可以被造成伤害\r\n\r\n    get maxHp(): number{\r\n        return  this.attribute.getMaxHP();\r\n    };\r\n    curHp: number = 0;\r\n    hurtTime:number = 0;\r\n\r\n    collideComp: FCircleCollider | FBoxCollider | FPolygonCollider | null = null; // 碰撞组件\r\n\r\n    private _skillComp: SkillComp | null = null;\r\n    private _buffComp: BuffComp | null = null;\r\n\r\n    // TODO 临时做法，后续应该挪到 PlaneBase\r\n    private _attributeData: AttributeData = new AttributeData();\r\n\r\n    init() {\r\n        this._skillComp = new SkillComp();\r\n        this.addComp(\"skill\", this._skillComp);\r\n        this._buffComp = new BuffComp();\r\n        this.addComp(\"buff\", this._buffComp)\r\n        super.init();\r\n    }\r\n\r\n    get skillComp() {\r\n        return this._skillComp!;\r\n    }\r\n\r\n    get buffComp() {\r\n        return this._buffComp!;\r\n    }\r\n\r\n    get attribute(): AttributeData {\r\n        return this._attributeData;\r\n    }\r\n\r\n    set colliderEnabled(value: boolean) {\r\n        if (this.collideComp) {\r\n            this.collideComp.isEnable = value;\r\n        }\r\n    }\r\n    get colliderEnabled(): boolean {\r\n        return this.collideComp ? this.collideComp.isEnable : false;\r\n    }\r\n\r\n    CastSkill(skillID: number) {\r\n        this.skillComp.Cast(this, skillID);\r\n    }\r\n\r\n    addHp(heal: number) {\r\n        this.curHp = Math.min(\r\n            this.maxHp,\r\n            this.curHp + heal\r\n        );\r\n        this.updateHpUI();;\r\n    }\r\n\r\n    hurt(damage: number) {\r\n        if (this.isDead) {\r\n            return;\r\n        }\r\n        this.hurtTime = GameIns.battleManager._gameTime;\r\n        this.cutHp(damage);\r\n        this.playHurtAnim();\r\n        if (this.curHp <= 0) {\r\n            this.toDie();\r\n        }\r\n    }\r\n\r\n    get collisionLevel() {\r\n        return 0;\r\n    }\r\n    get collisionHurt() {\r\n        return 0;\r\n    }\r\n\r\n    // 撞机\r\n    collisionPlane(plane: PlaneBase) {\r\n        if (this.isDead || plane.isDead) {\r\n            return;\r\n        }\r\n        if (this.collisionLevel > plane.collisionLevel) {\r\n            return\r\n        }\r\n        let hurt:number\r\n        if (this.collisionLevel < plane.collisionLevel) {\r\n            hurt = Math.max(this.maxHp, plane.collisionHurt)\r\n        } else {\r\n            hurt = plane.collisionHurt == -1 ? this.maxHp : plane.collisionHurt\r\n        }\r\n\r\n        hurt = (hurt - this.attribute.getFinialAttributeByOutInKey(\r\n                AttributeConst.CollisionHurtResistanceOutAdd, AttributeConst.CollisionHurtResistanceOutPer, \r\n                AttributeConst.CollisionHurtResistanceInAdd, AttributeConst.CollisionHurtResistanceInPer))\r\n            * (1 - this.attribute.getFinalAttributeByKey(AttributeConst.CollisionHurtDerateOut))\r\n            * (1 - this.attribute.getFinalAttributeByKey(AttributeConst.CollisionHurtDerateIn));\r\n        this.hurt(hurt);\r\n    }\r\n\r\n    /**\r\n     * 减少血量\r\n     * @param {number} damage 受到的伤害值\r\n     */\r\n    cutHp(damage: number) {\r\n        const newHp = this.curHp - damage;\r\n        this.curHp = Math.max(0, newHp);\r\n\r\n        this.updateHpUI();\r\n    }\r\n\r\n    toDie(destroyType: GameEnum.EnemyDestroyType = GameEnum.EnemyDestroyType.Die): boolean {\r\n        if (this.isDead) {\r\n            return false\r\n        }\r\n        this.isDead = true;\r\n        this.colliderEnabled = false;\r\n        return true\r\n    }\r\n    /**\r\n     * 更新血量显示\r\n     */\r\n    updateHpUI() {\r\n        if (this.hpBar) {\r\n            // 更新血条前景的填充范围\r\n            this.hpBar.fillRange = this.curHp / this.maxHp;\r\n\r\n            if (this.hpAniSprite) {\r\n                // 计算血条动画时间\r\n                const duration = Math.abs(this.hpAniSprite.fillRange - this.hpBar.fillRange);\r\n\r\n                Tween.stopAllByTarget(this.hpAniSprite);\r\n                // 血条中间部分的动画\r\n                tween(this.hpAniSprite)\r\n                    .to(duration, { fillRange: this.hpBar.fillRange })\r\n                    .call(() => {\r\n\r\n                    })\r\n                    .start();\r\n            }\r\n        }\r\n\r\n        // 更新血量文字\r\n        this.hpfont && (this.hpfont!.string = this.curHp.toFixed(0));\r\n    }\r\n\r\n    playHurtAnim() {\r\n        // 子类实现\r\n    }\r\n\r\n    ApplyBuffEffect(buff: Buff | null, effectData: EffectParam) {\r\n        switch (effectData.type) {\r\n            case EffectType.AttrMaxHPPer:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.MaxHPOutPer, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.MaxHPInPer, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrMaxHPAdd:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.MaxHPOutAdd, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.MaxHPInAdd, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrHPRecoveryPer:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.HPRecoveryOutPer, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.HPRecoveryInPer, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrHPRecoveryAdd:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.HPRecoveryOutAdd, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.HPRecoveryInAdd, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrHPRecoveryMaxHPPerAdd:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.MaxHPRecoveryRateOut, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.MaxHPRecoveryRateIn, effectData);\r\n                }\r\n                break;\r\n            case EffectType.HealMaxHPPer:\r\n                if (effectData.param.length >= 1) {\r\n                    this.addHp(this.maxHp * effectData.param[0]/10000);\r\n                }\r\n                break;\r\n            case EffectType.HealLoseHPPer:\r\n                if (effectData.param.length >= 1) {\r\n                    this.addHp((this.maxHp - this.curHp) * effectData.param[0]/10000);\r\n                }\r\n                break;\r\n            case EffectType.HealHP:\r\n                if (effectData.param.length >= 1) {\r\n                    this.addHp(effectData.param[0]);\r\n                }\r\n                break;\r\n            case EffectType.AttrAttackPer:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.AttackOutPer, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.AttackInPer, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrAttackAdd:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.AttackOutAdd, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.AttackInAdd, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrAttackBossPer:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.BossHurtBonusOut, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.BossHurtBonusIn, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrAttackNormalPer:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.NormalHurtBonusOut, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.NormalHurtBonusIn, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrFortunatePer:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.FortunateOutPer, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.FortunateInPer, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrFortunateAdd:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.FortunateOutAdd, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.FortunateInAdd, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrMissAdd:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.MissRateOut, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.MissRateIn, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrBulletHurtResistancePer:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtResistanceOutPer, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtResistanceInPer, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrBulletHurtResistanceAdd:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtResistanceOutAdd, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtResistanceInAdd, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrBulletHurtDerateAdd:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtDerateOut, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtDerateIn, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrCollisionHurtResistancePer:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.CollisionHurtResistanceOutPer, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.CollisionHurtResistanceInPer, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrCollisionHurtResistanceAdd:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.CollisionHurtResistanceOutAdd, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.CollisionHurtResistanceInAdd, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrCollisionHurtDerateAdd:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.CollisionHurtDerateOut, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.CollisionHurtDerateIn, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrFinalScoreAdd:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.FinalScoreRateOut, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.FinalScoreRateIn, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrKillScoreAdd:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.KillScoreRateOut, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.KillScoreRateIn, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrEnergyRecoveryPerAdd:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergyRecoveryOutPer, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergyRecoveryInPer, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrEnergyRecoveryAdd:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergyRecoveryOutAdd, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergyRecoveryInAdd, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrPickRadiusPer:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.PickRadiusOutPer, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.PickRadiusInPer, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrPickRadiusAdd:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.PickRadiusOutAdd, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.PickRadiusInAdd, effectData);\r\n                }\r\n                break;\r\n            case EffectType.ApplyBuff:\r\n                if (effectData.param.length < 2) {\r\n                    return;\r\n                }\r\n                const buffID = effectData.param[0];\r\n                const target = effectData.param[1];\r\n                SkillComp.forEachByTargetType(this, target, (entity) => {\r\n                    entity.buffComp.ApplyBuff(buff?.isOutside||false, buffID);\r\n                })\r\n                break;\r\n            case EffectType.ImmuneBulletHurt:\r\n                if (buff) {\r\n                    this.attribute.addModify(buff.id, AttributeConst.StatusImmuneBulletHurt, 1);\r\n                }\r\n                break;\r\n            case EffectType.ImmuneCollisionHurt:\r\n                if (buff) {\r\n                    this.attribute.addModify(buff.id, AttributeConst.StatusImmuneCollisionHurt, 1);\r\n                }\r\n                break;\r\n            case EffectType.IgnoreBullet:\r\n                if (buff) {\r\n                    this.attribute.addModify(buff.id, AttributeConst.StatusIgnoreBullet, 1);\r\n                }\r\n                break;\r\n            case EffectType.IgnoreCollision:\r\n                if (buff) {\r\n                    this.attribute.addModify(buff.id, AttributeConst.StatusIgnoreCollision, 1);\r\n                }\r\n                break;\r\n            case EffectType.ImmuneNuclearHurt:\r\n                if (buff) {\r\n                    this.attribute.addModify(buff.id, AttributeConst.StatusImmuneNuclearHurt, 1);\r\n                }\r\n                break;\r\n            case EffectType.ImmuneActiveSkillHurt:\r\n                if (buff) {\r\n                    this.attribute.addModify(buff.id, AttributeConst.StatusImmuneActiveSkillHurt, 1);\r\n                }\r\n                break;\r\n            case EffectType.Invincible:\r\n                if (buff) {\r\n                    this.attribute.addModify(buff.id, AttributeConst.StatusInvincible, 1);\r\n                }\r\n                break;\r\n            case EffectType.AttrNuclearMax:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.NuclearMax, effectData);\r\n                break;\r\n            case EffectType.AttrBulletAttackAdd:\r\n                let damageType = DamageType.ALL;\r\n                if (effectData.param.length > 1) {\r\n                    damageType = effectData.param[1];\r\n                }\r\n                if (buff?.isOutside) {\r\n                    switch(damageType) {\r\n                        case DamageType.ALL:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletAttackOutAdd, effectData);\r\n                            break;\r\n                        case DamageType.EXPLOSIVE:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.ExplosiveBulletAttackOutAdd, effectData);\r\n                            break;\r\n                        case DamageType.NORMAL:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.NormalBulletAttackOutAdd, effectData);\r\n                            break;\r\n                        case DamageType.ENERGETIC:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergeticBulletAttackOutAdd, effectData);\r\n                            break;\r\n                        case DamageType.PHYSICAL:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.PhysicsBulletAttackOutAdd, effectData);\r\n                            break;\r\n                    }\r\n                } else {\r\n                    switch(damageType) {\r\n                        case DamageType.ALL:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletAttackInAdd, effectData);\r\n                            break;\r\n                        case DamageType.EXPLOSIVE:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.ExplosiveBulletAttackInAdd, effectData);\r\n                            break;\r\n                        case DamageType.NORMAL:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.NormalBulletAttackInAdd, effectData);\r\n                            break;\r\n                        case DamageType.ENERGETIC:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergeticBulletAttackInAdd, effectData);\r\n                            break;\r\n                        case DamageType.PHYSICAL:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.PhysicsBulletAttackInAdd, effectData);\r\n                            break;\r\n                    }\r\n                }\r\n                break;\r\n            case EffectType.AttrBulletAttackPer:\r\n                damageType = DamageType.ALL;\r\n                if (effectData.param.length > 1) {\r\n                    damageType = effectData.param[1];\r\n                }\r\n                if (buff?.isOutside) {\r\n                    switch(damageType) {\r\n                        case DamageType.ALL:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletAttackOutPer, effectData);\r\n                            break;\r\n                        case DamageType.EXPLOSIVE:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.ExplosiveBulletAttackOutPer, effectData);\r\n                            break;\r\n                        case DamageType.NORMAL:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.NormalBulletAttackOutPer, effectData);\r\n                            break;\r\n                        case DamageType.ENERGETIC:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergeticBulletAttackOutPer, effectData);\r\n                            break;\r\n                        case DamageType.PHYSICAL:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.PhysicsBulletAttackOutPer, effectData);\r\n                            break;\r\n                    }\r\n                } else {\r\n                    switch(damageType) {\r\n                        case DamageType.ALL:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletAttackInPer, effectData);\r\n                            break;\r\n                        case DamageType.EXPLOSIVE:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.ExplosiveBulletAttackInPer, effectData);\r\n                            break;\r\n                        case DamageType.NORMAL:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.NormalBulletAttackInPer, effectData);\r\n                            break;\r\n                        case DamageType.ENERGETIC:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergeticBulletAttackInPer, effectData);\r\n                            break;\r\n                        case DamageType.PHYSICAL:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.PhysicsBulletAttackInPer, effectData);\r\n                            break;\r\n                    }\r\n                }\r\n                break;\r\n            case EffectType.AttrBulletHurtFix:\r\n                damageType = DamageType.ALL;\r\n                if (effectData.param.length > 1) {\r\n                    damageType = effectData.param[1];\r\n                }\r\n                if (buff?.isOutside) {\r\n                    switch(damageType) {\r\n                        case DamageType.ALL:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtFixOut, effectData);\r\n                            break;\r\n                        case DamageType.EXPLOSIVE:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.ExplosiveBulletHurtFixOut, effectData);\r\n                            break;\r\n                        case DamageType.NORMAL:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.NormalBulletHurtFixOut, effectData);\r\n                            break;\r\n                        case DamageType.ENERGETIC:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergeticBulletHurtFixOut, effectData);\r\n                            break;\r\n                        case DamageType.PHYSICAL:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.PhysicsBulletHurtFixOut, effectData);\r\n                            break;\r\n                    }\r\n                } else {\r\n                    switch(damageType) {\r\n                        case DamageType.ALL:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtFixIn, effectData);\r\n                            break;\r\n                        case DamageType.EXPLOSIVE:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.ExplosiveBulletHurtFixIn, effectData);\r\n                            break;\r\n                        case DamageType.NORMAL:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.NormalBulletHurtFixIn, effectData);\r\n                            break;\r\n                        case DamageType.ENERGETIC:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergeticBulletHurtFixIn, effectData);\r\n                            break;\r\n                        case DamageType.PHYSICAL:\r\n                            this.ApplyBuffAttributeEffect(buff, AttributeConst.PhysicsBulletHurtFixIn, effectData);\r\n                            break;\r\n                    }\r\n                }\r\n                break;\r\n            case EffectType.HurtMaxHPPer:\r\n                if (effectData.param.length < 1) {\r\n                    return;\r\n                }\r\n                this.hurt(this.maxHp * effectData.param[0]);\r\n                break;\r\n            case EffectType.HurtCurHPPer:\r\n                if (effectData.param.length < 1) {\r\n                    return;\r\n                }\r\n                this.hurt(this.curHp * effectData.param[0]);\r\n                break;\r\n            case EffectType.AttrNuclearAttackPer:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.NuclearAttackOutPer, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.NuclearAttackInPer, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AttrNuclearAttackAdd:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.NuclearAttackOutAdd, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.NuclearAttackInAdd, effectData);\r\n                }\r\n                break;\r\n            case EffectType.FireBullet:\r\n                // TODO not implement\r\n                break;\r\n            case EffectType.AttrNuclearHurtFix:\r\n                if (buff?.isOutside) {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.NuclearHurtFixOut, effectData);\r\n                } else {\r\n                    this.ApplyBuffAttributeEffect(buff, AttributeConst.NuclearHurtFixIn, effectData);\r\n                }\r\n                break;\r\n            case EffectType.AddNuclear:\r\n                if (effectData.param.length > 0) {\r\n                    this.addNuclear(effectData.param[0])\r\n                }\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    }\r\n    private ApplyBuffAttributeEffect(buff: Buff | null, key: number, effectData: EffectParam) {\r\n        if (!buff) {\r\n            return;\r\n        }\r\n        if (effectData.param.length < 1) {\r\n            return;\r\n        }\r\n        this.attribute.addModify(buff.id, key, effectData.param[0]);\r\n    }\r\n    RemoveBuffEffect(buff: Buff, effectData: EffectParam) {\r\n        this.attribute.removeModify(buff.id);\r\n    }\r\n\r\n    setAnimSpeed(speed: number) {\r\n        // 子类实现\r\n    }\r\n\r\n    // 获取当前的攻击目标\r\n    // 对于敌机，返回玩家飞机；\r\n    // 对于玩家飞机，可以按策划规则（距离或者其他规则）\r\n    public getTarget(): PlaneBase|null {\r\n        // 子类实现\r\n        return null;\r\n    }\r\n\r\n    // 增加核弹\r\n    addNuclear(num: number) {\r\n        // 子类实现\r\n    }\r\n\r\n    // 获取已拾取宝石数量\r\n    get pickDiamondNum():number {return 0}\r\n    // 获取已击杀敌机数量\r\n    get killEnemyNum():number {return 0}\r\n    // 获取已使用核弹数量\r\n    get usedNuclearNum():number {return 0}\r\n    // 获取已使用大招数量\r\n    get usedSuperNum():number {return 0}\r\n    // 获取当前核弹数量\r\n    get nuclearNum():number {return 0}\r\n}"]}