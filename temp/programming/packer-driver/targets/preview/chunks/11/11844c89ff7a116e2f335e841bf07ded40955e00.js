System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12", "__unresolved_13", "__unresolved_14", "__unresolved_15"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, instantiate, Node, Prefab, size, UIOpacity, view, MyApp, AttributeConst, Plane, Bullet, Emitter, FBoxCollider, ColliderGroupType, GameConst, GameResourceList, eEntityTag, <PERSON><PERSON><PERSON><PERSON>, EnemyPlaneBase, MainPlaneStat, GameIns, PlaneBase, _dec, _dec2, _dec3, _dec4, _class, _class2, _descriptor, _descriptor2, _descriptor3, _crd, ccclass, property, MainPlane;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeConst(extras) {
    _reporterNs.report("AttributeConst", "db://assets/bundles/common/script/const/AttributeConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeData(extras) {
    _reporterNs.report("AttributeData", "db://assets/bundles/common/script/data/base/AttributeData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneData(extras) {
    _reporterNs.report("PlaneData", "db://assets/bundles/common/script/data/plane/PlaneData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlane(extras) {
    _reporterNs.report("Plane", "db://assets/bundles/common/script/ui/Plane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "../../../bullet/Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "../../../bullet/Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFBoxCollider(extras) {
    _reporterNs.report("FBoxCollider", "../../../collider-system/FBoxCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "../../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderGroupType(extras) {
    _reporterNs.report("ColliderGroupType", "../../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameResourceList(extras) {
    _reporterNs.report("GameResourceList", "../../../const/GameResourceList", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEntityTag(extras) {
    _reporterNs.report("eEntityTag", "../../base/Entity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEffectLayer(extras) {
    _reporterNs.report("EffectLayer", "../../layer/EffectLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlaneBase(extras) {
    _reporterNs.report("EnemyPlaneBase", "../enemy/EnemyPlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainPlaneStat(extras) {
    _reporterNs.report("MainPlaneStat", "./MainPlaneStat", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "db://assets/bundles/common/script/game/ui/plane/PlaneBase", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      instantiate = _cc.instantiate;
      Node = _cc.Node;
      Prefab = _cc.Prefab;
      size = _cc.size;
      UIOpacity = _cc.UIOpacity;
      view = _cc.view;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      AttributeConst = _unresolved_3.AttributeConst;
    }, function (_unresolved_4) {
      Plane = _unresolved_4.Plane;
    }, function (_unresolved_5) {
      Bullet = _unresolved_5.Bullet;
    }, function (_unresolved_6) {
      Emitter = _unresolved_6.Emitter;
    }, function (_unresolved_7) {
      FBoxCollider = _unresolved_7.default;
    }, function (_unresolved_8) {
      ColliderGroupType = _unresolved_8.ColliderGroupType;
    }, function (_unresolved_9) {
      GameConst = _unresolved_9.GameConst;
    }, function (_unresolved_10) {
      GameResourceList = _unresolved_10.default;
    }, function (_unresolved_11) {
      eEntityTag = _unresolved_11.eEntityTag;
    }, function (_unresolved_12) {
      EffectLayer = _unresolved_12.default;
    }, function (_unresolved_13) {
      EnemyPlaneBase = _unresolved_13.default;
    }, function (_unresolved_14) {
      MainPlaneStat = _unresolved_14.default;
    }, function (_unresolved_15) {
      GameIns = _unresolved_15.GameIns;
    }, function (_unresolved_16) {
      PlaneBase = _unresolved_16.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "81e41b6hKRNqYJbS6uK9LDf", "MainPlane", undefined);

      __checkObsolete__(['_decorator', 'instantiate', 'Node', 'Prefab', 'size', 'UIOpacity', 'view']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("MainPlane", MainPlane = (_dec = ccclass("MainPlane"), _dec2 = property(Node), _dec3 = property(Node), _dec4 = property(Node), _dec(_class = (_class2 = class MainPlane extends (_crd && PlaneBase === void 0 ? (_reportPossibleCrUseOfPlaneBase({
        error: Error()
      }), PlaneBase) : PlaneBase) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "planeParent", _descriptor, this);

          _initializerDefineProperty(this, "NodeEmitter", _descriptor2, this);

          _initializerDefineProperty(this, "InvincibleNode", _descriptor3, this);

          this.m_moveEnable = true;
          // 是否允许移动
          this.emitterComp = null;
          // 发射器
          this._hurtActTime = 0;
          // 受伤动画时间
          this._hurtActDuration = 0.5;
          // 受伤动画持续时间
          this._planeData = null;
          //飞机数据
          this._plane = null;
          //飞机显示节点
          this._fireEnable = true;
          //是否允许射击
          this._unColliderTime = 0;
          //无敌时间
          this.statData = new (_crd && MainPlaneStat === void 0 ? (_reportPossibleCrUseOfMainPlaneStat({
            error: Error()
          }), MainPlaneStat) : MainPlaneStat)();
          this.hpRecoveryTime = 0;
          this._nuclearNum = 0;
        }

        get nuclearNum() {
          return this._nuclearNum;
        }

        addNuclear(num) {
          this._nuclearNum += num;
        }

        onLoad() {
          this.collideComp = this.getComponent(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
            error: Error()
          }), FBoxCollider) : FBoxCollider) || this.addComponent(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
            error: Error()
          }), FBoxCollider) : FBoxCollider);
          this.collideComp.init(this, size(180, 150)); // 初始化碰撞组件

          this.collideComp.groupType = (_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
            error: Error()
          }), ColliderGroupType) : ColliderGroupType).PLAYER;
        } // 纯表现层业务，请勿将逻辑代码写到这里


        update(dt) {
          dt = dt * (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.animSpeed;
          this._hurtActTime += dt;

          if (this._unColliderTime > 0) {
            this._unColliderTime -= dt;

            if (this._unColliderTime <= 0) {
              this.cancelUncollide();
            }
          }
        }

        updateGameLogic(dt) {
          super.updateGameLogic(dt);

          while (this.hpRecoveryTime <= (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager._gameTime) {
            this.hpRecoveryTime += 1;
            var hpRecovery = this.attribute.getHPRecovery();
            this.addHp(hpRecovery);
          }
        }

        initPlane(planeData) {
          var _this$planeParent;

          this._planeData = planeData;
          this._nuclearNum = planeData.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).NuclearMax); //加载飞机显示

          var plane = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).planeMgr.getPlane(planeData);
          this._plane = plane.getComponent(_crd && Plane === void 0 ? (_reportPossibleCrUseOfPlane({
            error: Error()
          }), Plane) : Plane);
          (_this$planeParent = this.planeParent) == null || _this$planeParent.addChild(plane);
          this.addTag((_crd && eEntityTag === void 0 ? (_reportPossibleCrUseOfeEntityTag({
            error: Error()
          }), eEntityTag) : eEntityTag).Player); //设置飞机发射组件

          this.setEmitter();
          this.resetPlane();
        }

        resetPlane() {
          // 禁用射击
          this.setFireEnable(false);
          this.setMoveAble(false);
          this.colliderEnabled = false;
          this.isDead = false;
          this.curHp = this.maxHp;
          this.updateHpUI();
          this.hpRecoveryTime = 0;

          this._plane.reset();

          var targetY = -view.getVisibleSize().height * 0.7;
          this.node.setPosition(0, targetY);
        }

        setEmitter() {
          //后期根据飞机的数据，加载不同的发送组件预制体
          var path = (_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).EmitterPrefabPath + "Emitter_main_01";
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadAsync(path, Prefab).then(prefab => {
            var _this$NodeEmitter;

            var node = instantiate(prefab);
            (_this$NodeEmitter = this.NodeEmitter) == null || _this$NodeEmitter.addChild(node);
            node.setPosition(0, 0);
            this.emitterComp = node.getComponent(_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
              error: Error()
            }), Emitter) : Emitter);
            this.emitterComp.setEntity(this);
            this.emitterComp.setIsActive(this._fireEnable);
            this.emitterComp.emitterId = 1000001;
          });
        }
        /**
         * 主飞机入场动画
         */


        planeIn() {
          this.hpNode.getComponent(UIOpacity).opacity = 0;
          this.node.getComponent(UIOpacity).opacity = 0;
          this.scheduleOnce(() => {
            var _this$_plane;

            this.node.getComponent(UIOpacity).opacity = 255;
            (_this$_plane = this._plane) == null || _this$_plane.onEnter(() => {
              this.hpNode.getComponent(UIOpacity).opacity = 255;
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.onPlaneIn();
            });
          }, 0.7);
        }
        /**
         * 碰撞处理
         * @param {Object} collision 碰撞对象
         */


        onCollide(collision) {
          var damage = 0;

          if (collision.entity instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet)) {
            if (this.attribute.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
              error: Error()
            }), AttributeConst) : AttributeConst).StatusImmuneBulletHurt) == 0) {
              damage = collision.entity.calcDamage(this);
            }

            if (damage > 0) {
              this.hurt(damage);
            }
          } else if (collision.entity instanceof (_crd && EnemyPlaneBase === void 0 ? (_reportPossibleCrUseOfEnemyPlaneBase({
            error: Error()
          }), EnemyPlaneBase) : EnemyPlaneBase)) {
            this.collisionPlane(collision.entity);
          }
        }
        /**
         * 控制飞机移动
         * @param {number} moveX 水平方向的移动量
         * @param {number} moveY 垂直方向的移动量
         */


        onControl(posX, posY) {
          if (!this.isDead && this.m_moveEnable) {
            var _this$_plane2;

            var isLeft = posX < this.node.position.x;
            (_this$_plane2 = this._plane) == null || _this$_plane2.onMoveCommand(isLeft); // 限制飞机移动范围

            var width = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
              error: Error()
            }), GameConst) : GameConst).ViewBattleWidth / 2;
            posX = Math.min(width, posX);
            posX = Math.max(-width, posX);
            posY = Math.min(0, posY);
            posY = Math.max(-(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
              error: Error()
            }), GameConst) : GameConst).ViewHeight, posY);
            this.node.setPosition(posX, posY);
          }
        }

        begine(isContinue) {
          if (isContinue === void 0) {
            isContinue = false;
          }

          this.setFireEnable(true);
          this.setMoveAble(true);

          if (isContinue) {
            this.setUncollideByTime(2);
          } else {
            this.cancelUncollide();
          }
        }

        revive() {
          this.node.active = true;
          this.begine(true);
        } //实现父类的方法


        playHurtAnim() {
          if (this._hurtActTime > this._hurtActDuration) {
            this._hurtActTime = 0; // 显示红屏效果

            (_crd && EffectLayer === void 0 ? (_reportPossibleCrUseOfEffectLayer({
              error: Error()
            }), EffectLayer) : EffectLayer).me.showRedScreen();
          }
        }

        toDie() {
          if (!super.toDie()) {
            return false;
          }

          this.node.active = false;
          this.resetPlane();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.battleDie();
          return true;
        }

        get collisionLevel() {
          var _config;

          return ((_config = this._planeData.config) == null ? void 0 : _config.collideLevel) || 0;
        }

        get collisionHurt() {
          var _config2;

          return ((_config2 = this._planeData.config) == null ? void 0 : _config2.collideDamage) || 0;
        }

        get attribute() {
          return this._planeData;
        }

        getAttack() {
          return this._planeData.getAttack();
        }

        setMoveAble(enable) {
          this.m_moveEnable = enable;
        }

        setFireEnable(enable) {
          this._fireEnable = enable;

          if (this.emitterComp) {
            this.emitterComp.setIsActive(enable);
          }
        } //设置无敌状态


        setUncollideByTime(time) {
          this._unColliderTime = time;
          this.colliderEnabled = false;
          this.InvincibleNode.active = true;
        } //取消无敌状态


        cancelUncollide() {
          this.colliderEnabled = true;
          this.InvincibleNode.active = false;
        }

        setAnimSpeed(speed) {
          if (this._plane) {
            this._plane.setAnimSpeed(speed);
          }
        }

        get pickDiamondNum() {
          return this.statData.pickDiamond;
        }

        get killEnemyNum() {
          return this.statData.killEnemy;
        }

        get usedNuclearNum() {
          return this.statData.usedNuclear;
        }

        get usedSuperNum() {
          return this.statData.usedSuper;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "planeParent", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "NodeEmitter", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "InvincibleNode", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=11844c89ff7a116e2f335e841bf07ded40955e00.js.map