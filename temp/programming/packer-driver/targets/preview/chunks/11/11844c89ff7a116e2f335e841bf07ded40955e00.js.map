{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlane.ts"], "names": ["_decorator", "instantiate", "Node", "Prefab", "size", "UIOpacity", "view", "MyApp", "AttributeConst", "Plane", "Bullet", "Emitter", "FBoxCollider", "ColliderGroupType", "GameConst", "GameResourceList", "eEntityTag", "EffectLayer", "EnemyPlaneBase", "MainPlaneStat", "GameIns", "PlaneBase", "ccclass", "property", "MainPlane", "m_moveEnable", "emitter<PERSON>omp", "_hurtActTime", "_hurtActDuration", "_planeData", "_plane", "_fireEnable", "_unColliderTime", "statData", "hpRecoveryTime", "_nuclearNum", "nuclearNum", "addNuclear", "num", "onLoad", "collide<PERSON>omp", "getComponent", "addComponent", "init", "groupType", "PLAYER", "update", "dt", "battleManager", "animSpeed", "cancelUncollide", "updateGameLogic", "_gameTime", "hpRecovery", "attribute", "getHPRecovery", "addHp", "initPlane", "planeData", "getFinalAttributeByKey", "NuclearMax", "plane", "planeMgr", "getPlane", "planeParent", "<PERSON><PERSON><PERSON><PERSON>", "addTag", "Player", "setEmitter", "reset<PERSON>lane", "setFireEnable", "setMoveAble", "colliderEnabled", "isDead", "curHp", "maxHp", "updateHpUI", "reset", "targetY", "getVisibleSize", "height", "node", "setPosition", "path", "EmitterPrefabPath", "resMgr", "loadAsync", "then", "prefab", "NodeEmitter", "setEntity", "setIsActive", "emitterId", "planeIn", "hpNode", "opacity", "scheduleOnce", "onEnter", "onPlaneIn", "onCollide", "collision", "damage", "entity", "StatusImmuneBulletHurt", "calcDamage", "hurt", "collisionPlane", "onControl", "posX", "posY", "isLeft", "position", "x", "onMoveCommand", "width", "ViewBattleWidth", "Math", "min", "max", "ViewHeight", "begine", "isContinue", "setUncollideByTime", "revive", "active", "playHurtAnim", "me", "showRedScreen", "to<PERSON><PERSON>", "battleDie", "collisionLevel", "config", "collideLevel", "collisionHurt", "collideDamage", "getAttack", "enable", "time", "InvincibleNode", "setAnimSpeed", "speed", "pickDiamond<PERSON>um", "<PERSON><PERSON><PERSON><PERSON>", "killEnemyNum", "killEnemy", "usedNuclearNum", "usedNuclear", "usedSuperNum", "usedSuper"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AAGxDC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,c,iBAAAA,c;;AAGAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,Y;;AACaC,MAAAA,iB,iBAAAA,iB;;AACXC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,gB;;AACEC,MAAAA,U,kBAAAA,U;;AACFC,MAAAA,W;;AACAC,MAAAA,c;;AACAC,MAAAA,a;;AACEC,MAAAA,O,kBAAAA,O;;AACFC,MAAAA,S;;;;;;;;;OAlBD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBvB,U;;2BAqBjBwB,S,WADZF,OAAO,CAAC,WAAD,C,UAGHC,QAAQ,CAACrB,IAAD,C,UAERqB,QAAQ,CAACrB,IAAD,C,UAERqB,QAAQ,CAACrB,IAAD,C,2BAPb,MACasB,SADb;AAAA;AAAA,kCACyC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eASrCC,YATqC,GAStB,IATsB;AAShB;AATgB,eAUrCC,WAVqC,GAUP,IAVO;AAUD;AAVC,eAYrCC,YAZqC,GAYtB,CAZsB;AAYnB;AAZmB,eAarCC,gBAbqC,GAalB,GAbkB;AAab;AAba,eAerCC,UAfqC,GAeN,IAfM;AAeD;AAfC,eAgBrCC,MAhBqC,GAgBd,IAhBc;AAgBT;AAhBS,eAiBrCC,WAjBqC,GAiBvB,IAjBuB;AAiBlB;AAjBkB,eAkBrCC,eAlBqC,GAkBX,CAlBW;AAkBT;AAlBS,eAmBrCC,QAnBqC,GAmBX;AAAA;AAAA,+CAnBW;AAAA,eAqB7BC,cArB6B,GAqBZ,CArBY;AAAA,eAsB7BC,WAtB6B,GAsBf,CAtBe;AAAA;;AAuBvB,YAAVC,UAAU,GAAG;AACb,iBAAO,KAAKD,WAAZ;AACH;;AACDE,QAAAA,UAAU,CAACC,GAAD,EAAc;AACpB,eAAKH,WAAL,IAAoBG,GAApB;AACH;;AAEDC,QAAAA,MAAM,GAAG;AACL,eAAKC,WAAL,GAAmB,KAAKC,YAAL;AAAA;AAAA,+CAAmC,KAAKC,YAAL;AAAA;AAAA,2CAAtD;AACA,eAAKF,WAAL,CAAkBG,IAAlB,CAAuB,IAAvB,EAA6BvC,IAAI,CAAC,GAAD,EAAM,GAAN,CAAjC,EAFK,CAEyC;;AAC9C,eAAKoC,WAAL,CAAkBI,SAAlB,GAA8B;AAAA;AAAA,sDAAkBC,MAAhD;AACH,SAlCoC,CAoCrC;;;AACAC,QAAAA,MAAM,CAACC,EAAD,EAAa;AACfA,UAAAA,EAAE,GAAGA,EAAE,GAAG;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,SAAhC;AACA,eAAKtB,YAAL,IAAqBoB,EAArB;;AAEA,cAAI,KAAKf,eAAL,GAAuB,CAA3B,EAA8B;AAC1B,iBAAKA,eAAL,IAAwBe,EAAxB;;AACA,gBAAI,KAAKf,eAAL,IAAwB,CAA5B,EAA+B;AAC3B,mBAAKkB,eAAL;AACH;AACJ;AACJ;;AAEDC,QAAAA,eAAe,CAACJ,EAAD,EAAmB;AAC9B,gBAAMI,eAAN,CAAsBJ,EAAtB;;AACA,iBAAO,KAAKb,cAAL,IAAuB;AAAA;AAAA,kCAAQc,aAAR,CAAsBI,SAApD,EAA+D;AAC3D,iBAAKlB,cAAL,IAAuB,CAAvB;AACA,gBAAImB,UAAU,GAAG,KAAKC,SAAL,CAAeC,aAAf,EAAjB;AACA,iBAAKC,KAAL,CAAWH,UAAX;AACH;AACJ;;AAEDI,QAAAA,SAAS,CAACC,SAAD,EAAuB;AAAA;;AAC5B,eAAK7B,UAAL,GAAkB6B,SAAlB;AAEA,eAAKvB,WAAL,GAAmBuB,SAAS,CAACC,sBAAV,CAAiC;AAAA;AAAA,gDAAeC,UAAhD,CAAnB,CAH4B,CAK5B;;AACA,cAAIC,KAAK,GAAG;AAAA;AAAA,8BAAMC,QAAN,CAAeC,QAAf,CAAwBL,SAAxB,CAAZ;AACA,eAAK5B,MAAL,GAAc+B,KAAK,CAACpB,YAAN;AAAA;AAAA,6BAAd;AACA,oCAAKuB,WAAL,+BAAkBC,QAAlB,CAA2BJ,KAA3B;AAEA,eAAKK,MAAL,CAAY;AAAA;AAAA,wCAAWC,MAAvB,EAV4B,CAY5B;;AACA,eAAKC,UAAL;AACA,eAAKC,UAAL;AACH;;AAEDA,QAAAA,UAAU,GAAG;AACT;AACA,eAAKC,aAAL,CAAmB,KAAnB;AACA,eAAKC,WAAL,CAAiB,KAAjB;AACA,eAAKC,eAAL,GAAuB,KAAvB;AACA,eAAKC,MAAL,GAAc,KAAd;AACA,eAAKC,KAAL,GAAa,KAAKC,KAAlB;AACA,eAAKC,UAAL;AAEA,eAAK1C,cAAL,GAAsB,CAAtB;;AACA,eAAKJ,MAAL,CAAa+C,KAAb;;AAEA,cAAMC,OAAO,GAAG,CAACxE,IAAI,CAACyE,cAAL,GAAsBC,MAAvB,GAAgC,GAAhD;AACA,eAAKC,IAAL,CAAUC,WAAV,CAAsB,CAAtB,EAAyBJ,OAAzB;AACH;;AAEDV,QAAAA,UAAU,GAAG;AACT;AACA,cAAIe,IAAI,GAAG;AAAA;AAAA,oDAAiBC,iBAAjB,GAAqC,iBAAhD;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,SAAb,CAAuBH,IAAvB,EAA6BhF,MAA7B,EAAqCoF,IAArC,CAA2CC,MAAD,IAAY;AAAA;;AAClD,gBAAIP,IAAI,GAAGhF,WAAW,CAACuF,MAAD,CAAtB;AACA,sCAAKC,WAAL,+BAAkBxB,QAAlB,CAA2BgB,IAA3B;AACAA,YAAAA,IAAI,CAACC,WAAL,CAAiB,CAAjB,EAAoB,CAApB;AAEA,iBAAKxD,WAAL,GAAmBuD,IAAI,CAACxC,YAAL;AAAA;AAAA,mCAAnB;AACA,iBAAKf,WAAL,CAAkBgE,SAAlB,CAA4B,IAA5B;AACA,iBAAKhE,WAAL,CAAkBiE,WAAlB,CAA8B,KAAK5D,WAAnC;AACA,iBAAKL,WAAL,CAAkBkE,SAAlB,GAA8B,OAA9B;AACH,WATD;AAUH;AAED;AACJ;AACA;;;AACIC,QAAAA,OAAO,GAAS;AACZ,eAAKC,MAAL,CAAarD,YAAb,CAA0BpC,SAA1B,EAAsC0F,OAAtC,GAAgD,CAAhD;AAEA,eAAKd,IAAL,CAAUxC,YAAV,CAAuBpC,SAAvB,EAAmC0F,OAAnC,GAA6C,CAA7C;AACA,eAAKC,YAAL,CAAkB,MAAM;AAAA;;AACpB,iBAAKf,IAAL,CAAUxC,YAAV,CAAuBpC,SAAvB,EAAmC0F,OAAnC,GAA6C,GAA7C;AACA,iCAAKjE,MAAL,0BAAamE,OAAb,CAAqB,MAAM;AACvB,mBAAKH,MAAL,CAAarD,YAAb,CAA0BpC,SAA1B,EAAsC0F,OAAtC,GAAgD,GAAhD;AACA;AAAA;AAAA,sCAAQ/C,aAAR,CAAsBkD,SAAtB;AACH,aAHD;AAIH,WAND,EAMG,GANH;AAOH;AAGD;AACJ;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACC,SAAD,EAAuB;AAC5B,cAAIC,MAAM,GAAG,CAAb;;AACA,cAAID,SAAS,CAACE,MAAV;AAAA;AAAA,+BAAJ,EAAwC;AACpC,gBAAI,KAAKhD,SAAL,CAAeK,sBAAf,CAAsC;AAAA;AAAA,kDAAe4C,sBAArD,KAAgF,CAApF,EAAuF;AACnFF,cAAAA,MAAM,GAAGD,SAAS,CAACE,MAAV,CAAiBE,UAAjB,CAA4B,IAA5B,CAAT;AACH;;AACD,gBAAIH,MAAM,GAAG,CAAb,EAAgB;AACZ,mBAAKI,IAAL,CAAUJ,MAAV;AACH;AACJ,WAPD,MAOO,IAAID,SAAS,CAACE,MAAV;AAAA;AAAA,+CAAJ,EAAgD;AACnD,iBAAKI,cAAL,CAAoBN,SAAS,CAACE,MAA9B;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIK,QAAAA,SAAS,CAACC,IAAD,EAAeC,IAAf,EAA6B;AAClC,cAAI,CAAC,KAAKpC,MAAN,IAAgB,KAAKhD,YAAzB,EAAuC;AAAA;;AACnC,gBAAIqF,MAAM,GAAGF,IAAI,GAAG,KAAK3B,IAAL,CAAU8B,QAAV,CAAmBC,CAAvC;AACA,kCAAKlF,MAAL,2BAAamF,aAAb,CAA2BH,MAA3B,EAFmC,CAInC;;AACA,gBAAII,KAAK,GAAG;AAAA;AAAA,wCAAUC,eAAV,GAA4B,CAAxC;AACAP,YAAAA,IAAI,GAAGQ,IAAI,CAACC,GAAL,CAASH,KAAT,EAAgBN,IAAhB,CAAP;AACAA,YAAAA,IAAI,GAAGQ,IAAI,CAACE,GAAL,CAAS,CAACJ,KAAV,EAAiBN,IAAjB,CAAP;AACAC,YAAAA,IAAI,GAAGO,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYR,IAAZ,CAAP;AACAA,YAAAA,IAAI,GAAGO,IAAI,CAACE,GAAL,CAAS,CAAC;AAAA;AAAA,wCAAUC,UAApB,EAAgCV,IAAhC,CAAP;AACA,iBAAK5B,IAAL,CAAUC,WAAV,CAAsB0B,IAAtB,EAA4BC,IAA5B;AACH;AACJ;;AAEDW,QAAAA,MAAM,CAACC,UAAD,EAAqB;AAAA,cAApBA,UAAoB;AAApBA,YAAAA,UAAoB,GAAP,KAAO;AAAA;;AACvB,eAAKnD,aAAL,CAAmB,IAAnB;AACA,eAAKC,WAAL,CAAiB,IAAjB;;AACA,cAAIkD,UAAJ,EAAgB;AACZ,iBAAKC,kBAAL,CAAwB,CAAxB;AACH,WAFD,MAEO;AACH,iBAAKxE,eAAL;AACH;AACJ;;AAEDyE,QAAAA,MAAM,GAAG;AACL,eAAK1C,IAAL,CAAU2C,MAAV,GAAmB,IAAnB;AACA,eAAKJ,MAAL,CAAY,IAAZ;AACH,SA9KoC,CAgLrC;;;AACAK,QAAAA,YAAY,GAAG;AACX,cAAI,KAAKlG,YAAL,GAAoB,KAAKC,gBAA7B,EAA+C;AAC3C,iBAAKD,YAAL,GAAoB,CAApB,CAD2C,CAE3C;;AACA;AAAA;AAAA,4CAAYmG,EAAZ,CAAeC,aAAf;AACH;AACJ;;AAEDC,QAAAA,KAAK,GAAY;AACb,cAAI,CAAC,MAAMA,KAAN,EAAL,EAAoB;AAChB,mBAAO,KAAP;AACH;;AACD,eAAK/C,IAAL,CAAU2C,MAAV,GAAmB,KAAnB;AACA,eAAKvD,UAAL;AACA;AAAA;AAAA,kCAAQrB,aAAR,CAAsBiF,SAAtB;AACA,iBAAO,IAAP;AACH;;AAEiB,YAAdC,cAAc,GAAG;AAAA;;AACjB,iBAAO,iBAAKrG,UAAL,CAAiBsG,MAAjB,6BAAyBC,YAAzB,KAAyC,CAAhD;AACH;;AACgB,YAAbC,aAAa,GAAG;AAAA;;AAChB,iBAAO,kBAAKxG,UAAL,CAAiBsG,MAAjB,8BAAyBG,aAAzB,KAA0C,CAAjD;AACH;;AAEY,YAAThF,SAAS,GAAkB;AAC3B,iBAAO,KAAKzB,UAAZ;AACH;;AAED0G,QAAAA,SAAS,GAAW;AAChB,iBAAO,KAAK1G,UAAL,CAAiB0G,SAAjB,EAAP;AACH;;AAEDhE,QAAAA,WAAW,CAACiE,MAAD,EAAkB;AACzB,eAAK/G,YAAL,GAAoB+G,MAApB;AACH;;AAEDlE,QAAAA,aAAa,CAACkE,MAAD,EAAkB;AAC3B,eAAKzG,WAAL,GAAmByG,MAAnB;;AACA,cAAI,KAAK9G,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAkBiE,WAAlB,CAA8B6C,MAA9B;AACH;AACJ,SA3NoC,CA4NrC;;;AACAd,QAAAA,kBAAkB,CAACe,IAAD,EAAe;AAC7B,eAAKzG,eAAL,GAAuByG,IAAvB;AACA,eAAKjE,eAAL,GAAuB,KAAvB;AACA,eAAKkE,cAAL,CAAqBd,MAArB,GAA8B,IAA9B;AACH,SAjOoC,CAkOrC;;;AACA1E,QAAAA,eAAe,GAAG;AACd,eAAKsB,eAAL,GAAuB,IAAvB;AACA,eAAKkE,cAAL,CAAqBd,MAArB,GAA8B,KAA9B;AACH;;AAEDe,QAAAA,YAAY,CAACC,KAAD,EAAgB;AACxB,cAAI,KAAK9G,MAAT,EAAiB;AACb,iBAAKA,MAAL,CAAY6G,YAAZ,CAAyBC,KAAzB;AACH;AACJ;;AACiB,YAAdC,cAAc,GAAU;AACxB,iBAAO,KAAK5G,QAAL,CAAc6G,WAArB;AACH;;AACe,YAAZC,YAAY,GAAU;AACtB,iBAAO,KAAK9G,QAAL,CAAc+G,SAArB;AACH;;AACiB,YAAdC,cAAc,GAAU;AACxB,iBAAO,KAAKhH,QAAL,CAAciH,WAArB;AACH;;AACe,YAAZC,YAAY,GAAU;AACtB,iBAAO,KAAKlH,QAAL,CAAcmH,SAArB;AACH;;AAxPoC,O;;;;;iBAGV,I;;;;;;;iBAEA,I;;;;;;;iBAEG,I", "sourcesContent": ["import { _decorator, instantiate, Node, Prefab, size, UIOpacity, view } from \"cc\";\r\nconst { ccclass, property } = _decorator;\r\n\r\nimport { MyApp } from \"db://assets/bundles/common/script/app/MyApp\";\r\nimport { AttributeConst } from \"db://assets/bundles/common/script/const/AttributeConst\";\r\nimport { AttributeData } from \"db://assets/bundles/common/script/data/base/AttributeData\";\r\nimport { PlaneData } from \"db://assets/bundles/common/script/data/plane/PlaneData\";\r\nimport { Plane } from \"db://assets/bundles/common/script/ui/Plane\";\r\nimport { Bullet } from \"../../../bullet/Bullet\";\r\nimport { Emitter } from \"../../../bullet/Emitter\";\r\nimport FBoxCollider from \"../../../collider-system/FBoxCollider\";\r\nimport FCollider, { ColliderGroupType } from \"../../../collider-system/FCollider\";\r\nimport { GameConst } from \"../../../const/GameConst\";\r\nimport GameResourceList from \"../../../const/GameResourceList\";\r\nimport { eEntityTag } from \"../../base/Entity\";\r\nimport EffectLayer from \"../../layer/EffectLayer\";\r\nimport EnemyPlaneBase from \"../enemy/EnemyPlaneBase\";\r\nimport MainPlaneStat from \"./MainPlaneStat\";\r\nimport { GameIns } from \"../../../GameIns\";\r\nimport PlaneBase from \"db://assets/bundles/common/script/game/ui/plane/PlaneBase\";\r\n\r\n@ccclass(\"MainPlane\")\r\nexport class MainPlane extends PlaneBase {\r\n\r\n    @property(Node)\r\n    planeParent: Node | null = null;\r\n    @property(Node)\r\n    NodeEmitter: Node | null = null;\r\n    @property(Node)\r\n    InvincibleNode: Node | null = null;\r\n\r\n    m_moveEnable = true; // 是否允许移动\r\n    emitterComp: Emitter | null = null; // 发射器\r\n\r\n    _hurtActTime = 0; // 受伤动画时间\r\n    _hurtActDuration = 0.5; // 受伤动画持续时间\r\n\r\n    _planeData: PlaneData | null = null;//飞机数据\r\n    _plane: Plane | null = null;//飞机显示节点\r\n    _fireEnable = true;//是否允许射击\r\n    _unColliderTime: number = 0;//无敌时间\r\n    statData: MainPlaneStat = new MainPlaneStat();\r\n\r\n    private hpRecoveryTime = 0;\r\n    private _nuclearNum = 0;\r\n    get nuclearNum() {\r\n        return this._nuclearNum;\r\n    }\r\n    addNuclear(num: number) {\r\n        this._nuclearNum += num;\r\n    }\r\n\r\n    onLoad() {\r\n        this.collideComp = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider);\r\n        this.collideComp!.init(this, size(180, 150)); // 初始化碰撞组件\r\n        this.collideComp!.groupType = ColliderGroupType.PLAYER;\r\n    }\r\n\r\n    // 纯表现层业务，请勿将逻辑代码写到这里\r\n    update(dt: number) {\r\n        dt = dt * GameIns.battleManager.animSpeed;\r\n        this._hurtActTime += dt;\r\n\r\n        if (this._unColliderTime > 0) {\r\n            this._unColliderTime -= dt;\r\n            if (this._unColliderTime <= 0) {\r\n                this.cancelUncollide();\r\n            }\r\n        }\r\n    }\r\n\r\n    updateGameLogic(dt: number): void {\r\n        super.updateGameLogic(dt)\r\n        while (this.hpRecoveryTime <= GameIns.battleManager._gameTime) {\r\n            this.hpRecoveryTime += 1;\r\n            let hpRecovery = this.attribute.getHPRecovery()\r\n            this.addHp(hpRecovery);\r\n        }\r\n    }\r\n\r\n    initPlane(planeData: PlaneData) {\r\n        this._planeData = planeData;\r\n\r\n        this._nuclearNum = planeData.getFinalAttributeByKey(AttributeConst.NuclearMax);\r\n\r\n        //加载飞机显示\r\n        let plane = MyApp.planeMgr.getPlane(planeData);\r\n        this._plane = plane.getComponent(Plane);\r\n        this.planeParent?.addChild(plane);\r\n\r\n        this.addTag(eEntityTag.Player);\r\n\r\n        //设置飞机发射组件\r\n        this.setEmitter();\r\n        this.resetPlane();\r\n    }\r\n\r\n    resetPlane() {\r\n        // 禁用射击\r\n        this.setFireEnable(false);\r\n        this.setMoveAble(false);\r\n        this.colliderEnabled = false;\r\n        this.isDead = false;\r\n        this.curHp = this.maxHp;\r\n        this.updateHpUI();\r\n\r\n        this.hpRecoveryTime = 0;\r\n        this._plane!.reset();\r\n\r\n        const targetY = -view.getVisibleSize().height * 0.7;\r\n        this.node.setPosition(0, targetY)\r\n    }\r\n\r\n    setEmitter() {\r\n        //后期根据飞机的数据，加载不同的发送组件预制体\r\n        let path = GameResourceList.EmitterPrefabPath + \"Emitter_main_01\";\r\n        MyApp.resMgr.loadAsync(path, Prefab).then((prefab) => {\r\n            let node = instantiate(prefab);\r\n            this.NodeEmitter?.addChild(node);\r\n            node.setPosition(0, 0);\r\n\r\n            this.emitterComp = node.getComponent(Emitter);\r\n            this.emitterComp!.setEntity(this);\r\n            this.emitterComp!.setIsActive(this._fireEnable);\r\n            this.emitterComp!.emitterId = 1000001;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 主飞机入场动画\r\n     */\r\n    planeIn(): void {\r\n        this.hpNode!.getComponent(UIOpacity)!.opacity = 0;\r\n\r\n        this.node.getComponent(UIOpacity)!.opacity = 0;\r\n        this.scheduleOnce(() => {\r\n            this.node.getComponent(UIOpacity)!.opacity = 255;\r\n            this._plane?.onEnter(() => {\r\n                this.hpNode!.getComponent(UIOpacity)!.opacity = 255;\r\n                GameIns.battleManager.onPlaneIn();\r\n            });\r\n        }, 0.7);\r\n    }\r\n\r\n\r\n    /**\r\n     * 碰撞处理\r\n     * @param {Object} collision 碰撞对象\r\n     */\r\n    onCollide(collision: FCollider) {\r\n        let damage = 0;\r\n        if (collision.entity instanceof Bullet) {\r\n            if (this.attribute.getFinalAttributeByKey(AttributeConst.StatusImmuneBulletHurt) == 0) {\r\n                damage = collision.entity.calcDamage(this);\r\n            }\r\n            if (damage > 0) {\r\n                this.hurt(damage)\r\n            }\r\n        } else if (collision.entity instanceof EnemyPlaneBase) {\r\n            this.collisionPlane(collision.entity);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 控制飞机移动\r\n     * @param {number} moveX 水平方向的移动量\r\n     * @param {number} moveY 垂直方向的移动量\r\n     */\r\n    onControl(posX: number, posY: number) {\r\n        if (!this.isDead && this.m_moveEnable) {\r\n            let isLeft = posX < this.node.position.x;\r\n            this._plane?.onMoveCommand(isLeft);\r\n\r\n            // 限制飞机移动范围\r\n            let width = GameConst.ViewBattleWidth / 2;\r\n            posX = Math.min(width, posX);\r\n            posX = Math.max(-width, posX);\r\n            posY = Math.min(0, posY);\r\n            posY = Math.max(-GameConst.ViewHeight, posY);\r\n            this.node.setPosition(posX, posY);\r\n        }\r\n    }\r\n\r\n    begine(isContinue = false) {\r\n        this.setFireEnable(true);\r\n        this.setMoveAble(true);\r\n        if (isContinue) {\r\n            this.setUncollideByTime(2);\r\n        } else {\r\n            this.cancelUncollide();\r\n        }\r\n    }\r\n\r\n    revive() {\r\n        this.node.active = true;\r\n        this.begine(true);\r\n    }\r\n\r\n    //实现父类的方法\r\n    playHurtAnim() {\r\n        if (this._hurtActTime > this._hurtActDuration) {\r\n            this._hurtActTime = 0;\r\n            // 显示红屏效果\r\n            EffectLayer.me.showRedScreen();\r\n        }\r\n    }\r\n\r\n    toDie(): boolean {\r\n        if (!super.toDie()) {\r\n            return false;\r\n        }\r\n        this.node.active = false;\r\n        this.resetPlane();\r\n        GameIns.battleManager.battleDie();\r\n        return true;\r\n    }\r\n\r\n    get collisionLevel() {\r\n        return this._planeData!.config?.collideLevel || 0;\r\n    }\r\n    get collisionHurt() {\r\n        return this._planeData!.config?.collideDamage || 0;\r\n    }\r\n\r\n    get attribute(): AttributeData {\r\n        return this._planeData!;\r\n    }\r\n\r\n    getAttack(): number {\r\n        return this._planeData!.getAttack();\r\n    }\r\n\r\n    setMoveAble(enable: boolean) {\r\n        this.m_moveEnable = enable;\r\n    }\r\n\r\n    setFireEnable(enable: boolean) {\r\n        this._fireEnable = enable;\r\n        if (this.emitterComp) {\r\n            this.emitterComp!.setIsActive(enable);\r\n        }\r\n    }\r\n    //设置无敌状态\r\n    setUncollideByTime(time: number) {\r\n        this._unColliderTime = time;\r\n        this.colliderEnabled = false;\r\n        this.InvincibleNode!.active = true;\r\n    }\r\n    //取消无敌状态\r\n    cancelUncollide() {\r\n        this.colliderEnabled = true;\r\n        this.InvincibleNode!.active = false;\r\n    }\r\n\r\n    setAnimSpeed(speed: number) {\r\n        if (this._plane) {\r\n            this._plane.setAnimSpeed(speed);\r\n        }\r\n    }\r\n    get pickDiamondNum():number {\r\n        return this.statData.pickDiamond;\r\n    }\r\n    get killEnemyNum():number {\r\n        return this.statData.killEnemy;\r\n    }\r\n    get usedNuclearNum():number {\r\n        return this.statData.usedNuclear;\r\n    }\r\n    get usedSuperNum():number {\r\n        return this.statData.usedSuper;\r\n    }\r\n}"]}