System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, eEmitterStatus, Emitter, Entity, _decorator, instantiate, Node, Prefab, Enum, CCInteger, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _crd, ccclass, property, eWeaponUseCond, eWeaponState, Weapon;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfeEmitterStatus(extras) {
    _reporterNs.report("eEmitterStatus", "db://assets/bundles/common/script/game/bullet/Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "db://assets/bundles/common/script/game/bullet/Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "db://assets/bundles/common/script/game/ui/base/Entity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "../PlaneBase", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      instantiate = _cc.instantiate;
      Node = _cc.Node;
      Prefab = _cc.Prefab;
      Enum = _cc.Enum;
      CCInteger = _cc.CCInteger;
    }, function (_unresolved_2) {
      eEmitterStatus = _unresolved_2.eEmitterStatus;
      Emitter = _unresolved_2.Emitter;
    }, function (_unresolved_3) {
      Entity = _unresolved_3.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "1fdc1ZnRu5MFLj4D97LAXCR", "Weapon", undefined);

      __checkObsolete__(['_decorator', 'instantiate', 'Component', 'Node', 'Prefab', 'Enum', 'CCInteger']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("eWeaponUseCond", eWeaponUseCond = /*#__PURE__*/function (eWeaponUseCond) {
        eWeaponUseCond[eWeaponUseCond["Immediate"] = 0] = "Immediate";
        eWeaponUseCond[eWeaponUseCond["DelayTime"] = 1] = "DelayTime";
        eWeaponUseCond[eWeaponUseCond["WeaponDestroyed"] = 2] = "WeaponDestroyed";
        return eWeaponUseCond;
      }({}));

      _export("eWeaponState", eWeaponState = {
        Aiming: 0,
        Emitting: 1,
        None: 2
      });
      /**
       * 武器后续可能也需要继承自PlaneBase, 因为武器可能也需要血量，扣血等逻辑。
       */


      _export("Weapon", Weapon = (_dec = ccclass('Weapon'), _dec2 = property({
        type: CCInteger,
        displayName: "转向速度"
      }), _dec3 = property({
        type: CCInteger,
        displayName: "锁定目标时间(ms)"
      }), _dec4 = property({
        type: Enum(eWeaponUseCond),
        displayName: "启用条件"
      }), _dec5 = property({
        type: CCInteger,
        displayName: "延迟启用时间(ms)",

        visible() {
          // @ts-ignore
          return this.useCondition === eWeaponUseCond.DelayTime;
        }

      }), _dec6 = property({
        type: [Node],
        displayName: "目标武器",

        visible() {
          // @ts-ignore
          return this.useCondition === eWeaponUseCond.WeaponDestroyed;
        }

      }), _dec7 = property({
        type: Prefab,
        displayName: "发射器"
      }), _dec(_class = (_class2 = class Weapon extends (_crd && Entity === void 0 ? (_reportPossibleCrUseOfEntity({
        error: Error()
      }), Entity) : Entity) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "turnSpeed", _descriptor, this);

          // 转向速度（仅用在追踪目标时）
          _initializerDefineProperty(this, "aimingTime", _descriptor2, this);

          // 锁定目标时间，单位ms
          _initializerDefineProperty(this, "useCondition", _descriptor3, this);

          _initializerDefineProperty(this, "delayTime", _descriptor4, this);

          // 延迟时间，单位ms
          // 注意这里的Node不能替换成Weapon，因为cocos在ccclass里不能引用自己。
          _initializerDefineProperty(this, "targetWeaponNodes", _descriptor5, this);

          _initializerDefineProperty(this, "emitterPrefab", _descriptor6, this);

          // 武器发射器
          this.m_state = eWeaponState.None;
          this.m_stateElapsedTime = 0;
          // 当前状态持续时间
          this.m_target = null;
          // 目标实体
          this.m_owner = null;
          // 拥有者实体
          this.m_targetWeapons = [];
          this.m_emitter = null;
        }

        init() {
          this.m_state = eWeaponState.None; // if (this.emitter) {
          //     this.emitter.changeStatus(eEmitterStatus.None);
          // }

          if (this.useCondition === eWeaponUseCond.WeaponDestroyed) {
            this.m_targetWeapons = this.node.getComponentsInChildren(Weapon);
          }
        }

        initEmitter() {
          if (!this.m_emitter) {
            if (this.emitterPrefab) {
              var _this$m_emitter, _this$m_emitter2;

              var node = instantiate(this.emitterPrefab);
              this.node.addChild(node);
              this.m_emitter = node.getComponent(_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
                error: Error()
              }), Emitter) : Emitter);
              (_this$m_emitter = this.m_emitter) == null || _this$m_emitter.setEntity(this.m_owner);
              (_this$m_emitter2 = this.m_emitter) == null || _this$m_emitter2.changeStatus((_crd && eEmitterStatus === void 0 ? (_reportPossibleCrUseOfeEmitterStatus({
                error: Error()
              }), eEmitterStatus) : eEmitterStatus).None);
            }
          } else {
            var _this$m_emitter3;

            // 非首次启用了，后面看是直接切到预热还是要算启用延迟
            (_this$m_emitter3 = this.m_emitter) == null || _this$m_emitter3.changeStatus((_crd && eEmitterStatus === void 0 ? (_reportPossibleCrUseOfeEmitterStatus({
              error: Error()
            }), eEmitterStatus) : eEmitterStatus).Prewarm);
          }
        }

        get state() {
          return this.m_state;
        }

        set state(value) {
          if (this.m_state === value) {
            return;
          }

          this.m_state = value;
          this.m_stateElapsedTime = 0;
        }

        setOwner(owner) {
          this.m_owner = owner;
          return this;
        }

        setTarget(target) {
          this.m_target = target;

          if (this.useCondition === eWeaponUseCond.Immediate) {
            this.activate();
          }

          return this;
        }

        tick(dt) {
          this.m_stateElapsedTime += dt;

          switch (this.m_state) {
            case eWeaponState.Aiming:
              this.tickAiming(dt);
              break;

            case eWeaponState.Emitting:
              //this.tickEmitting(dt);
              break;

            case eWeaponState.None:
              this.tickNone(dt);
              break;
          }
        } // 激活武器


        activate() {
          this.state = this.aimingTime > 0 ? eWeaponState.Aiming : eWeaponState.Emitting;
          this.initEmitter();
        }

        tickAiming(dt) {
          if (this.m_stateElapsedTime >= this.aimingTime) {
            this.state = eWeaponState.Emitting;
          }

          this.turnToTarget(dt);
        }

        tickEmitting(dt) {//if (this.m_emitter && this.m_emitter.status !== eEmitterStatus.Emitting) {
          //    this.m_emitter.changeStatus(eEmitterStatus.Emitting);
          //}
        }

        tickNone(dt) {
          if (this.useCondition === eWeaponUseCond.WeaponDestroyed) {
            for (var weapon of this.m_targetWeapons) {
              if (!weapon.isDead) {
                return;
              }
            }

            this.activate();
          } else if (this.useCondition === eWeaponUseCond.DelayTime) {
            if (this.m_stateElapsedTime >= this.delayTime) {
              this.activate();
            }
          }
        }

        turnToTarget(dt) {
          if (!this.m_target || !this.m_owner) {
            return;
          }

          var ownerNode = this.m_owner.node;
          var targetNode = this.m_target.node;
          var angle = ownerNode.angle;
          var targetAngle = Math.atan2(targetNode.worldPosition.y - ownerNode.worldPosition.y, targetNode.worldPosition.x - ownerNode.worldPosition.x) * 180 / Math.PI - 90;
          var deltaAngle = targetAngle - angle;

          if (deltaAngle > 180) {
            deltaAngle -= 360;
          } else if (deltaAngle < -180) {
            deltaAngle += 360;
          }

          var maxDelta = this.turnSpeed * dt / 1000;

          if (Math.abs(deltaAngle) <= maxDelta) {
            ownerNode.angle = targetAngle;
          } else {
            ownerNode.angle += Math.sign(deltaAngle) * maxDelta;
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "turnSpeed", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 60;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "aimingTime", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 2000;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "useCondition", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return eWeaponUseCond.Immediate;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "delayTime", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "targetWeaponNodes", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "emitterPrefab", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7582fa84034226d6868ba8b0d46678ff92a1dd03.js.map