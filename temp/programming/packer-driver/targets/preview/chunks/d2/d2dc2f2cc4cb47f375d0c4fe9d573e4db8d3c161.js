System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, assetManager, misc, EDITOR, MyApp, BulletData, EmitterData, Bullet, BulletProperty, BulletSystem, EventGroupContext, ObjectPool, PropertyContainerComponent, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _descriptor, _descriptor2, _descriptor3, _class3, _crd, ccclass, executeInEditMode, property, disallowMultiple, menu, degreesToRadians, radiansToDegrees, eEmitterStatus, eEmitterProp, ePropMask, Emitter;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResEmitter(extras) {
    _reporterNs.report("ResEmitter", "db://assets/bundles/common/script/autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletData(extras) {
    _reporterNs.report("BulletData", "../data/bullet/BulletData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterData(extras) {
    _reporterNs.report("EmitterData", "../data/bullet/EmitterData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "./Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletProperty(extras) {
    _reporterNs.report("BulletProperty", "./Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "./BulletSystem", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroup(extras) {
    _reporterNs.report("EventGroup", "./EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupContext(extras) {
    _reporterNs.report("EventGroupContext", "./EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfObjectPool(extras) {
    _reporterNs.report("ObjectPool", "./ObjectPool", _context.meta, extras);
  }

  function _reportPossibleCrUseOfProperty(extras) {
    _reporterNs.report("Property", "./PropertyContainer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPropertyContainerComponent(extras) {
    _reporterNs.report("PropertyContainerComponent", "./PropertyContainer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "../ui/plane/PlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfExpressionValue(extras) {
    _reporterNs.report("ExpressionValue", "../data/bullet/ExpressionValue", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      assetManager = _cc.assetManager;
      misc = _cc.misc;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      BulletData = _unresolved_3.BulletData;
    }, function (_unresolved_4) {
      EmitterData = _unresolved_4.EmitterData;
    }, function (_unresolved_5) {
      Bullet = _unresolved_5.Bullet;
      BulletProperty = _unresolved_5.BulletProperty;
    }, function (_unresolved_6) {
      BulletSystem = _unresolved_6.BulletSystem;
    }, function (_unresolved_7) {
      EventGroupContext = _unresolved_7.EventGroupContext;
    }, function (_unresolved_8) {
      ObjectPool = _unresolved_8.ObjectPool;
    }, function (_unresolved_9) {
      PropertyContainerComponent = _unresolved_9.PropertyContainerComponent;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2564dArcRFKZKoo3odCQrHw", "Emitter", undefined);

      __checkObsolete__(['_decorator', 'assetManager', 'misc', 'Prefab', 'Quat', 'Vec3', 'Enum', 'CCString']);

      // // 这个import仅用于编辑功能
      // import { BulletEnum } from 'db://assets/editor/enum-gen/BulletEnum'
      ({
        ccclass,
        executeInEditMode,
        property,
        disallowMultiple,
        menu
      } = _decorator);
      ({
        degreesToRadians,
        radiansToDegrees
      } = misc);
      /**
       * 发射器状态变换
       * [None] -> <InitialDelay> -> [Prewarm] -> <PrewarmDuration> -> [Emitting]
       *                                 ^                                 |
       *                                 |                                 v
       *                                 |                           <EmitDuration>
       *                                 |                                 |
       *                                 |                              isLoop? ---no---> [Completed] 
       *                                 |                                 |
       *                                 |                                 |yes
       *                                 |                                 |
       *                                 |                                 v
       *                                 -------<LoopInterval>------[LoopEndReached]
       */

      _export("eEmitterStatus", eEmitterStatus = /*#__PURE__*/function (eEmitterStatus) {
        eEmitterStatus[eEmitterStatus["None"] = 0] = "None";
        eEmitterStatus[eEmitterStatus["Prewarm"] = 1] = "Prewarm";
        eEmitterStatus[eEmitterStatus["Emitting"] = 2] = "Emitting";
        eEmitterStatus[eEmitterStatus["LoopEndReached"] = 3] = "LoopEndReached";
        eEmitterStatus[eEmitterStatus["Completed"] = 4] = "Completed";
        return eEmitterStatus;
      }({})); // 用枚举定义属性


      _export("eEmitterProp", eEmitterProp = /*#__PURE__*/function (eEmitterProp) {
        eEmitterProp[eEmitterProp["IsActive"] = 1] = "IsActive";
        eEmitterProp[eEmitterProp["IsOnlyInScreen"] = 2] = "IsOnlyInScreen";
        eEmitterProp[eEmitterProp["IsPreWarm"] = 3] = "IsPreWarm";
        eEmitterProp[eEmitterProp["IsLoop"] = 4] = "IsLoop";
        eEmitterProp[eEmitterProp["InitialDelay"] = 5] = "InitialDelay";
        eEmitterProp[eEmitterProp["PrewarmDuration"] = 6] = "PrewarmDuration";
        eEmitterProp[eEmitterProp["EmitDuration"] = 7] = "EmitDuration";
        eEmitterProp[eEmitterProp["EmitInterval"] = 8] = "EmitInterval";
        eEmitterProp[eEmitterProp["EmitPower"] = 9] = "EmitPower";
        eEmitterProp[eEmitterProp["LoopInterval"] = 10] = "LoopInterval";
        eEmitterProp[eEmitterProp["PerEmitCount"] = 11] = "PerEmitCount";
        eEmitterProp[eEmitterProp["PerEmitInterval"] = 12] = "PerEmitInterval";
        eEmitterProp[eEmitterProp["PerEmitOffsetX"] = 13] = "PerEmitOffsetX";
        eEmitterProp[eEmitterProp["Angle"] = 14] = "Angle";
        eEmitterProp[eEmitterProp["Count"] = 15] = "Count";
        eEmitterProp[eEmitterProp["Arc"] = 16] = "Arc";
        eEmitterProp[eEmitterProp["Radius"] = 17] = "Radius";
        eEmitterProp[eEmitterProp["ElapsedTime"] = 18] = "ElapsedTime";
        return eEmitterProp;
      }({}));
      /**
       * 说明：
       * 因为发射器属性可能需要从emitterData里的公式计算，如: randi(0,360);
       * 但同时也可能被事件组修改，参考: EmitterEventActions
       * 事件组的优先级要高于emitterData的公式计算, 因此, 如果一个属性带了ePropMask.EventGroup标记, 后续ReEval时就直接跳过
       */


      _export("ePropMask", ePropMask = /*#__PURE__*/function (ePropMask) {
        ePropMask[ePropMask["ReEval"] = 1] = "ReEval";
        ePropMask[ePropMask["EventGroup"] = 2] = "EventGroup";
        return ePropMask;
      }({}));

      /**
       * 目前Emitter,EventGroup,BulletSystem的状态管理还是比较混乱
       * 需要看下怎么调整，使代码不论是运行时，还是编辑器下，都更加健壮
       * - young
       */
      _export("Emitter", Emitter = (_dec = ccclass('Emitter'), _dec2 = menu('子弹系统/发射器'), _dec3 = executeInEditMode(true), _dec4 = disallowMultiple(true), _dec5 = property({
        displayName: '名称',
        editorOnly: true
      }), _dec6 = property({
        type: _crd && EmitterData === void 0 ? (_reportPossibleCrUseOfEmitterData({
          error: Error()
        }), EmitterData) : EmitterData,
        displayName: "发射器属性"
      }), _dec7 = property({
        type: _crd && BulletData === void 0 ? (_reportPossibleCrUseOfBulletData({
          error: Error()
        }), BulletData) : BulletData,
        displayName: "子弹属性"
      }), _dec(_class = _dec2(_class = _dec3(_class = _dec4(_class = (_class2 = (_class3 = class Emitter extends (_crd && PropertyContainerComponent === void 0 ? (_reportPossibleCrUseOfPropertyContainerComponent({
        error: Error()
      }), PropertyContainerComponent) : PropertyContainerComponent) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "emitterName", _descriptor, this);

          // 备注(策划用)
          // @property({ type: Enum(BulletEnum), displayName: "子弹ID" })
          // readonly bulletID: number = 0;
          _initializerDefineProperty(this, "emitterData", _descriptor2, this);

          _initializerDefineProperty(this, "bulletData", _descriptor3, this);

          // callbacks
          this.onBulletCreatedCallback = null;
          this.onEmitterStatusChangedCallback = null;
          // 以下属性缓存为了性能优化(减少this.getProperty<T>的调用)
          this.isActive = void 0;
          this.isOnlyInScreen = void 0;
          this.isPreWarm = void 0;
          this.isLoop = void 0;
          this.initialDelay = void 0;
          this.preWarmDuration = void 0;
          this.emitDuration = void 0;
          this.emitInterval = void 0;
          this.emitPower = void 0;
          this.loopInterval = void 0;
          this.perEmitCount = void 0;
          this.perEmitInterval = void 0;
          this.perEmitOffsetX = void 0;
          this.angle = void 0;
          this.count = void 0;
          this.arc = void 0;
          this.radius = void 0;
          this.elapsedTime = void 0;
          // 以下用于事件组修改子弹的属性，（不直接修改bulletData)
          this.bulletProp = void 0;
          // 发射器自己的事件组
          this.eventGroups = [];
          // 私有变量
          this._emitterId = 0;
          this._status = eEmitterStatus.None;
          this._statusElapsedTime = 0;
          this._totalElapsedTime = 0;
          this._isEmitting = false;
          this._nextEmitTime = 0;
          this._bulletPrefab = null;
          this._prewarmEffectPrefab = null;
          this._emitEffectPrefab = null;
          this._entity = null;
          this._emitterConfig = undefined;
          // Per-emit timing tracking
          this._perEmitBulletQueue = [];
        }

        get isEmitting() {
          return this._isEmitting;
        }

        get status() {
          return this._status;
        }

        get statusElapsedTime() {
          return this._statusElapsedTime;
        }

        get totalElapsedTime() {
          return this._totalElapsedTime;
        }

        get emitterId() {
          return this._emitterId;
        }

        get config() {
          return this._emitterConfig;
        }

        set emitterId(id) {
          this._emitterId = id;
          this.loadConfigByID(id);
        }

        onLoad() {
          this.createProperties();
          this.createEventGroups(); // 更新属性

          this.resetProperties();
        } //#region "Editor Region"


        onLostFocusInEditor() {
          this.updatePropertiesInEditor();
          this.createEventGroups();
        }

        updatePropertiesInEditor() {
          if (!this.emitterData) return;
          this.isActive.value = true;
          this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;
          this.isPreWarm.value = this.emitterData.isPreWarm;
          this.isLoop.value = this.emitterData.isLoop;
          this.initialDelay.value = this.emitterData.initialDelay.eval(null, true);
          this.preWarmDuration.value = this.emitterData.preWarmDuration.eval(null, true);
          this.emitDuration.value = this.emitterData.emitDuration.eval(null, true);
          this.emitInterval.value = this.emitterData.emitInterval.eval(null, true);
          this.emitPower.value = this.emitterData.emitPower.eval(null, true);
          this.loopInterval.value = this.emitterData.loopInterval.eval(null, true);
          this.perEmitCount.value = this.emitterData.perEmitCount.eval(null, true);
          this.perEmitInterval.value = this.emitterData.perEmitInterval.eval(null, true);
          this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval(null, true);
          this.angle.value = this.emitterData.angle.eval(null, true);
          this.count.value = this.emitterData.count.eval(null, true);
          this.arc.value = this.emitterData.arc.eval(null, true);
          this.radius.value = this.emitterData.radius.eval(null, true);
          this.notifyAll(true);
        } //#endregion "Editor Region"
        // 通过这个接口来启用和禁用发射器


        setIsActive(active) {
          this.isActive.value = active;
          this.isActive.notify();
        } // 这个接口清理发射器的状态，全部从头开始


        reset() {
          this._isEmitting = false;
          this.changeStatus(eEmitterStatus.None);
          this.resetProperties();

          if (this.eventGroups.length > 0) {
            this.eventGroups.forEach(group => group.reset());
          }
        }

        setEntity(entity) {
          this._entity = entity;
        }

        getEntity() {
          return this._entity;
        }

        createProperties() {
          this.clear();
          this.isActive = this.addProperty(eEmitterProp.IsActive, false);
          this.elapsedTime = this.addProperty(eEmitterProp.ElapsedTime, 0);
          this.isOnlyInScreen = this.addProperty(eEmitterProp.IsOnlyInScreen, true);
          this.isPreWarm = this.addProperty(eEmitterProp.IsPreWarm, true);
          this.isLoop = this.addProperty(eEmitterProp.IsLoop, true);
          this.initialDelay = this.addProperty(eEmitterProp.InitialDelay, 0);
          this.preWarmDuration = this.addProperty(eEmitterProp.PrewarmDuration, 0);
          this.emitDuration = this.addProperty(eEmitterProp.EmitDuration, 0);
          this.emitInterval = this.addProperty(eEmitterProp.EmitInterval, 0);
          this.emitPower = this.addProperty(eEmitterProp.EmitPower, 1);
          this.loopInterval = this.addProperty(eEmitterProp.LoopInterval, 0);
          this.perEmitCount = this.addProperty(eEmitterProp.PerEmitCount, 1);
          this.perEmitInterval = this.addProperty(eEmitterProp.PerEmitInterval, 0);
          this.perEmitOffsetX = this.addProperty(eEmitterProp.PerEmitOffsetX, 0);
          this.angle = this.addProperty(eEmitterProp.Angle, 0);
          this.count = this.addProperty(eEmitterProp.Count, 1);
          this.arc = this.addProperty(eEmitterProp.Arc, 0);
          this.radius = this.addProperty(eEmitterProp.Radius, 0); // 子弹相关属性

          this.bulletProp = new (_crd && BulletProperty === void 0 ? (_reportPossibleCrUseOfBulletProperty({
            error: Error()
          }), BulletProperty) : BulletProperty)();
          this.isActive.on(value => {
            if (value) {
              this.changeStatus(eEmitterStatus.Prewarm);
              (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
                error: Error()
              }), BulletSystem) : BulletSystem).onCreateEmitter(this);
            } else {
              (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
                error: Error()
              }), BulletSystem) : BulletSystem).onDestroyEmitter(this);
            }
          });
        }

        createEventGroups() {
          if (!this.emitterData || this.emitterData.eventGroupData.length <= 0) return;
          this.eventGroups = [];
          var ctx = new (_crd && EventGroupContext === void 0 ? (_reportPossibleCrUseOfEventGroupContext({
            error: Error()
          }), EventGroupContext) : EventGroupContext)();
          ctx.emitter = this;
          ctx.playerPlane = (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).playerPlane;

          for (var eventGroup of this.emitterData.eventGroupData) {
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).createEmitterEventGroup(ctx, eventGroup);
          }
        } // reset properties from emitterData


        resetProperties() {
          if (!this.emitterData) return;
          this.isActive.value = false;
          this.elapsedTime.value = 0;
          this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;
          this.isPreWarm.value = this.emitterData.isPreWarm;
          this.isLoop.value = this.emitterData.isLoop;
          this.initialDelay.value = this.emitterData.initialDelay.eval(null, true);
          this.preWarmDuration.value = this.emitterData.preWarmDuration.eval(null, true);
          this.emitDuration.value = this.emitterData.emitDuration.eval(null, true);
          this.emitInterval.value = this.emitterData.emitInterval.eval(null, true);
          this.emitPower.value = this.emitterData.emitPower.eval(null, true);
          this.loopInterval.value = this.emitterData.loopInterval.eval(null, true);
          this.perEmitCount.value = this.emitterData.perEmitCount.eval(null, true);
          this.perEmitInterval.value = this.emitterData.perEmitInterval.eval(null, true);
          this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval(null, true);
          this.angle.value = this.emitterData.angle.eval(null, true);
          this.count.value = this.emitterData.count.eval(null, true);
          this.arc.value = this.emitterData.arc.eval(null, true);
          this.radius.value = this.emitterData.radius.eval(null, true);
          this.bulletProp.resetFromData(this.bulletData);
          this.notifyAll(true);
        }

        evalProperty(prop, value) {
          // 为什么这样写，而不是直接：prop.setValue(value.eval(), ePropMask.ReEval);
          // 是为了避免非必要的eval()调用
          if (prop.canWrite(ePropMask.ReEval) && !value.isFixedValue) {
            prop.value = value.eval();
          }
        }
        /**
         * public apis
         */


        changeStatus(status) {
          if (this._status === status) return;
          var oldStatus = this._status;
          this._status = status;
          this._statusElapsedTime = 0;
          this._nextEmitTime = 0; // Clear per-emit queue when changing status

          this._perEmitBulletQueue = [];

          if (status === eEmitterStatus.Prewarm) {
            this.elapsedTime.value = 0; // emitInterval可能是一个序列，每次尽量从第一个序列从头开始

            this.emitterData.emitInterval.reset();
          }

          if (status === eEmitterStatus.None) {
            if (this.eventGroups.length > 0) {
              this.eventGroups.forEach(group => group.tryStop());
            }
          } else {
            // 所有其他状态，都尝试开始执行eventGroup
            if (this.eventGroups.length > 0) {
              this.eventGroups.forEach(group => group.tryStart());
            }
          }

          if (this.onEmitterStatusChangedCallback != null) {
            this.onEmitterStatusChangedCallback(this, oldStatus, status);
          }
        }

        scheduleNextEmit() {
          // re-eval
          this.evalProperty(this.emitInterval, this.emitterData.emitInterval); // Schedule the next emit after emitInterval

          this._nextEmitTime = this._statusElapsedTime + this.emitInterval.value; // console.log('scheduleNextEmit: ', this._nextEmitTime, ', ', this._statusElapsedTime, ', ', this.emitInterval.value);
        }

        startEmitting() {
          this._isEmitting = true; // 下一次update时触发发射
          // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射
          // this.tryEmit();
          // if (this.perEmitInterval.value <= 0) {
          //     this.scheduleNextEmit();
          // }
          // else {
          //     // 开始这一波
          //     this._nextEmitTime = this._statusElapsedTime + 10000000;
          // }
          // // reset status time 
          // this._statusElapsedTime = 0;
        }

        stopEmitting() {
          this._isEmitting = false; // Clear the per-emit bullet queue

          this._perEmitBulletQueue = [];
          this.unscheduleAllCallbacks();
        }

        canEmit() {
          // 检查是否可以触发发射
          // Override this method in subclasses to add custom trigger conditions
          return true;
        }

        emit() {
          // re-eval
          this.evalProperty(this.count, this.emitterData.count);
          this.evalProperty(this.arc, this.emitterData.arc);
          this.evalProperty(this.radius, this.emitterData.radius);
          this.evalProperty(this.perEmitCount, this.emitterData.perEmitCount);

          if (this.perEmitInterval.value > 0) {
            // Generate bullets in time-sorted order directly
            for (var j = 0; j < this.perEmitCount.value; j++) {
              this.evalProperty(this.perEmitInterval, this.emitterData.perEmitInterval);
              var targetTime = this._statusElapsedTime + this.perEmitInterval.value * j;

              for (var i = 0; i < this.count.value; i++) {
                this._perEmitBulletQueue.push({
                  index: i,
                  perEmitIndex: j,
                  targetTime: targetTime
                });
              }
            }
          } else {
            // Immediate emission - no timing needed
            for (var _i = 0; _i < this.count.value; _i++) {
              for (var _j = 0; _j < this.perEmitCount.value; _j++) {
                this.emitSingle(_i, _j);
              }
            }
          }
        }

        processPerEmitQueue() {
          // Process bullets that should be emitted based on current time
          while (this._perEmitBulletQueue.length > 0) {
            var nextBullet = this._perEmitBulletQueue[0]; // Check if it's time to emit this bullet

            if (this._statusElapsedTime >= nextBullet.targetTime) {
              // Remove from queue and emit
              this._perEmitBulletQueue.shift();

              this.emitSingle(nextBullet.index, nextBullet.perEmitIndex);
            } else {
              // No more bullets ready to emit yet
              break;
            }
          }
        }

        tryEmit() {
          if (this.canEmit()) {
            this.emit();
            return true;
          }

          return false;
        }

        emitSingle(index, perEmitIndex) {
          var direction = this.getSpawnDirection(index);
          var position = this.getSpawnPosition(index, perEmitIndex);
          this.createBullet(direction, position);
        }
        /**
         * Calculate the direction for a bullet at the given index
         * @param index The index of the bullet (0 to count-1)
         * @returns Direction vector {x, y}
         */


        getSpawnDirection(index) {
          // 期望如果配了公式，每次发射方向都随机下
          this.evalProperty(this.angle, this.emitterData.angle); // 计算发射方向

          var angleOffset = this.count.value > 1 ? this.arc.value / (this.count.value - 1) * index - this.arc.value / 2 : 0;
          var radian = degreesToRadians(this.angle.value + angleOffset);
          return {
            x: Math.cos(radian),
            y: Math.sin(radian)
          };
        }
        /**
         * Get the spawn position for a bullet at the given index
         * odd number to the right, even number to the left
         * @param index The index of the bullet (0 to count-1)
         * @returns Position offset from emitter center
         */


        getSpawnPosition(index, perEmitIndex) {
          // add perEmitOffsetX by perEmitIndex, with the rules:
          // by the following order:0, 1; 2, 0, 1; 2, 0, 1, 3;
          var getEmitOffsetX = (perEmitIndex, perEmitCount, perEmitOffsetX) => {
            if (perEmitCount <= 1 || perEmitOffsetX === 0) return 0;
            var interval = perEmitOffsetX / (perEmitCount - 1); //const middle = 0;

            if (perEmitCount % 2 === 1) {
              // 奇数情况
              if (perEmitIndex === 0) return 0;

              if (perEmitIndex % 2 === 0) {
                // 偶数索引在左边
                var stepsFromMiddle = Math.floor(perEmitIndex / 2);
                return -stepsFromMiddle * interval;
              } else {
                // 奇数索引在右边
                var _stepsFromMiddle = Math.ceil(perEmitIndex / 2);

                return _stepsFromMiddle * interval;
              }
            } else {
              // 偶数情况
              if (perEmitIndex === 0) return -interval / 2;

              if (perEmitIndex % 2 === 0) {
                // 偶数索引在左边
                var _stepsFromMiddle2 = Math.floor(perEmitIndex / 2);

                return -interval / 2 - _stepsFromMiddle2 * interval;
              } else {
                // 奇数索引在右边
                var _stepsFromMiddle3 = Math.floor(perEmitIndex / 2);

                return interval / 2 + _stepsFromMiddle3 * interval;
              }
            }
          };

          this.evalProperty(this.perEmitOffsetX, this.emitterData.perEmitOffsetX);
          var perEmitOffsetX = getEmitOffsetX(perEmitIndex, this.perEmitCount.value, this.perEmitOffsetX.value);

          if (this.radius.value <= 0) {
            return {
              x: perEmitOffsetX,
              y: 0
            };
          }

          var direction = this.getSpawnDirection(index); // 计算垂直于发射方向的向量（逆时针90度旋转）

          var perpendicular = {
            x: -direction.y,
            y: direction.x
          };

          if (this.radius.value <= 0) {
            return {
              x: perpendicular.x * perEmitOffsetX,
              y: perpendicular.y * perEmitOffsetX
            };
          }

          return {
            x: direction.x * this.radius.value + perpendicular.x * perEmitOffsetX,
            y: direction.y * this.radius.value + perpendicular.y * perEmitOffsetX
          };
        }

        createBullet(direction, position) {
          if (!this._bulletPrefab) {
            this._bulletPrefab = this.bulletData.prefab;

            if (!this._bulletPrefab) {
              if (EDITOR) {
                this.createBulletInEditor(direction, position);
              }

              return;
            }
          }

          var bullet = this.instantiateBullet();
          if (!bullet) return;
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).onCreateBullet(this, bullet); // Set bullet position relative to emitter

          var emitterPos = this.node.getWorldPosition();
          bullet.node.setWorldPosition(emitterPos.x + position.x, emitterPos.y + position.y, emitterPos.z);
          bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));
          bullet.prop.speed.value *= this.emitPower.value; // 为什么需要在这里resetEventGroups?
          // 因为EventGroups的条件初始化依赖上面先初始化子弹的属性

          bullet.onReady();

          if (this.onBulletCreatedCallback != null) {
            this.onBulletCreatedCallback(bullet);
          }
        }

        createBulletInEditor(direction, position) {
          var _this = this;

          return _asyncToGenerator(function* () {
            // use a default bullet prefab
            var prefabPath = 'db://assets/resources/game/prefabs/Bullet_New.prefab'; // @ts-ignore

            Editor.Message.request('asset-db', 'query-uuid', prefabPath).then(uuid => {
              assetManager.loadAny({
                uuid: uuid
              }, (err, prefab) => {
                if (err) {
                  console.error(err);
                  return;
                }

                _this._bulletPrefab = prefab;

                var bullet = _this.instantiateBullet();

                if (!bullet) return;
                (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
                  error: Error()
                }), BulletSystem) : BulletSystem).onCreateBullet(_this, bullet); // Set bullet position relative to emitter

                var emitterPos = _this.node.getWorldPosition();

                bullet.node.setWorldPosition(emitterPos.x + position.x, emitterPos.y + position.y, emitterPos.z);
                bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));
                bullet.prop.speed.value *= _this.emitPower.value;
                bullet.onReady();
              });
            });
          })();
        }

        instantiateBullet() {
          var bulletNode = (_crd && ObjectPool === void 0 ? (_reportPossibleCrUseOfObjectPool({
            error: Error()
          }), ObjectPool) : ObjectPool).getNode((_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).bulletParent, this._bulletPrefab);

          if (!bulletNode) {
            console.error("Emitter: Failed to instantiate bullet prefab");
            return null;
          } // Get the bullet component


          var bullet = bulletNode.getComponent(_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);

          if (!bullet) {
            console.error("Emitter: Bullet prefab does not have Bullet component");
            bulletNode.destroy();
            return null;
          }

          if (EDITOR) {
            bulletNode.name = Emitter.kBulletNameInEditor;
          }

          return bullet;
        }

        loadConfigByID(emitterId) {
          if (emitterId > 0 && (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).GetInstance() && (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanMgr) {
            this._emitterConfig = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbResEmitter.get(emitterId); // if (this._bulletConfig) {
            //     MyApp.resMgr.load(this._bulletConfig.prefab, Prefab, (error: any, prefab: Prefab) => {
            //         if (error) {
            //             console.error("Emitter load bullet prefab err", error);
            //             return;
            //         }
            //         this._bulletPrefab = prefab;
            //     });
            // }
          } // else if (EDITOR) {
          //     let lubanMgr = new LubanMgr();
          //     lubanMgr.initInEditor().then(() => {
          //         this._bulletConfig = lubanMgr.table.TbResBullet.get(bulletID);
          //         if (this._bulletConfig) {
          //             const prefabPath = 'db://assets/resources/' + this._bulletConfig.prefab + '.prefab';
          //             // @ts-ignore
          //             Editor.Message.request('asset-db', 'query-uuid', prefabPath)
          //                 .then((uuid: string) => {
          //                     assetManager.loadAny({ uuid: uuid }, (err, prefab) => {
          //                         if (err) {
          //                             console.error(err);
          //                             return;
          //                         }
          //                         this._bulletPrefab = prefab;
          //                     });
          //                 });
          //         }
          //      });
          // }

        }

        playEffect(prefab, position, rotation, duration) {
          if (!prefab) return;
          var effectNode = (_crd && ObjectPool === void 0 ? (_reportPossibleCrUseOfObjectPool({
            error: Error()
          }), ObjectPool) : ObjectPool).getNode(this.node, prefab);
          if (!effectNode) return;
          effectNode.setWorldPosition(position);
          effectNode.setWorldRotation(rotation); // Play the effect and destroy it after duration
          // effectNode.getComponent(ParticleSystem)?.play();

          this.scheduleOnce(() => {
            (_crd && ObjectPool === void 0 ? (_reportPossibleCrUseOfObjectPool({
              error: Error()
            }), ObjectPool) : ObjectPool).returnNode(effectNode);
          }, duration);
        }
        /**
         * Return true if this.node is in screen
         */


        isInScreen() {
          // TODO: Get mainCamera.containsNode(this.node)
          return true;
        }

        tick(deltaTime) {
          if (!this.isActive || !this.isActive.value) {
            return;
          }

          switch (this._status) {
            case eEmitterStatus.None:
              this.updateStatusNone();
              break;

            case eEmitterStatus.Prewarm:
              this.updateStatusPrewarm();
              break;

            case eEmitterStatus.Emitting:
              this.updateStatusEmitting();
              break;

            case eEmitterStatus.LoopEndReached:
              this.updateStatusLoopEndReached();
              break;

            case eEmitterStatus.Completed:
              this.updateStatusCompleted();
              break;

            default:
              break;
          }

          this.elapsedTime.value += deltaTime;
          this._statusElapsedTime += deltaTime;
          this._totalElapsedTime += deltaTime;
          this.notifyAll();
        }

        updateStatusNone() {
          if (this._statusElapsedTime >= this.initialDelay.value) {
            this.changeStatus(eEmitterStatus.Prewarm);
          }
        }

        updateStatusPrewarm() {
          if (!this.isPreWarm.value) this.changeStatus(eEmitterStatus.Emitting);else {
            if (this._statusElapsedTime >= this.preWarmDuration.value) {
              this.changeStatus(eEmitterStatus.Emitting);
            }
          }
        }

        updateStatusEmitting() {
          if (this._statusElapsedTime > this.emitDuration.value) {
            this.stopEmitting();
            if (this.isLoop.value) this.changeStatus(eEmitterStatus.LoopEndReached);else this.changeStatus(eEmitterStatus.Completed);
            return;
          } // Start emitting if not already started


          if (!this._isEmitting) {
            this.startEmitting();
          } else if (this._statusElapsedTime >= this._nextEmitTime) {
            this.tryEmit();

            if (this.perEmitInterval.value <= 0) {
              this.scheduleNextEmit();
            } else {
              // 开始这一波
              this._nextEmitTime = this._statusElapsedTime + 10000000;
            }
          }

          var wasEmitting = this._perEmitBulletQueue.length > 0; // Process per-emit bullet queue based on precise timing

          this.processPerEmitQueue();

          if (wasEmitting && this._perEmitBulletQueue.length <= 0) {
            this.scheduleNextEmit();
          }
        }

        updateStatusLoopEndReached() {
          if (this._statusElapsedTime >= this.loopInterval.value) {
            this.changeStatus(eEmitterStatus.Prewarm);
          }
        }

        updateStatusCompleted() {
          // Do nothing or cleanup if needed
          this.isActive.value = false;
          this.isActive.notify();
        }

      }, _class3.kBulletNameInEditor = "_bullet_", _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "emitterName", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return '';
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "emitterData", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && EmitterData === void 0 ? (_reportPossibleCrUseOfEmitterData({
            error: Error()
          }), EmitterData) : EmitterData)();
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "bulletData", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && BulletData === void 0 ? (_reportPossibleCrUseOfBulletData({
            error: Error()
          }), BulletData) : BulletData)();
        }
      })), _class2)) || _class) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=d2dc2f2cc4cb47f375d0c4fe9d573e4db8d3c161.js.map