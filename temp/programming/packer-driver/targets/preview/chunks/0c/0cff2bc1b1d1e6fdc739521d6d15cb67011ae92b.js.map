{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/editor/emitter/Test.ts"], "names": ["_decorator", "Component", "ccclass", "property", "Test", "start", "update", "deltaTime"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;;;;;;;;OACf;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;sBAGjBI,I,WADZF,OAAO,CAAC,MAAD,C,gBAAR,MACaE,IADb,SAC0BH,SAD1B,CACoC;AAChCI,QAAAA,KAAK,GAAG,CAEP;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AAP+B,O", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\nconst { ccclass, property } = _decorator;\n\n@ccclass('Test')\nexport class Test extends Component {\n    start() {\n\n    }\n\n    update(deltaTime: number) {\n        \n    }\n}\n\n"]}