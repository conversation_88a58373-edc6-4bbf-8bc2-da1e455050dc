System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, MyApp, csproto, logError, ResTaskClass, DataEvent, EventMgr, Task, _crd;

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/bundles/common/script/autogen/pb/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIData(extras) {
    _reporterNs.report("IData", "db://assets/bundles/common/script/data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogError(extras) {
    _reporterNs.report("logError", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResGoalType(extras) {
    _reporterNs.report("ResGoalType", "../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResTask(extras) {
    _reporterNs.report("ResTask", "../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResTaskClass(extras) {
    _reporterNs.report("ResTaskClass", "../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataEvent(extras) {
    _reporterNs.report("DataEvent", "../../event/DataEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../event/EventManager", _context.meta, extras);
  }

  _export("Task", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      csproto = _unresolved_3.default;
    }, function (_unresolved_4) {
      logError = _unresolved_4.logError;
    }, function (_unresolved_5) {
      ResTaskClass = _unresolved_5.ResTaskClass;
    }, function (_unresolved_6) {
      DataEvent = _unresolved_6.DataEvent;
    }, function (_unresolved_7) {
      EventMgr = _unresolved_7.EventMgr;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "937a5sNu2NPOKTaYU3HSRiv", "Task", undefined);

      _export("Task", Task = class Task {
        constructor() {
          // 任务合集
          this.taskMap = new Map();
        }

        init() {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_TASK_GET_INFO, this.onGetTaskInfoMsg, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_TASK_GET_REWARD, this.onGetTaskRewardMsg, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_TASK_GET_LIST, this.onTaskListMsg, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_TASK_UPDATE_DATA, this.onTaskUpdateDataMsg, this);
          this.refreshAllTasks();
        }

        refreshAllTasks() {
          this.refreshTaskByClass((_crd && ResTaskClass === void 0 ? (_reportPossibleCrUseOfResTaskClass({
            error: Error()
          }), ResTaskClass) : ResTaskClass).DAILY_TASK);
          this.refreshTaskByClass((_crd && ResTaskClass === void 0 ? (_reportPossibleCrUseOfResTaskClass({
            error: Error()
          }), ResTaskClass) : ResTaskClass).WEEKLY_TASK);
          this.refreshTaskByClass((_crd && ResTaskClass === void 0 ? (_reportPossibleCrUseOfResTaskClass({
            error: Error()
          }), ResTaskClass) : ResTaskClass).TASK_ORBIT);
        }

        refreshTaskByClass(taskClass) {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_TASK_GET_LIST, {
            task_get_list: {
              task_class: taskClass
            }
          });
        }

        getTaskListByClass(taskClass) {
          return this.taskMap.get(taskClass) || [];
        }

        getTaskByTaskId(taskId, taskClass) {
          if (taskClass) {
            var taskList = this.taskMap.get(taskClass) || [];
            return taskList.find(t => t.task_id === taskId);
          }

          for (var _taskList of this.taskMap.values()) {
            var task = _taskList.find(t => t.task_id === taskId);

            if (task) {
              return task;
            }
          }

          return undefined;
        }
        /**
         * 获取任务描述和进度最大值
         * @param taskCfg 任务配置
         * @returns 任务描述和进度最大值
         */


        getTaskDescAndProgress(taskCfg) {
          var taskDesc = taskCfg.taskGoal.desc || "";
          var progressMax = 0;

          if (taskCfg.taskGoal.params.length == 1) {
            progressMax = taskCfg.taskGoal.params[0];
            taskDesc = taskDesc.replace("N", taskCfg.taskGoal.params[0].toString());
          } else if (taskCfg.taskGoal.params.length == 2) {
            //暂时不读表了，表配置还没出来,先直接替换param[0]
            taskDesc = taskDesc.replace("XX", taskCfg.taskGoal.params[0].toString());
            taskDesc = taskDesc.replace("N", taskCfg.taskGoal.params[1].toString());
            progressMax = taskCfg.taskGoal.params[1]; // switch (taskCfg.taskGoal.goalType) {
            //     case ResGoalType.MODE_PASS_TIMES:
            //         const gameMode = MyApp.lubanTables.TbResGameMode.get(taskCfg.taskGoal.params[0]);
            //         replaceName = gameMode?.description || "";
            //         break;
            //     default:
            //         break;
            // }
          }

          return {
            desc: taskDesc,
            progressMax
          };
        }
        /**
         * 上报任务目标进度
         * @param goalType 任务目标类型
         * @param params 任务目标参数 任务目标参数根据goalType不同而不同 比如关卡目标参数为关卡id
         * @returns 
         */


        commitTaskProgress(goalType) {
          var goldTypeTasks = [];
          this.taskMap.forEach((taskList, taskClass) => {
            var tasks = taskList.filter(t => {
              if (t.status !== (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                error: Error()
              }), csproto) : csproto).comm.TASK_STATUS.TASK_STATUS_NORMAL) return false;
              var taskCfg = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).lubanTables.TbResTask.get(t.task_id);
              return taskCfg && taskCfg.taskGoal.goalType === goalType;
            }) || [];

            if (tasks.length > 0) {
              goldTypeTasks.push(...tasks);
            }
          });
          if (goldTypeTasks.length === 0) return;

          for (var _len = arguments.length, params = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
            params[_key - 1] = arguments[_key];
          }

          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_TASK_GOAL_UPDATE, {
            task_goal_update: {
              goal_type: goalType,
              values: params
            }
          });
        }

        onTaskListMsg(msg) {
          var _msg$body, _msg$body2;

          var taskList = ((_msg$body = msg.body) == null || (_msg$body = _msg$body.task_get_list) == null ? void 0 : _msg$body.task_list) || [];
          var taskClass = (_msg$body2 = msg.body) == null || (_msg$body2 = _msg$body2.task_get_list) == null ? void 0 : _msg$body2.task_class;

          if (taskClass) {
            this.taskMap.set(taskClass, taskList);
          }

          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).TaskRefresh, taskClass);
        }

        onTaskUpdateDataMsg(msg) {
          var _msg$body3, _msg$body4;

          var taskInfo = (_msg$body3 = msg.body) == null || (_msg$body3 = _msg$body3.task_update_data) == null ? void 0 : _msg$body3.task_info;
          var taskClass = (_msg$body4 = msg.body) == null || (_msg$body4 = _msg$body4.task_update_data) == null ? void 0 : _msg$body4.task_class;

          if (!taskInfo || !taskInfo.task_id || !taskClass) {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("Task", "task update data task_id is undefined");
            return;
          }

          var taskList = this.taskMap.get(taskClass) || [];
          var taskIndex = taskList.findIndex(t => t.task_id === (taskInfo == null ? void 0 : taskInfo.task_id));

          if (taskIndex !== -1) {
            taskList[taskIndex] = taskInfo;
          } else {
            taskList.push(taskInfo);
          }

          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).TaskRefresh, taskClass);
        } // 任务奖励


        onGetTaskRewardMsg(msg) {
          var _msg$body5;

          var rewardList = ((_msg$body5 = msg.body) == null || (_msg$body5 = _msg$body5.task_get_reward) == null ? void 0 : _msg$body5.reward_list) || []; // if (taskId) {
          //     const taskCfg = MyApp.lubanTables.TbResTask.get();
          //     if (taskCfg) {
          //         this.taskMap.set(taskCfg.taskClass, t);
          //     }
          // }
        } // 全任务信息


        onGetTaskInfoMsg(msg) {
          var _msg$body6;

          var taskList = ((_msg$body6 = msg.body) == null || (_msg$body6 = _msg$body6.task_get_info) == null ? void 0 : _msg$body6.task_list) || [];
          taskList.forEach(t => {
            var taskCfg = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbResTask.get(t.task_id);

            if (!taskCfg) {
              (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                error: Error()
              }), logError) : logError)("Task", "task id " + t.task_id + " not found");
              return;
            }

            var taskList = this.taskMap.get(taskCfg.taskClass) || [];
            taskList.push(t);
            this.taskMap.set(taskCfg.taskClass, taskList);
          });
        }

        update() {}

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=949ecb74eb0e7b956a3dca7498beb7934344bffe.js.map