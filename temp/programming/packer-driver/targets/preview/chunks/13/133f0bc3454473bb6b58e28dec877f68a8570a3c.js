System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, eWaveConditionType, eWaveActionType, EventGroupBase, ConditionChain, _decorator, wave_cond, wave_act, WaveEventGroupContext, WaveEventGroup, WaveConditionFactory, WaveActionFactory, _crd, ccclass, property, type;

  function _reportPossibleCrUseOfWaveConditionData(extras) {
    _reporterNs.report("WaveConditionData", "../data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWaveEventGroupData(extras) {
    _reporterNs.report("WaveEventGroupData", "../data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWaveActionData(extras) {
    _reporterNs.report("WaveActionData", "../data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeWaveConditionType(extras) {
    _reporterNs.report("eWaveConditionType", "../data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeWaveActionType(extras) {
    _reporterNs.report("eWaveActionType", "../data/WaveData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventGroupContext(extras) {
    _reporterNs.report("IEventGroupContext", "db://assets/bundles/common/script/game/eventgroup/IEventGroupContext", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupBase(extras) {
    _reporterNs.report("EventGroupBase", "db://assets/bundles/common/script/game/eventgroup/IEventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventCondition(extras) {
    _reporterNs.report("IEventCondition", "db://assets/bundles/common/script/game/eventgroup/IEventCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfConditionChain(extras) {
    _reporterNs.report("ConditionChain", "db://assets/bundles/common/script/game/eventgroup/IEventCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventAction(extras) {
    _reporterNs.report("IEventAction", "db://assets/bundles/common/script/game/eventgroup/IEventAction", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWave(extras) {
    _reporterNs.report("Wave", "./Wave", _context.meta, extras);
  }

  _export({
    WaveEventGroupContext: void 0,
    WaveEventGroup: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      eWaveConditionType = _unresolved_2.eWaveConditionType;
      eWaveActionType = _unresolved_2.eWaveActionType;
    }, function (_unresolved_3) {
      EventGroupBase = _unresolved_3.EventGroupBase;
    }, function (_unresolved_4) {
      ConditionChain = _unresolved_4.ConditionChain;
    }, function (_unresolved_5) {
      wave_cond = _unresolved_5;
    }, function (_unresolved_6) {
      wave_act = _unresolved_6;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "ffe45opZFdLEahljREt4B8X", "WaveEventGroup", undefined);

      __checkObsolete__(['_decorator', 'CCInteger']);

      ({
        ccclass,
        property,
        type
      } = _decorator);

      _export("WaveEventGroupContext", WaveEventGroupContext = class WaveEventGroupContext {
        constructor() {
          // 继承来的，在波次这里不使用
          this.emitter = null;
          // 继承来的，在波次这里不使用
          this.bullet = null;
          this.playerPlane = null;
          this.wave = null;
        }

        reset() {
          this.emitter = null;
          this.bullet = null;
          this.playerPlane = null;
          this.wave = null;
        }

      }); /// Wave事件组
      /// 和子弹&发射器事件组主要差异在于数据源不同: WaveEventGroupData vs EventGroupData


      _export("WaveEventGroup", WaveEventGroup = class WaveEventGroup extends (_crd && EventGroupBase === void 0 ? (_reportPossibleCrUseOfEventGroupBase({
        error: Error()
      }), EventGroupBase) : EventGroupBase) {
        buildConditions() {
          var chain = new (_crd && ConditionChain === void 0 ? (_reportPossibleCrUseOfConditionChain({
            error: Error()
          }), ConditionChain) : ConditionChain)();
          this.data.conditions.forEach((condData, index) => {
            var condition = WaveConditionFactory.create(condData);

            if (condition) {
              condition.onLoad(this.context);
              chain.conditions.push(condition);
            }
          });
          return chain;
        }

        buildActions() {
          return this.data.actions.map(actionData => {
            var action = WaveActionFactory.create(actionData);
            return action;
          });
        }

      });

      WaveConditionFactory = class WaveConditionFactory {
        static create(data) {
          switch (data.type) {
            case (_crd && eWaveConditionType === void 0 ? (_reportPossibleCrUseOfeWaveConditionType({
              error: Error()
            }), eWaveConditionType) : eWaveConditionType).Spawn_Count:
              return new wave_cond.WaveCondition_SpawnCount(data);

            case (_crd && eWaveConditionType === void 0 ? (_reportPossibleCrUseOfeWaveConditionType({
              error: Error()
            }), eWaveConditionType) : eWaveConditionType).Player_Level:
              return new wave_cond.WaveCondition_PlayerLevel(data);

            default:
              throw new Error("Unknown condition type: " + data.type);
          }
        }

      };
      WaveActionFactory = class WaveActionFactory {
        static create(data) {
          switch (data.type) {
            case (_crd && eWaveActionType === void 0 ? (_reportPossibleCrUseOfeWaveActionType({
              error: Error()
            }), eWaveActionType) : eWaveActionType).Spawn_Interval:
              return new wave_act.WaveAction_SpawnInterval(data);

            case (_crd && eWaveActionType === void 0 ? (_reportPossibleCrUseOfeWaveActionType({
              error: Error()
            }), eWaveActionType) : eWaveActionType).Spawn_Angle:
              return new wave_act.WaveAction_SpawnAngle(data);

            default:
              throw new Error("Unknown action type: " + data.type);
          }
        }

      };

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=133f0bc3454473bb6b58e28dec877f68a8570a3c.js.map