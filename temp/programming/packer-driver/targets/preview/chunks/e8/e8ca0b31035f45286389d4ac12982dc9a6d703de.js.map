{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/skill/ExCondition.ts"], "names": ["ExCondition", "ExConditionNum", "ResSkillConditionElem", "constructor", "res", "reset", "num", "value"], "mappings": ";;;qDAEaA,W,EAMAC,c;;;;;;;;;;;;;;;;;AARUC,MAAAA,qB,iBAAAA,qB;;;;;;;6BAEVF,W,GAAN,MAAMA,WAAN;AAAA;AAAA,0DAAgD;AACnDG,QAAAA,WAAW,CAACC,GAAD,EAA4B;AACnC,gBAAMA,GAAN;AACH;;AACDC,QAAAA,KAAK,GAAG,CAAE;;AAJyC,O;;gCAM1CJ,c,GAAN,MAAMA,cAAN,SAA6BD,WAA7B,CAAwC;AAE3CG,QAAAA,WAAW,CAACC,GAAD,EAA4BE,GAA5B,EAAwC;AAC/C,gBAAMF,GAAN;AAD+C,eADnDE,GACmD,GADtC,CACsC;AAE/C,eAAKA,GAAL,GAAWA,GAAX;AACH;;AACDD,QAAAA,KAAK,GAAG;AACJ,eAAKC,GAAL,IAAY,KAAKC,KAAjB;AACH;;AAR0C,O", "sourcesContent": ["import { ResCondition, ResSkillConditionElem } from \"../../../../autogen/luban/schema\";\r\n\r\nexport class ExCondition extends ResSkillConditionElem {\r\n    constructor(res:ResSkillConditionElem) {\r\n        super(res)\r\n    }\r\n    reset() {}\r\n}\r\nexport class ExConditionNum extends ExCondition{\r\n    num:number = 0;\r\n    constructor(res:ResSkillConditionElem, num:number) {\r\n        super(res)\r\n        this.num = num\r\n    }\r\n    reset() {\r\n        this.num -= this.value\r\n    }\r\n}\r\n"]}