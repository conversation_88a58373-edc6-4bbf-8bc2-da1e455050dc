{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/bullet/Bullet.ts"], "names": ["BulletProperty", "_decorator", "Sprite", "Color", "Prefab", "EDITOR", "ObjectPool", "Movable", "eSpriteDefaultFacing", "eMoveEvent", "BulletSystem", "EventGroupContext", "PropertyContainer", "FCollider", "ColliderGroupType", "FBoxCollider", "Entity", "eEntityTag", "log<PERSON>arn", "BulletSourceType", "BulletType", "GameIns", "AttributeData", "ccclass", "property", "executeInEditMode", "requireComponent", "constructor", "duration", "delayDestroy", "attack", "speed", "speedAngle", "acceleration", "accelerationAngle", "scale", "color", "defaultFacing", "isDestroyOutScreen", "isDestructive", "isDestructiveOnHit", "isFacingMoveDir", "isTrackingTarget", "config", "undefined", "addProperty", "WHITE", "Up", "resetFromData", "data", "value", "eval", "copyFrom", "other", "forEachProperty", "k", "prop", "getPropertyValue", "clear", "Bullet", "type", "displayName", "isAlive", "elapsedTime", "emitter", "bulletData", "eventGroups", "sourceType", "source", "MAINPLANE", "NORMAL", "onLoad", "mover", "getComponent", "addComponent", "on", "onBecomeInvisible", "onDestroyBullet", "collider", "boxCollider", "node", "setScale", "bulletSprite", "onCreate", "bulletConfig", "ent", "getEntity", "isShootFromEnemy", "ENEMYPLANE", "initBaseData", "groupType", "BULLET_ENEMY", "BULLET_SELF", "isEnable", "addTag", "EnemyBullet", "<PERSON><PERSON><PERSON><PERSON>", "resetProperties", "onReady", "notifyAll", "setMovable", "resetEventGroups", "<PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON>", "destroySelf", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "returnNode", "bulletProp", "eventGroupData", "length", "ctx", "bullet", "eventName", "createBulletEventGroup", "tick", "dt", "anyPropertyDirty", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "group", "tryStop", "removeAllComp", "clearTags", "isVisible", "scheduleOnce", "onCollide", "remove", "getAttack", "calcDamage", "defender", "CalcBulletDamage", "attribute", "attackCoefficient", "damageType", "getSquaredDistanceToPlayer", "mainPlaneManager", "mainPlane", "pos", "position", "<PERSON><PERSON><PERSON>", "x", "y"], "mappings": ";;;oWAuBaA,c;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAvBJC,MAAAA,U,OAAAA,U;AAAmCC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;;AAClDC,MAAAA,M,UAAAA,M;;AAEAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,oB,iBAAAA,oB;AAAsBC,MAAAA,U,iBAAAA,U;;AAC/BC,MAAAA,Y,iBAAAA,Y;;AACYC,MAAAA,iB,iBAAAA,iB;;AACFC,MAAAA,iB,iBAAAA,iB;;AAGZC,MAAAA,S;AAAaC,MAAAA,iB,iBAAAA,iB;;AACbC,MAAAA,Y;;AACAC,MAAAA,M;;AACEC,MAAAA,U,kBAAAA,U;;AACQC,MAAAA,O,kBAAAA,O;;AACIC,MAAAA,gB,kBAAAA,gB;AAAkBC,MAAAA,U,kBAAAA,U;;AAC9BC,MAAAA,O,kBAAAA,O;;AAGAC,MAAAA,a,kBAAAA,a;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA;AAAxC,O,GAA6DzB,U;;gCAEtDD,c,GAAN,MAAMA,cAAN;AAAA;AAAA,kDAAuD;AAqB1D2B,QAAAA,WAAW,GAAG;AACV;AADU,eApBPC,QAoBO;AApBqC;AAoBrC,eAnBPC,YAmBO;AAnBqC;AAmBrC,eAjBPC,MAiBO;AAjBqC;AAiBrC,eAhBPC,KAgBO;AAhBqC;AAgBrC,eAfPC,UAeO;AAfqC;AAerC,eAdPC,YAcO;AAdqC;AAcrC,eAbPC,iBAaO;AAbqC;AAarC,eAZPC,KAYO;AAZqC;AAYrC,eAXPC,KAWO;AAXqC;AAWrC,eAVPC,aAUO;AAVkD;AAUlD,eARPC,kBAQO;AARqC;AAQrC,eAPPC,aAOO;AAPqC;AAOrC,eANPC,kBAMO;AANqC;AAMrC,eALPC,eAKO;AALqC;AAKrC,eAJPC,gBAIO;AAJqC;AAIrC,eAFPC,MAEO,GAFwBC,SAExB;AAEV,eAAKhB,QAAL,GAAgB,KAAKiB,WAAL,CAAiB,CAAjB,EAAoB,IAApB,CAAhB;AACA,eAAKhB,YAAL,GAAoB,KAAKgB,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAApB;AACA,eAAKf,MAAL,GAAc,KAAKe,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAAd;AACA,eAAKd,KAAL,GAAa,KAAKc,WAAL,CAAiB,CAAjB,EAAoB,GAApB,CAAb;AACA,eAAKb,UAAL,GAAkB,KAAKa,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAAlB;AACA,eAAKZ,YAAL,GAAoB,KAAKY,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAApB;AACA,eAAKX,iBAAL,GAAyB,KAAKW,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAAzB;AACA,eAAKV,KAAL,GAAa,KAAKU,WAAL,CAAiB,CAAjB,EAAoB,CAApB,CAAb;AACA,eAAKT,KAAL,GAAa,KAAKS,WAAL,CAAiB,CAAjB,EAAoB1C,KAAK,CAAC2C,KAA1B,CAAb;AACA,eAAKT,aAAL,GAAqB,KAAKQ,WAAL,CAAuC,CAAvC,EAA0C;AAAA;AAAA,4DAAqBE,EAA/D,CAArB;AACA,eAAKT,kBAAL,GAA0B,KAAKO,WAAL,CAAiB,EAAjB,EAAqB,IAArB,CAA1B;AACA,eAAKN,aAAL,GAAqB,KAAKM,WAAL,CAAiB,EAAjB,EAAqB,KAArB,CAArB;AACA,eAAKL,kBAAL,GAA0B,KAAKK,WAAL,CAAiB,EAAjB,EAAqB,KAArB,CAA1B;AACA,eAAKJ,eAAL,GAAuB,KAAKI,WAAL,CAAiB,EAAjB,EAAqB,KAArB,CAAvB;AACA,eAAKH,gBAAL,GAAwB,KAAKG,WAAL,CAAiB,EAAjB,EAAqB,KAArB,CAAxB;AACH;;AAEMG,QAAAA,aAAa,CAACC,IAAD,EAAmB;AACnC,eAAKrB,QAAL,CAAcsB,KAAd,GAAsBD,IAAI,CAACrB,QAAL,CAAcuB,IAAd,CAAmB,IAAnB,EAAyB,IAAzB,CAAtB;AACA,eAAKtB,YAAL,CAAkBqB,KAAlB,GAA0BD,IAAI,CAACpB,YAAL,CAAkBsB,IAAlB,CAAuB,IAAvB,EAA6B,IAA7B,CAA1B;AACA,eAAKpB,KAAL,CAAWmB,KAAX,GAAmBD,IAAI,CAAClB,KAAL,CAAWoB,IAAX,CAAgB,IAAhB,EAAsB,IAAtB,CAAnB,CAHmC,CAInC;;AACA,eAAKlB,YAAL,CAAkBiB,KAAlB,GAA0BD,IAAI,CAAChB,YAAL,CAAkBkB,IAAlB,CAAuB,IAAvB,EAA6B,IAA7B,CAA1B;AACA,eAAKjB,iBAAL,CAAuBgB,KAAvB,GAA+BD,IAAI,CAACf,iBAAL,CAAuBiB,IAAvB,CAA4B,IAA5B,EAAkC,IAAlC,CAA/B;AACA,eAAKhB,KAAL,CAAWe,KAAX,GAAmBD,IAAI,CAACd,KAAL,CAAWgB,IAAX,CAAgB,IAAhB,EAAsB,IAAtB,CAAnB,CAPmC,CAQnC;;AACA,eAAKb,kBAAL,CAAwBY,KAAxB,GAAgCD,IAAI,CAACX,kBAArC;AACA,eAAKC,aAAL,CAAmBW,KAAnB,GAA2BD,IAAI,CAACV,aAAhC;AACA,eAAKC,kBAAL,CAAwBU,KAAxB,GAAgCD,IAAI,CAACT,kBAArC;AACA,eAAKC,eAAL,CAAqBS,KAArB,GAA6BD,IAAI,CAACR,eAAlC;AACA,eAAKC,gBAAL,CAAsBQ,KAAtB,GAA8BD,IAAI,CAACP,gBAAnC;AACH;;AAEMU,QAAAA,QAAQ,CAACC,KAAD,EAAwB;AACnC,eAAKC,eAAL,CAAqB,CAACC,CAAD,EAAIC,IAAJ,KAAa;AAC9BA,YAAAA,IAAI,CAACN,KAAL,GAAaG,KAAK,CAACI,gBAAN,CAAuBF,CAAvB,CAAb;AACH,WAFD;AAGH;;AAEMG,QAAAA,KAAK,GAAG;AACX;AACA,eAAKJ,eAAL,CAAqB,CAACC,CAAD,EAAIC,IAAJ,KAAaA,IAAI,CAACE,KAAL,EAAlC;AACH;;AAjEyD,O,GAoE9D;AACA;;;wBAIaC,M,WAHZpC,OAAO,CAAC,QAAD,C,UACPE,iBAAiB,CAAC,IAAD,C,UACjBC,gBAAgB;AAAA;AAAA,6B,UAEZF,QAAQ,CAAC;AAACoC,QAAAA,IAAI;AAAA;AAAA,8BAAL;AAAgBC,QAAAA,WAAW,EAAE;AAA7B,OAAD,C,UAGRrC,QAAQ,CAAC;AAACoC,QAAAA,IAAI,EAAE1D,MAAP;AAAe2D,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,UAGRrC,QAAQ,CAAC;AAACoC,QAAAA,IAAI;AAAA;AAAA,kCAAL;AAAkBC,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,UAGRrC,QAAQ,CAAC;AAACoC,QAAAA,IAAI,EAAExD,MAAP;AAAeyD,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,4DAbb,MAGaF,MAHb;AAAA;AAAA,4BAGmC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAaxBG,OAbwB,GAaL,KAbK;AAAA,eAcxBC,WAdwB,GAcF,CAdE;AAcS;AAdT,eAexBC,OAfwB;AAAA,eAgBxBC,UAhBwB;AAgBS;AAhBT,eAiBxBtB,MAjBwB,GAiBOC,SAjBP;AAiBoB;AACnD;AAlB+B,eAmBxBY,IAnBwB,GAmBD,IAAIxD,cAAJ,EAnBC;AAAA,eAoBxBkE,WApBwB,GAoBI,EApBJ;AAAA;;AAsB/B;AACqB,YAAVC,UAAU,GAAqB;AAAA;;AACtC,iBAAO,sBAAKxB,MAAL,kCAAayB,MAAb,KAAuB;AAAA;AAAA,oDAAiBC,SAA/C;AACH;;AACc,YAAJT,IAAI,GAAe;AAAA;;AAC1B,iBAAO,uBAAKjB,MAAL,mCAAaiB,IAAb,KAAqB;AAAA;AAAA,wCAAWU,MAAvC;AACH;;AAEDC,QAAAA,MAAM,GAAS;AACX,cAAI,CAAC,KAAKC,KAAV,EAAiB;AACb,iBAAKA,KAAL,GAAa,KAAKC,YAAL;AAAA;AAAA,uCAA8B,KAAKC,YAAL;AAAA;AAAA,mCAA3C;AACH;;AACD,eAAKF,KAAL,CAAWG,EAAX,CAAc;AAAA;AAAA,wCAAWC,iBAAzB,EAA4C,MAAM;AAC9C,gBAAI,KAAKpB,IAAL,CAAUlB,kBAAV,CAA6BY,KAAjC,EAAwC;AACpC;AAAA;AAAA,gDAAa2B,eAAb,CAA6B,IAA7B;AACH;AACJ,WAJD;;AAKA,cAAI,CAAC,KAAKC,QAAV,EAAoB;AAChB,gBAAIC,WAAW,GAAG,KAAKN,YAAL;AAAA;AAAA,iDAAmC,KAAKC,YAAL;AAAA;AAAA,6CAArD;AACA,iBAAKI,QAAL,GAAgBC,WAAhB;AACH;;AAED,eAAKvB,IAAL,CAAUnB,aAAV,CAAwBa,KAAxB,GAAgC,KAAKsB,KAAL,CAAWnC,aAA3C,CAdW,CAeX;;AACA,eAAKmB,IAAL,CAAUf,eAAV,CAA0BkC,EAA1B,CAA8BzB,KAAD,IAAW;AACpC,iBAAKsB,KAAL,CAAW/B,eAAX,GAA6BS,KAA7B;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUd,gBAAV,CAA2BiC,EAA3B,CAA+BzB,KAAD,IAAW;AACrC,iBAAKsB,KAAL,CAAW9B,gBAAX,GAA8BQ,KAA9B;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUzB,KAAV,CAAgB4C,EAAhB,CAAoBzB,KAAD,IAAW;AAC1B,iBAAKsB,KAAL,CAAWzC,KAAX,GAAmBmB,KAAnB;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUxB,UAAV,CAAqB2C,EAArB,CAAyBzB,KAAD,IAAW;AAC/B,iBAAKsB,KAAL,CAAWxC,UAAX,GAAwBkB,KAAxB;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUvB,YAAV,CAAuB0C,EAAvB,CAA2BzB,KAAD,IAAW;AACjC,iBAAKsB,KAAL,CAAWvC,YAAX,GAA0BiB,KAA1B;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUtB,iBAAV,CAA4ByC,EAA5B,CAAgCzB,KAAD,IAAW;AACtC,iBAAKsB,KAAL,CAAWtC,iBAAX,GAA+BgB,KAA/B;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUrB,KAAV,CAAgBwC,EAAhB,CAAoBzB,KAAD,IAAW;AAC1B,iBAAK8B,IAAL,CAAUC,QAAV,CAAmB/B,KAAnB,EAA0BA,KAA1B,EAAiCA,KAAjC;AACH,WAFD;AAGA,eAAKM,IAAL,CAAUpB,KAAV,CAAgBuC,EAAhB,CAAoBzB,KAAD,IAAW;AAC1B,gBAAI,KAAKgC,YAAT,EAAuB;AACnB,mBAAKA,YAAL,CAAkB9C,KAAlB,GAA0Bc,KAA1B;AACH;AACJ,WAJD;AAKH;;AAEMiC,QAAAA,QAAQ,CAACnB,OAAD,EAAmBoB,YAAnB,EAA6D;AACxE,eAAKtB,OAAL,GAAe,IAAf;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKC,OAAL,GAAeA,OAAf;AACA,eAAKC,UAAL,GAAkBD,OAAO,CAACC,UAA1B;AACA,eAAKtB,MAAL,GAAcyC,YAAd,CALwE,CAOxE;;AACA,cAAMC,GAAG,GAAGrB,OAAO,CAACsB,SAAR,EAAZ;;AACA,cAAID,GAAJ,EAAS;AAAA;;AACL,gBAAME,gBAAgB,GAAG,uBAAK5C,MAAL,mCAAayB,MAAb,KAAuB;AAAA;AAAA,sDAAiBoB,UAAjE;AACA,iBAAKV,QAAL,CAAeW,YAAf,CAA4B,IAA5B;AACA,iBAAKX,QAAL,CAAeY,SAAf,GAA2BH,gBAAgB,GAAG;AAAA;AAAA,wDAAkBI,YAArB,GAAoC;AAAA;AAAA,wDAAkBC,WAAjG;AACA,iBAAKd,QAAL,CAAee,QAAf,GAA0B,IAA1B;AACA,iBAAKC,MAAL,CAAYP,gBAAgB,GAAG;AAAA;AAAA,0CAAWQ,WAAd,GAA4B;AAAA;AAAA,0CAAWC,YAAnE;AACH,WAND,MAMO;AACH;AAAA;AAAA,oCAAQ,SAAR,EAAmB,iBAAnB,EAAsC,eAAtC;AACH;;AAED,eAAKC,eAAL;AACH;;AAEMC,QAAAA,OAAO,GAAG;AACb,eAAK1C,IAAL,CAAU2C,SAAV,CAAoB,IAApB;AACA,eAAK3B,KAAL,CAAW4B,UAAX,CAAsB,IAAtB;AACA,eAAKC,gBAAL;AAEA,cAAMhB,GAAG,GAAG,KAAKrB,OAAL,CAAasB,SAAb,EAAZ;;AACA,cAAID,GAAJ,EAAS;AACL,iBAAKb,KAAL,CAAW8B,SAAX,CAAqBjB,GAAG,CAACkB,SAAJ,EAArB;AACH;AACJ;;AAEOC,QAAAA,WAAW,GAAG;AAClB,cAAI,CAAC,KAAKxB,IAAN,IAAc,CAAC,KAAKA,IAAL,CAAUyB,OAA7B,EAAsC;;AAEtC,cAAIpG,MAAJ,EAAY;AACR,iBAAK2E,IAAL,CAAU0B,OAAV;AACH,WAFD,MAEO;AACH;AAAA;AAAA,0CAAWC,UAAX,CAAsB,KAAK3B,IAA3B;AACH;AACJ;;AAEMiB,QAAAA,eAAe,GAAS;AAC3B,cAAI,CAAC,KAAKjC,OAAV,EAAmB;AAEnB,eAAKR,IAAL,CAAUJ,QAAV,CAAmB,KAAKY,OAAL,CAAa4C,UAAhC;AACA,eAAKpD,IAAL,CAAU2C,SAAV,CAAoB,IAApB;AACH;;AAEME,QAAAA,gBAAgB,GAAS;AAC5B;AACA,cAAI,KAAKpC,UAAL,IAAmB,KAAKA,UAAL,CAAgB4C,cAAhB,CAA+BC,MAA/B,GAAwC,CAA/D,EAAkE;AAC9D,gBAAIC,GAAG,GAAG;AAAA;AAAA,yDAAV;AACAA,YAAAA,GAAG,CAACC,MAAJ,GAAa,IAAb;AACAD,YAAAA,GAAG,CAAC/C,OAAJ,GAAc,KAAKA,OAAnB;;AACA,iBAAK,IAAMiD,SAAX,IAAwB,KAAKhD,UAAL,CAAgB4C,cAAxC,EAAwD;AACpD;AAAA;AAAA,gDAAaK,sBAAb,CAAoCH,GAApC,EAAyCE,SAAzC;AACH;AACJ;AACJ;;AAEME,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B,cAAI,CAAC,KAAKtD,OAAV,EAAmB;AAEnB,eAAKC,WAAL,IAAoBqD,EAApB;;AACA,cAAI,KAAKrD,WAAL,GAAmB,KAAKP,IAAL,CAAU5B,QAAV,CAAmBsB,KAA1C,EAAiD;AAC7C;AAAA;AAAA,8CAAa2B,eAAb,CAA6B,IAA7B;AACA;AACH,WAPyB,CAS1B;;;AACA,eAAKL,KAAL,CAAY2C,IAAZ,CAAiBC,EAAE,GAAG,IAAtB;AACA,cAAI,KAAK5D,IAAL,CAAU6D,gBAAd,EACI,KAAK7D,IAAL,CAAU2C,SAAV;AACP;;AAEMmB,QAAAA,WAAW,GAAS;AACvB,eAAKxD,OAAL,GAAe,KAAf;;AACA,cAAI,KAAKI,WAAL,IAAoB,KAAKA,WAAL,CAAiB4C,MAAjB,GAA0B,CAAlD,EAAqD;AACjD,iBAAK5C,WAAL,CAAiBqD,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAACC,OAAN,EAAlC,EADiD,CACG;;AACpD,iBAAKvD,WAAL,GAAmB,EAAnB,CAFiD,CAE1B;AAC1B;;AAED,eAAKM,KAAL,CAAW4B,UAAX,CAAsB,KAAtB;AACA,eAAKtB,QAAL,CAAee,QAAf,GAA0B,KAA1B;AACA,eAAK6B,aAAL;AACA,eAAKC,SAAL,GAVuB,CAWvB;;AACA,cAAI,CAAC,KAAKnD,KAAL,CAAWoD,SAAZ,IAAyB,KAAKpE,IAAL,CAAU3B,YAAnC,IAAmD,KAAK2B,IAAL,CAAU3B,YAAV,CAAuBqB,KAAvB,GAA+B,CAAtF,EAAyF;AACrF,iBAAK2E,YAAL,CAAkB,MAAM;AACpB,mBAAKrB,WAAL;AACH,aAFD,EAEG,KAAKhD,IAAL,CAAU3B,YAAV,CAAuBqB,KAF1B;AAGH,WAJD,MAIO;AACH,iBAAKsD,WAAL;AACH;AACJ;;AAEDsB,QAAAA,SAAS,CAAChD,QAAD,EAAsB;AAC3B,eAAKiD,MAAL;AACH;;AAEMA,QAAAA,MAAM,GAAG;AACZ;AAAA;AAAA,4CAAalD,eAAb,CAA6B,IAA7B;AACH;;AAEDmD,QAAAA,SAAS,GAAW;AAChB,cAAM3C,GAAG,GAAG,KAAKrB,OAAL,CAAasB,SAAb,EAAZ;AACA,iBAAOD,GAAG,CAAE2C,SAAL,EAAP;AACH;;AACDC,QAAAA,UAAU,CAACC,QAAD,EAA8B;AACpC,cAAM7C,GAAG,GAAG,KAAKrB,OAAL,CAAasB,SAAb,EAAZ;;AACA,cAAI,CAACD,GAAL,EAAU;AACN,mBAAO,CAAP;AACH;;AAED,iBAAO;AAAA;AAAA,8CAAc8C,gBAAd,CAA+B9C,GAAG,CAAC+C,SAAnC,EAA8CF,QAAQ,CAACE,SAAvD,EACH,KAAKzF,MAAL,CAAa0F,iBADV,EAC6B,IAD7B,EACmC,KAAK1F,MAAL,CAAa2F,UADhD,EAC4D,KAD5D,EACmE;AAAC;AADpE,WAAP;AAEH;AAED;AACJ;AACA;;;AACIC,QAAAA,0BAA0B,GAAW;AACjC,cAAMC,gBAAgB,GAAG;AAAA;AAAA,kCAAQA,gBAAjC;AACA,cAAI,CAACA,gBAAgB,CAACC,SAAtB,EAAiC,OAAO,CAAP;AACjC,cAAMC,GAAG,GAAG,KAAK1D,IAAL,CAAU2D,QAAtB;AACA,cAAMC,SAAS,GAAGJ,gBAAgB,CAACC,SAAjB,CAA2BzD,IAA3B,CAAgC2D,QAAlD;AACA,iBAAO,CAACD,GAAG,CAACG,CAAJ,GAAQD,SAAS,CAACC,CAAnB,KAAyBH,GAAG,CAACG,CAAJ,GAAQD,SAAS,CAACC,CAA3C,IAAgD,CAACH,GAAG,CAACI,CAAJ,GAAQF,SAAS,CAACE,CAAnB,KAAyBJ,GAAG,CAACI,CAAJ,GAAQF,SAAS,CAACE,CAA3C,CAAvD;AACH;;AA3M8B,O;;;;;;;;;;iBAKI,I;;;;;;;iBAGD,I;;;;;;;iBAGI,I", "sourcesContent": ["import { _decorator, misc, Component, Node, Sprite, Color, Prefab, AudioClip } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { BulletData } from '../data/bullet/BulletData';\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { Movable, eSpriteDefaultFacing, eMoveEvent } from '../move/Movable';\r\nimport { BulletSystem } from './BulletSystem';\r\nimport { EventGroup, EventGroupContext } from './EventGroup';\r\nimport { Property, PropertyContainer } from './PropertyContainer';\r\nimport { Emitter } from './Emitter';\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport FCollider, { ColliderGroupType } from 'db://assets/bundles/common/script/game/collider-system/FCollider';\r\nimport FBoxCollider from 'db://assets/bundles/common/script/game/collider-system/FBoxCollider';\r\nimport Entity from 'db://assets/bundles/common/script/game/ui/base/Entity';\r\nimport { eEntityTag } from 'db://assets/bundles/common/script/game/ui/base/Entity';\r\nimport { Logger, logWarn } from 'db://assets/scripts/utils/Logger';\r\nimport { ResEmitter, BulletSourceType, BulletType } from 'db://assets/bundles/common/script/autogen/luban/schema';\r\nimport { GameIns } from 'db://assets/bundles/common/script/game/GameIns';\r\nimport PlaneBase from '../ui/plane/PlaneBase';\r\nimport { AttributeConst } from '../../const/AttributeConst';\r\nimport { AttributeData } from '../../data/base/AttributeData';\r\n\r\nconst { ccclass, property, executeInEditMode, requireComponent } = _decorator;\r\n\r\nexport class BulletProperty extends PropertyContainer<number> {\r\n    public duration!: Property<number>;                // 子弹持续时间(超出后销毁回收)\r\n    public delayDestroy!: Property<number>;            // 延迟销毁时间\r\n\r\n    public attack!: Property<number>;                  // 子弹伤害\r\n    public speed!: Property<number>;                   // 子弹速度\r\n    public speedAngle!: Property<number>;              // 子弹速度角度\r\n    public acceleration!: Property<number>;            // 子弹加速度\r\n    public accelerationAngle!: Property<number>;       // 子弹加速度角度\r\n    public scale!: Property<number>;                   // 子弹缩放\r\n    public color!: Property<Color>;                    // 子弹颜色\r\n    public defaultFacing!: Property<eSpriteDefaultFacing>;          // 子弹初始朝向\r\n\r\n    public isDestroyOutScreen!: Property<boolean>;     // 是否超出屏幕销毁\r\n    public isDestructive!: Property<boolean>;          // 是否可被破坏\r\n    public isDestructiveOnHit!: Property<boolean>;     // 命中时是否被销毁\r\n    public isFacingMoveDir!: Property<boolean>;        // 是否面向移动方向\r\n    public isTrackingTarget!: Property<boolean>;       // 是否追踪目标\r\n\r\n    public config: ResEmitter|undefined = undefined;\r\n\r\n    constructor() {\r\n        super();\r\n        this.duration = this.addProperty(0, 6000);\r\n        this.delayDestroy = this.addProperty(1, 0);\r\n        this.attack = this.addProperty(2, 1);\r\n        this.speed = this.addProperty(3, 600);\r\n        this.speedAngle = this.addProperty(4, 0);\r\n        this.acceleration = this.addProperty(5, 0);\r\n        this.accelerationAngle = this.addProperty(6, 0);\r\n        this.scale = this.addProperty(7, 1);\r\n        this.color = this.addProperty(8, Color.WHITE);\r\n        this.defaultFacing = this.addProperty<eSpriteDefaultFacing>(9, eSpriteDefaultFacing.Up);\r\n        this.isDestroyOutScreen = this.addProperty(10, true);\r\n        this.isDestructive = this.addProperty(11, false);\r\n        this.isDestructiveOnHit = this.addProperty(12, false);\r\n        this.isFacingMoveDir = this.addProperty(13, false);\r\n        this.isTrackingTarget = this.addProperty(14, false);\r\n    }\r\n\r\n    public resetFromData(data: BulletData) {\r\n        this.duration.value = data.duration.eval(null, true); \r\n        this.delayDestroy.value = data.delayDestroy.eval(null, true); \r\n        this.speed.value = data.speed.eval(null, true); \r\n        // this.speedAngle.value = data.speedAngle.eval(); \r\n        this.acceleration.value = data.acceleration.eval(null, true); \r\n        this.accelerationAngle.value = data.accelerationAngle.eval(null, true); \r\n        this.scale.value = data.scale.eval(null, true); \r\n        // this.color.value = data.color.eval(); \r\n        this.isDestroyOutScreen.value = data.isDestroyOutScreen; \r\n        this.isDestructive.value = data.isDestructive; \r\n        this.isDestructiveOnHit.value = data.isDestructiveOnHit; \r\n        this.isFacingMoveDir.value = data.isFacingMoveDir; \r\n        this.isTrackingTarget.value = data.isTrackingTarget;\r\n    }\r\n\r\n    public copyFrom(other: BulletProperty) {\r\n        this.forEachProperty((k, prop) => {\r\n            prop.value = other.getPropertyValue(k)!;\r\n        });\r\n    }\r\n\r\n    public clear() {\r\n        // Clear all listeners\r\n        this.forEachProperty((k, prop) => prop.clear());\r\n    }\r\n}\r\n\r\n// 子弹 Bullet\r\n// 如何集成到项目里? \r\n@ccclass('Bullet')\r\n@executeInEditMode(true)\r\n@requireComponent(Movable)\r\nexport class Bullet extends Entity {\r\n    @property({type: Movable, displayName: \"移动组件\"})\r\n    public mover!: Movable;\r\n\r\n    @property({type: Sprite, displayName: \"子弹精灵\"})\r\n    public bulletSprite: Sprite|null = null;\r\n\r\n    @property({type: FCollider, displayName: '碰撞组件'})\r\n    public collider: FCollider|null = null;\r\n\r\n    @property({type: Prefab, displayName: \"碰撞特效\"})\r\n    public hitEffectPrefab: Prefab|null = null;\r\n    \r\n    public isAlive: boolean = false;\r\n    public elapsedTime: number = 0;         // 子弹存活时间\r\n    public emitter!: Emitter;\r\n    public bulletData!: BulletData;         // 子弹编辑器下的配置\r\n    public config: ResEmitter|undefined = undefined;   // 子弹表格配置\r\n    // 以下属性重新定义一遍, 作为可修改的属性, 部分定义在movable里\r\n    public prop: BulletProperty = new BulletProperty();\r\n    public eventGroups: EventGroup[] = [];\r\n\r\n    // 增加部分便捷接口获取子弹属性\r\n    public get sourceType(): BulletSourceType {\r\n        return this.config?.source || BulletSourceType.MAINPLANE;\r\n    }\r\n    public get type(): BulletType {\r\n        return this.config?.type || BulletType.NORMAL;\r\n    }\r\n\r\n    onLoad(): void {\r\n        if (!this.mover) {\r\n            this.mover = this.getComponent(Movable) || this.addComponent(Movable)!;\r\n        }\r\n        this.mover.on(eMoveEvent.onBecomeInvisible, () => {\r\n            if (this.prop.isDestroyOutScreen.value) {\r\n                BulletSystem.onDestroyBullet(this);\r\n            }\r\n        });\r\n        if (!this.collider) {\r\n            let boxCollider = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider)!;\r\n            this.collider = boxCollider;\r\n        }\r\n \r\n        this.prop.defaultFacing.value = this.mover.defaultFacing;\r\n        // listen to property changes\r\n        this.prop.isFacingMoveDir.on((value) => {\r\n            this.mover.isFacingMoveDir = value;\r\n        });\r\n        this.prop.isTrackingTarget.on((value) => {\r\n            this.mover.isTrackingTarget = value;\r\n        });\r\n        this.prop.speed.on((value) => {\r\n            this.mover.speed = value;\r\n        });\r\n        this.prop.speedAngle.on((value) => {\r\n            this.mover.speedAngle = value;\r\n        });\r\n        this.prop.acceleration.on((value) => {\r\n            this.mover.acceleration = value;\r\n        });\r\n        this.prop.accelerationAngle.on((value) => {\r\n            this.mover.accelerationAngle = value;\r\n        });\r\n        this.prop.scale.on((value) => {\r\n            this.node.setScale(value, value, value);\r\n        });\r\n        this.prop.color.on((value) => {\r\n            if (this.bulletSprite) {\r\n                this.bulletSprite.color = value;\r\n            }\r\n        });\r\n    }\r\n\r\n    public onCreate(emitter: Emitter, bulletConfig: ResEmitter|undefined): void {\r\n        this.isAlive = true;\r\n        this.elapsedTime = 0;\r\n        this.emitter = emitter;\r\n        this.bulletData = emitter.bulletData!;\r\n        this.config = bulletConfig;\r\n\r\n        // TODO: 创建entity的时候，设置正确的tag.\r\n        const ent = emitter.getEntity();\r\n        if (ent) {\r\n            const isShootFromEnemy = this.config?.source == BulletSourceType.ENEMYPLANE;\r\n            this.collider!.initBaseData(this);\r\n            this.collider!.groupType = isShootFromEnemy ? ColliderGroupType.BULLET_ENEMY : ColliderGroupType.BULLET_SELF;\r\n            this.collider!.isEnable = true;\r\n            this.addTag(isShootFromEnemy ? eEntityTag.EnemyBullet : eEntityTag.PlayerBullet);\r\n        } else {\r\n            logWarn(\"emitter\", \"onCreate bullet\", \"has no entity\");\r\n        }\r\n\r\n        this.resetProperties();\r\n    }\r\n\r\n    public onReady() {\r\n        this.prop.notifyAll(true);\r\n        this.mover.setMovable(true);\r\n        this.resetEventGroups();\r\n        \r\n        const ent = this.emitter.getEntity();\r\n        if (ent) {\r\n            this.mover.setTarget(ent.getTarget());\r\n        }\r\n    }\r\n\r\n    private destroySelf() {\r\n        if (!this.node || !this.node.isValid) return;\r\n        \r\n        if (EDITOR) {\r\n            this.node.destroy();\r\n        } else {\r\n            ObjectPool.returnNode(this.node);\r\n        }\r\n    }\r\n\r\n    public resetProperties(): void {\r\n        if (!this.emitter) return;\r\n\r\n        this.prop.copyFrom(this.emitter.bulletProp);\r\n        this.prop.notifyAll(true);\r\n    }\r\n\r\n    public resetEventGroups(): void {\r\n        // create event groups here\r\n        if (this.bulletData && this.bulletData.eventGroupData.length > 0) {\r\n            let ctx = new EventGroupContext();\r\n            ctx.bullet = this;\r\n            ctx.emitter = this.emitter;\r\n            for (const eventName of this.bulletData.eventGroupData) {\r\n                BulletSystem.createBulletEventGroup(ctx, eventName);\r\n            }\r\n        }\r\n    }\r\n\r\n    public tick(dt: number): void {\r\n        if (!this.isAlive) return;\r\n\r\n        this.elapsedTime += dt;\r\n        if (this.elapsedTime > this.prop.duration.value) {\r\n            BulletSystem.onDestroyBullet(this);\r\n            return;\r\n        }\r\n        \r\n        // 毫秒 -> 秒\r\n        this.mover!.tick(dt / 1000);\r\n        if (this.prop.anyPropertyDirty) \r\n            this.prop.notifyAll();\r\n    }\r\n\r\n    public willDestroy(): void {\r\n        this.isAlive = false;\r\n        if (this.eventGroups && this.eventGroups.length > 0) {\r\n            this.eventGroups.forEach(group => group.tryStop()); // stop all event groups before destroying the bullet itself.\r\n            this.eventGroups = []; // clear the event groups array\r\n        }\r\n        \r\n        this.mover.setMovable(false);\r\n        this.collider!.isEnable = false;\r\n        this.removeAllComp();\r\n        this.clearTags();\r\n        // 只有离开屏幕（不可见）的时候，才启用延迟\r\n        if (!this.mover.isVisible && this.prop.delayDestroy && this.prop.delayDestroy.value > 0) {\r\n            this.scheduleOnce(() => {\r\n                this.destroySelf();\r\n            }, this.prop.delayDestroy.value);\r\n        } else {\r\n            this.destroySelf();\r\n        }\r\n    }\r\n\r\n    onCollide(collider: FCollider) {\r\n        this.remove();\r\n    }\r\n\r\n    public remove() {\r\n        BulletSystem.onDestroyBullet(this);\r\n    }\r\n\r\n    getAttack(): number {\r\n        const ent = this.emitter.getEntity();\r\n        return ent!.getAttack();\r\n    }\r\n    calcDamage(defender: PlaneBase): number {\r\n        const ent = this.emitter.getEntity();\r\n        if (!ent) {\r\n            return 0;\r\n        }\r\n\r\n        return AttributeData.CalcBulletDamage(ent.attribute, defender.attribute, \r\n            this.config!.attackCoefficient, true, this.config!.damageType, false, 1/* TODO ybgg 攻击力修正 */);\r\n    }\r\n\r\n    /** \r\n     * 获取子弹到玩家的距离平方 \r\n     */\r\n    getSquaredDistanceToPlayer(): number {\r\n        const mainPlaneManager = GameIns.mainPlaneManager;\r\n        if (!mainPlaneManager.mainPlane) return 0;\r\n        const pos = this.node.position;\r\n        const playerPos = mainPlaneManager.mainPlane.node.position;\r\n        return (pos.x - playerPos.x) * (pos.x - playerPos.x) + (pos.y - playerPos.y) * (pos.y - playerPos.y);\r\n    }\r\n}\r\n"]}