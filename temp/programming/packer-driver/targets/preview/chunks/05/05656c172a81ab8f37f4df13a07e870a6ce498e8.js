System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, MyApp, logInfo, logWarn, BaseComp, forEachEntityByTargetType, SkillComp, _crd;

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogInfo(extras) {
    _reporterNs.report("logInfo", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogWarn(extras) {
    _reporterNs.report("logWarn", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseComp(extras) {
    _reporterNs.report("BaseComp", "../../base/BaseComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "db://assets/bundles/common/script/game/ui/plane/PlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfforEachEntityByTargetType(extras) {
    _reporterNs.report("forEachEntityByTargetType", "./SearchTarget", _context.meta, extras);
  }

  _export("default", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      logInfo = _unresolved_3.logInfo;
      logWarn = _unresolved_3.logWarn;
    }, function (_unresolved_4) {
      BaseComp = _unresolved_4.default;
    }, function (_unresolved_5) {
      forEachEntityByTargetType = _unresolved_5.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "a0b80LTvOZBP779siDvr8m7", "SkillComp", undefined);

      _export("default", SkillComp = class SkillComp extends (_crd && BaseComp === void 0 ? (_reportPossibleCrUseOfBaseComp({
        error: Error()
      }), BaseComp) : BaseComp) {
        Cast(caster, skillID) {
          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("Skill", "cast skill " + skillID);
          var skillData = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbResSkill.get(skillID);

          if (!skillData) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("Skill", "cast skill " + skillID + " but config not found");
            return;
          }

          skillData.ApplyBuffs.forEach(applyBuff => {
            (_crd && forEachEntityByTargetType === void 0 ? (_reportPossibleCrUseOfforEachEntityByTargetType({
              error: Error()
            }), forEachEntityByTargetType) : forEachEntityByTargetType)(caster, applyBuff.target, entity => {
              entity.buffComp.ApplyBuff(false, applyBuff.buffID);
            });
          });
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=05656c172a81ab8f37f4df13a07e870a6ce498e8.js.map