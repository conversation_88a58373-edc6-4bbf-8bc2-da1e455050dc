{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/skill/SkillComp.ts"], "names": ["<PERSON>llComp", "MyApp", "logInfo", "log<PERSON>arn", "BaseComp", "forEachEntityByTargetType", "Cast", "caster", "skillID", "skillData", "lubanTables", "TbResSkill", "get", "ApplyBuffs", "for<PERSON>ach", "applyBuff", "target", "entity", "buff<PERSON><PERSON>p", "A<PERSON><PERSON><PERSON><PERSON>", "buff<PERSON>"], "mappings": ";;;4FAQqBA,S;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AARZC,MAAAA,K,iBAAAA,K;;AAEAC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,O,iBAAAA,O;;AAEXC,MAAAA,Q;;AAEAC,MAAAA,yB;;;;;;;yBAEcL,S,GAAN,MAAMA,SAAN;AAAA;AAAA,gCAAiC;AAC5CM,QAAAA,IAAI,CAACC,MAAD,EAAoBC,OAApB,EAAqC;AACrC;AAAA;AAAA,kCAAQ,OAAR,kBAA+BA,OAA/B;AACA,cAAIC,SAAS,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,UAAlB,CAA6BC,GAA7B,CAAiCJ,OAAjC,CAAhB;;AACA,cAAI,CAACC,SAAL,EAAgB;AACZ;AAAA;AAAA,oCAAQ,OAAR,kBAA+BD,OAA/B;AACA;AACH;;AACDC,UAAAA,SAAS,CAACI,UAAV,CAAqBC,OAArB,CAA8BC,SAAD,IAAe;AACxC;AAAA;AAAA,wEAA0BR,MAA1B,EAAkCQ,SAAS,CAACC,MAA5C,EAAqDC,MAAD,IAAY;AAC3DA,cAAAA,MAAD,CAAsBC,QAAtB,CAA+BC,SAA/B,CAAyC,KAAzC,EAAgDJ,SAAS,CAACK,MAA1D;AACH,aAFD;AAGH,WAJD;AAKH;;AAb2C,O", "sourcesContent": ["import { MyApp } from \"db://assets/bundles/common/script/app/MyApp\";\r\nimport { TargetType } from \"db://assets/bundles/common/script/autogen/luban/schema\";\r\nimport { logInfo, logWarn } from \"db://assets/scripts/utils/Logger\";\r\nimport { GameIns } from \"../../../GameIns\";\r\nimport BaseComp from \"../../base/BaseComp\";\r\nimport type PlaneBase from \"db://assets/bundles/common/script/game/ui/plane/PlaneBase\";\r\nimport forEachEntityByTargetType from \"./SearchTarget\";\r\n\r\nexport default class SkillComp extends BaseComp {\r\n    Cast(caster: PlaneBase, skillID: number) {\r\n        logInfo(\"Skill\", `cast skill ${skillID}`);\r\n        let skillData = MyApp.lubanTables.TbResSkill.get(skillID);\r\n        if (!skillData) {\r\n            logWarn(\"Skill\", `cast skill ${skillID} but config not found`)\r\n            return;\r\n        }\r\n        skillData.ApplyBuffs.forEach((applyBuff) => {\r\n            forEachEntityByTargetType(caster, applyBuff.target, (entity) => {\r\n                (entity as PlaneBase).buffComp.ApplyBuff(false, applyBuff.buffID);\r\n            })\r\n        })\r\n    }\r\n}"]}