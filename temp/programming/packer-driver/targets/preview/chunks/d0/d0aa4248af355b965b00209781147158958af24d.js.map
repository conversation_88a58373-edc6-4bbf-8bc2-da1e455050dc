{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/move/Movable.ts"], "names": ["_decorator", "Component", "Enum", "misc", "UITransform", "Vec2", "Vec3", "BulletSystem", "degreesToRadians", "radiansToDegrees", "ccclass", "property", "executeInEditMode", "eSpriteDefaultFacing", "eMoveEvent", "Movable", "type", "displayName", "isFacingMoveDir", "isTrackingTarget", "speed", "speedAngle", "turnSpeed", "acceleration", "accelerationAngle", "tiltSpeed", "tiltOffset", "target", "arrivalDistance", "_selfSize", "_position", "_tiltTime", "_basePosition", "_wasVisible", "_isVisible", "_isMovable", "_visibility<PERSON><PERSON><PERSON><PERSON>ounter", "_eventListeners", "Map", "isVisible", "isMovable", "onLoad", "uiTransform", "node", "getComponent", "self_size", "contentSize", "width", "height", "set", "onDestroy", "clear", "on", "event", "listener", "has", "listeners", "get", "includes", "push", "off", "index", "indexOf", "splice", "removeAllListeners", "emit", "length", "for<PERSON>ach", "tick", "dt", "angleRadians", "velocityX", "Math", "cos", "velocityY", "sin", "targetPos", "getPosition", "currentPos", "directionX", "x", "directionY", "y", "distance", "sqrt", "desiredAngle", "atan2", "angleDiff", "normalizedAngleDiff", "trackingStrength", "maxTurnRate", "turnAmount", "min", "abs", "sign", "angleRadiansAfterTracking", "accelerationRadians", "accelerationX", "accelerationY", "moveAngleRad", "perpX", "perpY", "tiltAmount", "setPosition", "VISIBILITY_CHECK_INTERVAL", "checkVisibility", "finalAngle", "defaultFacing", "setRotationFromEuler", "visibleSize", "worldBounds", "position", "worldPosition", "xMin", "xMax", "yMax", "yMin", "setVisible", "visible", "onBecomeVisible", "onBecomeInvisible", "<PERSON><PERSON><PERSON><PERSON>", "setTracking", "tracking", "setMovable", "movable", "Up"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAC5DC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OAGH;AAAEC,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCN,I;OACzC;AAAEO,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CZ,U;;sCAErCa,oB,0BAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;eAAAA,oB;;;4BAOAC,U,0BAAAA,U;AAAAA,QAAAA,U,CAAAA,U;AAAAA,QAAAA,U,CAAAA,U;eAAAA,U;;;yBAOCC,O,WAFZL,OAAO,CAAC,SAAD,C,UAIHC,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEd,IAAI,CAACW,oBAAD,CAAZ;AAAoCI,QAAAA,WAAW,EAAE;AAAjD,OAAD,C,gBAHZL,iB,gCADD,MAEaG,OAFb,SAE6Bd,SAF7B,CAE2D;AAAA;AAAA;;AAAA;;AAAA,eAKhDiB,eALgD,GAKrB,KALqB;AAKT;AALS,eAMhDC,gBANgD,GAMpB,KANoB;AAMT;AANS,eAOhDC,KAPgD,GAOhC,CAPgC;AAOT;AAPS,eAQhDC,UARgD,GAQ3B,CAR2B;AAQT;AARS,eAShDC,SATgD,GAS5B,EAT4B;AAST;AATS,eAUhDC,YAVgD,GAUzB,CAVyB;AAUT;AAVS,eAWhDC,iBAXgD,GAWpB,CAXoB;AAWT;AAE9C;AAbuD,eAchDC,SAdgD,GAc5B,CAd4B;AAcT;AAC9C;AAfuD,eAgBhDC,UAhBgD,GAgB3B,GAhB2B;AAgBR;AAhBQ,eAkBhDC,MAlBgD,GAkB1B,IAlB0B;AAkBT;AAlBS,eAmBhDC,eAnBgD,GAmBtB,EAnBsB;AAmBT;AAnBS,eAqB/CC,SArB+C,GAqB7B,IAAIxB,IAAJ,EArB6B;AAAA,eAsB/CyB,SAtB+C,GAsB7B,IAAIxB,IAAJ,EAtB6B;AAAA,eAuB/CyB,SAvB+C,GAuB3B,CAvB2B;AAuBT;AAvBS,eAwB/CC,aAxB+C,GAwBzB,IAAI1B,IAAJ,EAxByB;AAwBT;AAxBS,eA0B/C2B,WA1B+C,GA0BxB,KA1BwB;AAAA,eA2B/CC,UA3B+C,GA2BzB,KA3ByB;AAAA,eA6B/CC,UA7B+C,GA6BzB,IA7ByB;AAAA,eAgC/CC,uBAhC+C,GAgCb,CAhCa;AAiCA;AAEvD;AAnCuD,eAoC/CC,eApC+C,GAoCO,IAAIC,GAAJ,EApCP;AAAA;;AA2BR;AAC3B,YAATC,SAAS,GAAG;AAAE,iBAAO,KAAKL,UAAZ;AAAyB;;AACJ;AAC1B,YAATM,SAAS,GAAG;AAAE,iBAAO,KAAKL,UAAZ;AAAyB;;AAQlDM,QAAAA,MAAM,GAAG;AACL,cAAMC,WAAW,GAAG,KAAKC,IAAL,CAAUC,YAAV,CAAuBxC,WAAvB,CAApB;AACA,cAAMyC,SAAS,GAAGH,WAAW,GAAGA,WAAW,CAACI,WAAf,GAA6B;AAAEC,YAAAA,KAAK,EAAE,CAAT;AAAYC,YAAAA,MAAM,EAAE;AAApB,WAA1D;;AACA,eAAKnB,SAAL,CAAeoB,GAAf,CAAmBJ,SAAS,CAACE,KAAV,GAAkB,CAArC,EAAwCF,SAAS,CAACG,MAAV,GAAmB,CAA3D;AACH;;AAEDE,QAAAA,SAAS,GAAG;AACR;AACA,eAAKb,eAAL,CAAqBc,KAArB;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWC,QAAAA,EAAE,CAACC,KAAD,EAAoBC,QAApB,EAAgD;AACrD,cAAI,CAAC,KAAKjB,eAAL,CAAqBkB,GAArB,CAAyBF,KAAzB,CAAL,EAAsC;AAClC,iBAAKhB,eAAL,CAAqBY,GAArB,CAAyBI,KAAzB,EAAgC,EAAhC;AACH;;AACD,cAAMG,SAAS,GAAG,KAAKnB,eAAL,CAAqBoB,GAArB,CAAyBJ,KAAzB,CAAlB;;AACA,cAAI,CAACG,SAAS,CAACE,QAAV,CAAmBJ,QAAnB,CAAL,EAAmC;AAC/BE,YAAAA,SAAS,CAACG,IAAV,CAAeL,QAAf;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACWM,QAAAA,GAAG,CAACP,KAAD,EAAoBC,QAApB,EAAgD;AACtD,cAAME,SAAS,GAAG,KAAKnB,eAAL,CAAqBoB,GAArB,CAAyBJ,KAAzB,CAAlB;;AACA,cAAIG,SAAJ,EAAe;AACX,gBAAMK,KAAK,GAAGL,SAAS,CAACM,OAAV,CAAkBR,QAAlB,CAAd;;AACA,gBAAIO,KAAK,KAAK,CAAC,CAAf,EAAkB;AACdL,cAAAA,SAAS,CAACO,MAAV,CAAiBF,KAAjB,EAAwB,CAAxB;AACH;AACJ;AACJ;;AAEMG,QAAAA,kBAAkB,GAAS;AAC9B,eAAK3B,eAAL,CAAqBc,KAArB;AACH;AAED;AACJ;AACA;AACA;;;AACYc,QAAAA,IAAI,CAACZ,KAAD,EAA0B;AAClC,cAAMG,SAAS,GAAG,KAAKnB,eAAL,CAAqBoB,GAArB,CAAyBJ,KAAzB,CAAlB;;AACA,cAAIG,SAAS,IAAIA,SAAS,CAACU,MAAV,GAAmB,CAApC,EAAuC;AACnCV,YAAAA,SAAS,CAACW,OAAV,CAAkBb,QAAQ,IAAIA,QAAQ,EAAtC;AACH;AACJ;;AAEMc,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B,cAAI,CAAC,KAAKlC,UAAV,EAAsB;AAEtB,cAAMmC,YAAY,GAAG9D,gBAAgB,CAAC,KAAKa,UAAN,CAArC,CAH0B,CAI1B;;AACA,cAAIkD,SAAS,GAAG,KAAKnD,KAAL,GAAaoD,IAAI,CAACC,GAAL,CAASH,YAAT,CAA7B;AACA,cAAII,SAAS,GAAG,KAAKtD,KAAL,GAAaoD,IAAI,CAACG,GAAL,CAASL,YAAT,CAA7B;;AAEA,cAAI,KAAKnD,gBAAL,IAAyB,KAAKQ,MAAlC,EAA0C;AACtC,gBAAMiD,SAAS,GAAG,KAAKjD,MAAL,CAAYkD,WAAZ,EAAlB;AACA,gBAAMC,UAAU,GAAG,KAAK9C,aAAxB,CAFsC,CAItC;;AACA,gBAAM+C,UAAU,GAAGH,SAAS,CAACI,CAAV,GAAcF,UAAU,CAACE,CAA5C;AACA,gBAAMC,UAAU,GAAGL,SAAS,CAACM,CAAV,GAAcJ,UAAU,CAACI,CAA5C;AACA,gBAAMC,QAAQ,GAAGX,IAAI,CAACY,IAAL,CAAUL,UAAU,GAAGA,UAAb,GAA0BE,UAAU,GAAGA,UAAjD,CAAjB;;AAEA,gBAAIE,QAAQ,GAAG,CAAf,EAAkB;AACd;AACA,kBAAME,YAAY,GAAG5E,gBAAgB,CAAC+D,IAAI,CAACc,KAAL,CAAWL,UAAX,EAAuBF,UAAvB,CAAD,CAArC,CAFc,CAId;;AACA,kBAAMQ,SAAS,GAAGF,YAAY,GAAG,KAAKhE,UAAtC,CALc,CAMd;;AACA,kBAAMmE,mBAAmB,GAAI,CAACD,SAAS,GAAG,GAAb,IAAoB,GAArB,GAA4B,GAAxD,CAPc,CASd;;AACA,kBAAME,gBAAgB,GAAG,GAAzB,CAVc,CAUgB;;AAC9B,kBAAMC,WAAW,GAAG,KAAKpE,SAAzB,CAXc,CAWsB;;AACpC,kBAAMqE,UAAU,GAAGnB,IAAI,CAACoB,GAAL,CAASpB,IAAI,CAACqB,GAAL,CAASL,mBAAT,CAAT,EAAwCE,WAAW,GAAGrB,EAAtD,IAA4DG,IAAI,CAACsB,IAAL,CAAUN,mBAAV,CAA/E;AAEA,mBAAKnE,UAAL,IAAmBsE,UAAU,GAAGF,gBAAhC;AACA,kBAAMM,yBAAyB,GAAGvF,gBAAgB,CAAC,KAAKa,UAAN,CAAlD,CAfc,CAiBd;;AACAkD,cAAAA,SAAS,GAAG,KAAKnD,KAAL,GAAaoD,IAAI,CAACC,GAAL,CAASsB,yBAAT,CAAzB;AACArB,cAAAA,SAAS,GAAG,KAAKtD,KAAL,GAAaoD,IAAI,CAACG,GAAL,CAASoB,yBAAT,CAAzB;AACH;AACJ,WAtCyB,CAwC1B;;;AACA,cAAI,KAAKxE,YAAL,KAAsB,CAA1B,EAA6B;AACzB,gBAAMyE,mBAAmB,GAAGxF,gBAAgB,CAAC,KAAKgB,iBAAN,CAA5C;AACA,gBAAMyE,aAAa,GAAG,KAAK1E,YAAL,GAAoBiD,IAAI,CAACC,GAAL,CAASuB,mBAAT,CAA1C;AACA,gBAAME,aAAa,GAAG,KAAK3E,YAAL,GAAoBiD,IAAI,CAACG,GAAL,CAASqB,mBAAT,CAA1C,CAHyB,CAIzB;;AACAzB,YAAAA,SAAS,IAAI0B,aAAa,GAAG5B,EAA7B;AACAK,YAAAA,SAAS,IAAIwB,aAAa,GAAG7B,EAA7B;AACH,WAhDyB,CAkD1B;;;AACA,eAAKjD,KAAL,GAAaoD,IAAI,CAACY,IAAL,CAAUb,SAAS,GAAGA,SAAZ,GAAwBG,SAAS,GAAGA,SAA9C,CAAb;AACA,eAAKrD,UAAL,GAAkBZ,gBAAgB,CAAC+D,IAAI,CAACc,KAAL,CAAWZ,SAAX,EAAsBH,SAAtB,CAAD,CAAlC,CApD0B,CAsD1B;;AACA,cAAIA,SAAS,KAAK,CAAd,IAAmBG,SAAS,KAAK,CAArC,EAAwC;AACpC;AACA,iBAAK1C,aAAL,CAAmBgD,CAAnB,IAAwBT,SAAS,GAAGF,EAApC;AACA,iBAAKrC,aAAL,CAAmBkD,CAAnB,IAAwBR,SAAS,GAAGL,EAApC,CAHoC,CAKpC;;AACA,iBAAKvC,SAAL,CAAemB,GAAf,CAAmB,KAAKjB,aAAxB,EANoC,CAQpC;;;AACA,gBAAI,KAAKP,SAAL,GAAiB,CAAjB,IAAsB,KAAKC,UAAL,GAAkB,CAA5C,EAA+C;AAC3C;AACA,mBAAKK,SAAL,IAAkBsC,EAAlB,CAF2C,CAI3C;AACA;;AACA,kBAAM8B,YAAY,GAAG3F,gBAAgB,CAAC,KAAKa,UAAN,CAArC;AACA,kBAAM+E,KAAK,GAAG,CAAC5B,IAAI,CAACG,GAAL,CAASwB,YAAT,CAAf;AACA,kBAAME,KAAK,GAAG7B,IAAI,CAACC,GAAL,CAAS0B,YAAT,CAAd,CAR2C,CAU3C;;AACA,kBAAMG,UAAU,GAAG9B,IAAI,CAACG,GAAL,CAAS,KAAK5C,SAAL,GAAiB,KAAKN,SAA/B,IAA4C,KAAKC,UAApE,CAX2C,CAa3C;;AACA,mBAAKI,SAAL,CAAekD,CAAf,IAAoBoB,KAAK,GAAGE,UAA5B;AACA,mBAAKxE,SAAL,CAAeoD,CAAf,IAAoBmB,KAAK,GAAGC,UAA5B;AACH;;AAED,iBAAK3D,IAAL,CAAU4D,WAAV,CAAsB,KAAKzE,SAA3B,EA3BoC,CA6BpC;;AACA,gBAAI,EAAE,KAAKM,uBAAP,IAAkCrB,OAAO,CAACyF,yBAA9C,EAAyE;AACrE,mBAAKpE,uBAAL,GAA+B,CAA/B;AACA,mBAAKqE,eAAL;AACH;AACJ;;AAED,cAAI,KAAKvF,eAAL,IAAwB,KAAKE,KAAL,GAAa,CAAzC,EAA4C;AACxC,gBAAMsF,UAAU,GAAG,KAAKrF,UAAL,GAAkB,KAAKsF,aAA1C;AACA,iBAAKhE,IAAL,CAAUiE,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqCF,UAArC;AACH;AACJ;;AAEMD,QAAAA,eAAe,GAAS;AAC3B;AACA;AACA,cAAMI,WAAW,GAAG;AAAA;AAAA,4CAAaC,WAAjC;AACA,cAAMC,QAAQ,GAAG,KAAKpE,IAAL,CAAUqE,aAA3B;AACA,cAAMzE,SAAS,GAAIwE,QAAQ,CAAC/B,CAAT,GAAa,KAAKnD,SAAL,CAAemD,CAA7B,IAAmC6B,WAAW,CAACI,IAA/C,IACbF,QAAQ,CAAC/B,CAAT,GAAa,KAAKnD,SAAL,CAAemD,CAA7B,IAAmC6B,WAAW,CAACK,IADjC,IAEbH,QAAQ,CAAC7B,CAAT,GAAa,KAAKrD,SAAL,CAAeqD,CAA7B,IAAmC2B,WAAW,CAACM,IAFjC,IAGbJ,QAAQ,CAAC7B,CAAT,GAAa,KAAKrD,SAAL,CAAeqD,CAA7B,IAAmC2B,WAAW,CAACO,IAHnD,CAL2B,CAU3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAKC,UAAL,CAAgB9E,SAAhB;AACH;;AAEO8E,QAAAA,UAAU,CAACC,OAAD,EAAmB;AACjC;AACA,cAAIA,OAAJ,EAAa;AACT,gBAAI,CAAC,KAAKrF,WAAV,EACI,KAAKgC,IAAL,CAAUnD,UAAU,CAACyG,eAArB;AACP,WAHD,MAGO;AACH,gBAAI,KAAKtF,WAAT,EACI,KAAKgC,IAAL,CAAUnD,UAAU,CAAC0G,iBAArB;AACP;;AACD,eAAKvF,WAAL,GAAmB,KAAKC,UAAxB;AACA,eAAKA,UAAL,GAAkBoF,OAAlB;AACH;AAED;AACJ;AACA;;;AACWG,QAAAA,SAAS,CAAC9F,MAAD,EAAiC;AAC7C,eAAKA,MAAL,GAAcA,MAAM,GAAGA,MAAM,CAACgB,IAAV,GAAiB,IAArC;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACW+E,QAAAA,WAAW,CAACC,QAAD,EAA6B;AAC3C,eAAKxG,gBAAL,GAAwBwG,QAAxB;AACA,iBAAO,IAAP;AACH;;AAEMC,QAAAA,UAAU,CAACC,OAAD,EAA4B;AACzC,eAAK1F,UAAL,GAAkB0F,OAAlB;;AAEA,cAAI,KAAK1F,UAAT,EAAqB;AACjB;AACA,iBAAKQ,IAAL,CAAUkC,WAAV,CAAsB,KAAK7C,aAA3B;AACH;;AAED,iBAAO,IAAP;AACH;;AA3PsD,O,UAiC/BwE,yB,GAA4B,C;;;;;iBA9BP3F,oBAAoB,CAACiH,E", "sourcesContent": ["import { _decorator, Component, Enum, misc, Node, UITransform, Vec2, Vec3 } from 'cc';\r\nimport { BulletSystem } from '../bullet/BulletSystem';\r\nimport { IMovable } from './IMovable';\r\nimport Entity from '../ui/base/Entity';\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\nexport enum eSpriteDefaultFacing {\r\n    Right = 0,    // →\r\n    Up = -90,     // ↑\r\n    Down = 90,    // ↓\r\n    Left = 180    // ←\r\n}\r\n\r\nexport enum eMoveEvent {\r\n    onBecomeVisible,\r\n    onBecomeInvisible,\r\n}\r\n\r\n@ccclass('Movable')\r\n@executeInEditMode\r\nexport class Movable extends Component implements IMovable {\r\n\r\n    @property({ type: Enum(eSpriteDefaultFacing), displayName: '图片默认朝向' })\r\n    public defaultFacing: eSpriteDefaultFacing = eSpriteDefaultFacing.Up;\r\n\r\n    public isFacingMoveDir: boolean = false;      // 是否朝向行进方向\r\n    public isTrackingTarget: boolean = false;     // 是否正在追踪目标\r\n    public speed: number = 1;                     // 速度\r\n    public speedAngle: number = 0;                // 速度方向 (用角度表示)\r\n    public turnSpeed: number = 60;                // 转向速度（仅用在追踪目标时）\r\n    public acceleration: number = 0;              // 加速度\r\n    public accelerationAngle: number = 0;         // 加速度方向 (用角度表示)\r\n\r\n    // @property({displayName: '振荡偏移速度', tooltip: '控制倾斜振荡的频率'})\r\n    public tiltSpeed: number = 0;                 // 偏移速度\r\n    // @property({displayName: '振荡偏移幅度', tooltip: '控制倾斜振荡的幅度'})\r\n    public tiltOffset: number = 100;               // 偏移距离\r\n\r\n    public target: Node | null = null;            // 追踪的目标节点\r\n    public arrivalDistance: number = 10;          // 到达目标的距离\r\n\r\n    private _selfSize: Vec2 = new Vec2();\r\n    private _position: Vec3 = new Vec3();\r\n    private _tiltTime: number = 0;                // 用于计算倾斜偏移的累积时间\r\n    private _basePosition: Vec3 = new Vec3();     // 基础位置（不包含倾斜偏移）\r\n\r\n    private _wasVisible: boolean = false;\r\n    private _isVisible: boolean = false;           // 是否可见\r\n    public get isVisible() { return this._isVisible; }\r\n    private _isMovable: boolean = true;           // 是否可移动\r\n    public get isMovable() { return this._isMovable; }\r\n\r\n    private _visibilityCheckCounter: number = 0;  // 可见性检查计数器\r\n    private static readonly VISIBILITY_CHECK_INTERVAL = 5; // 每x帧检查一次可见性\r\n\r\n    // Event system:\r\n    private _eventListeners: Map<eMoveEvent, Array<() => void>> = new Map();\r\n\r\n    onLoad() {\r\n        const uiTransform = this.node.getComponent(UITransform);\r\n        const self_size = uiTransform ? uiTransform.contentSize : { width: 0, height: 0 };\r\n        this._selfSize.set(self_size.width / 2, self_size.height / 2);\r\n    }\r\n\r\n    onDestroy() {\r\n        // clear all event listeners\r\n        this._eventListeners.clear();\r\n    }\r\n\r\n    /**\r\n     * 添加事件监听器\r\n     * @param event 事件类型\r\n     * @param listener 监听器函数\r\n     */\r\n    public on(event: eMoveEvent, listener: () => void): void {\r\n        if (!this._eventListeners.has(event)) {\r\n            this._eventListeners.set(event, []);\r\n        }\r\n        const listeners = this._eventListeners.get(event)!;\r\n        if (!listeners.includes(listener)) {\r\n            listeners.push(listener);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移除事件监听器\r\n     * @param event 事件类型\r\n     * @param listener 监听器函数\r\n     */\r\n    public off(event: eMoveEvent, listener: () => void): void {\r\n        const listeners = this._eventListeners.get(event);\r\n        if (listeners) {\r\n            const index = listeners.indexOf(listener);\r\n            if (index !== -1) {\r\n                listeners.splice(index, 1);\r\n            }\r\n        }\r\n    }\r\n\r\n    public removeAllListeners(): void {\r\n        this._eventListeners.clear();\r\n    }\r\n\r\n    /**\r\n     * 触发事件\r\n     * @param event 事件类型\r\n     */\r\n    private emit(event: eMoveEvent): void {\r\n        const listeners = this._eventListeners.get(event);\r\n        if (listeners && listeners.length > 0) {\r\n            listeners.forEach(listener => listener());\r\n        }\r\n    }\r\n\r\n    public tick(dt: number): void {\r\n        if (!this._isMovable) return;\r\n\r\n        const angleRadians = degreesToRadians(this.speedAngle);\r\n        // Convert speed and angle to velocity vector\r\n        let velocityX = this.speed * Math.cos(angleRadians);\r\n        let velocityY = this.speed * Math.sin(angleRadians);\r\n\r\n        if (this.isTrackingTarget && this.target) {\r\n            const targetPos = this.target.getPosition();\r\n            const currentPos = this._basePosition;\r\n\r\n            // Calculate direction to target\r\n            const directionX = targetPos.x - currentPos.x;\r\n            const directionY = targetPos.y - currentPos.y;\r\n            const distance = Math.sqrt(directionX * directionX + directionY * directionY);\r\n\r\n            if (distance > 0) {\r\n                // Calculate desired angle to target\r\n                const desiredAngle = radiansToDegrees(Math.atan2(directionY, directionX));\r\n\r\n                // Smoothly adjust speedAngle toward target\r\n                const angleDiff = desiredAngle - this.speedAngle;\r\n                // Normalize angle difference to [-180, 180] range\r\n                const normalizedAngleDiff = ((angleDiff + 180) % 360) - 180;\r\n\r\n                // Apply tracking adjustment (you can add a trackingStrength property to control this)\r\n                const trackingStrength = 1.0; // Can be made configurable\r\n                const maxTurnRate = this.turnSpeed; // degrees per second - can be made configurable\r\n                const turnAmount = Math.min(Math.abs(normalizedAngleDiff), maxTurnRate * dt) * Math.sign(normalizedAngleDiff);\r\n\r\n                this.speedAngle += turnAmount * trackingStrength;\r\n                const angleRadiansAfterTracking = degreesToRadians(this.speedAngle);\r\n\r\n                // Recalculate velocity with new angle\r\n                velocityX = this.speed * Math.cos(angleRadiansAfterTracking);\r\n                velocityY = this.speed * Math.sin(angleRadiansAfterTracking);\r\n            }\r\n        }\r\n\r\n        // Convert acceleration and angle to acceleration vector\r\n        if (this.acceleration !== 0) {\r\n            const accelerationRadians = degreesToRadians(this.accelerationAngle);\r\n            const accelerationX = this.acceleration * Math.cos(accelerationRadians);\r\n            const accelerationY = this.acceleration * Math.sin(accelerationRadians);\r\n            // Update velocity vector: v = v + a * dt\r\n            velocityX += accelerationX * dt;\r\n            velocityY += accelerationY * dt;\r\n        }\r\n\r\n        // Convert back to speed and angle\r\n        this.speed = Math.sqrt(velocityX * velocityX + velocityY * velocityY);\r\n        this.speedAngle = radiansToDegrees(Math.atan2(velocityY, velocityX));\r\n\r\n        // Update position: p = p + v * dt\r\n        if (velocityX !== 0 || velocityY !== 0) {\r\n            // Update base position (main movement path)\r\n            this._basePosition.x += velocityX * dt;\r\n            this._basePosition.y += velocityY * dt;\r\n\r\n            // Start with base position\r\n            this._position.set(this._basePosition);\r\n\r\n            // Apply tilting behavior if enabled\r\n            if (this.tiltSpeed > 0 && this.tiltOffset > 0) {\r\n                // Update tilt time\r\n                this._tiltTime += dt;\r\n\r\n                // Calculate perpendicular direction to movement\r\n                // If moving in direction (cos(angle), sin(angle)), perpendicular is (-sin(angle), cos(angle))\r\n                const moveAngleRad = degreesToRadians(this.speedAngle);\r\n                const perpX = -Math.sin(moveAngleRad);\r\n                const perpY = Math.cos(moveAngleRad);\r\n\r\n                // Calculate tilt offset using sine wave\r\n                const tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset;\r\n\r\n                // Apply tilt offset in perpendicular direction (as position offset, not velocity)\r\n                this._position.x += perpX * tiltAmount;\r\n                this._position.y += perpY * tiltAmount;\r\n            }\r\n\r\n            this.node.setPosition(this._position);\r\n\r\n            // 降低可见性检查频率\r\n            if (++this._visibilityCheckCounter >= Movable.VISIBILITY_CHECK_INTERVAL) {\r\n                this._visibilityCheckCounter = 0;\r\n                this.checkVisibility();\r\n            }\r\n        }\r\n\r\n        if (this.isFacingMoveDir && this.speed > 0) {\r\n            const finalAngle = this.speedAngle + this.defaultFacing;\r\n            this.node.setRotationFromEuler(0, 0, finalAngle);\r\n        }\r\n    }\r\n\r\n    public checkVisibility(): void {\r\n        // 这里目前的检查逻辑没有考虑旋转和缩放\r\n        // 正常来说需要判定world corners，如果四个角有一个在屏幕内，就认为是可见的\r\n        const visibleSize = BulletSystem.worldBounds;\r\n        const position = this.node.worldPosition;\r\n        const isVisible = (position.x + this._selfSize.x) >= visibleSize.xMin &&\r\n            (position.x - this._selfSize.x) <= visibleSize.xMax &&\r\n            (position.y - this._selfSize.y) <= visibleSize.yMax &&\r\n            (position.y + this._selfSize.y) >= visibleSize.yMin;\r\n\r\n        // debug visibility\r\n        // if (!isVisible) {\r\n        //     console.log(\"Movable\", \"checkVisibility\", this.node.name + \" is not visible\");\r\n        //     console.log(\"Movable\", \"checkLeftBound  :\", (this._position.x - this._selfSize.x) <= visibleSize.xMax, (this._position.x - this._selfSize.x), \"<=\", visibleSize.xMax);\r\n        //     console.log(\"Movable\", \"checkRightBound :\", (this._position.x + this._selfSize.x) >= visibleSize.xMin, (this._position.x + this._selfSize.x), \">=\", visibleSize.xMin);\r\n        //     console.log(\"Movable\", \"checkTopBound   :\", (this._position.y + this._selfSize.y) <= visibleSize.yMax, (this._position.y + this._selfSize.y), \"<=\", visibleSize.yMax);\r\n        //     console.log(\"Movable\", \"checkBottomBound:\", (this._position.y - this._selfSize.y) >= visibleSize.yMin, (this._position.y - this._selfSize.y), \">=\", visibleSize.yMin);\r\n        // }\r\n\r\n        this.setVisible(isVisible);\r\n    }\r\n\r\n    private setVisible(visible: boolean) {\r\n        // console.log('setVisible: ', this._wasVisible, ', ', visible);\r\n        if (visible) {\r\n            if (!this._wasVisible)\r\n                this.emit(eMoveEvent.onBecomeVisible);\r\n        } else {\r\n            if (this._wasVisible)\r\n                this.emit(eMoveEvent.onBecomeInvisible);\r\n        }\r\n        this._wasVisible = this._isVisible;\r\n        this._isVisible = visible;\r\n    }\r\n\r\n    /**\r\n     * Set the target\r\n     */\r\n    public setTarget(target: Entity | null): Movable {\r\n        this.target = target ? target.node : null;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set whether to track the target\r\n     */\r\n    public setTracking(tracking: boolean): Movable {\r\n        this.isTrackingTarget = tracking;\r\n        return this;\r\n    }\r\n\r\n    public setMovable(movable: boolean): Movable {\r\n        this._isMovable = movable;\r\n\r\n        if (this._isMovable) {\r\n            // Initialize base position to current node position\r\n            this.node.getPosition(this._basePosition);\r\n        }\r\n\r\n        return this;\r\n    }\r\n}"]}