{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/base/Controller.ts"], "names": ["_decorator", "Component", "NodeEventType", "Vec3", "GameIns", "GameEnum", "ccclass", "property", "Controller", "target", "_targetStartPos", "onLoad", "start", "node", "on", "TOUCH_START", "onTouchStart", "TOUCH_END", "onTouchEnd", "TOUCH_CANCEL", "TOUCH_MOVE", "onTouchMove", "event", "mainPlaneManager", "mainPlane", "getPosition", "battleManager", "setTouchState", "gameStateManager", "gameState", "GameState", "Battle", "startPos", "getUIStartLocation", "location", "getUILocation", "posX", "x", "posY", "y", "onControl"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAsBC,MAAAA,a,OAAAA,a;AAAiCC,MAAAA,I,OAAAA,I;;AACnEC,MAAAA,O,iBAAAA,O;;AACDC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OAGF;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;4BAGjBQ,U,WADZF,OAAO,CAAC,YAAD,C,gBAAR,MACaE,UADb,SACgCP,SADhC,CAC0C;AAAA;AAAA;AAAA,eAEtCQ,MAFsC,GAEX,IAFW;AAEL;AAFK,eAGtCC,eAHsC,GAGd,IAAIP,IAAJ,CAAS,CAAT,EAAY,CAAZ,CAHc;AAAA;;AAGE;;AAExC;AACJ;AACA;AACIQ,QAAAA,MAAM,GAAG,CAER;AAED;AACJ;AACA;;;AACIC,QAAAA,KAAK,GAAG;AACJ,eAAKC,IAAL,CAAUC,EAAV,CAAaZ,aAAa,CAACa,WAA3B,EAAwC,KAAKC,YAA7C,EAA2D,IAA3D;AACA,eAAKH,IAAL,CAAUC,EAAV,CAAaZ,aAAa,CAACe,SAA3B,EAAsC,KAAKC,UAA3C,EAAuD,IAAvD;AACA,eAAKL,IAAL,CAAUC,EAAV,CAAaZ,aAAa,CAACiB,YAA3B,EAAyC,KAAKD,UAA9C,EAA0D,IAA1D;AACA,eAAKL,IAAL,CAAUC,EAAV,CAAaZ,aAAa,CAACkB,UAA3B,EAAuC,KAAKC,WAA5C,EAAyD,IAAzD;AACH;AAED;AACJ;AACA;AACA;;;AACIL,QAAAA,YAAY,CAACM,KAAD,EAAmB;AAC3B,cAAIb,MAAM,GAAG;AAAA;AAAA,kCAAQc,gBAAR,CAAyBC,SAAtC,CAD2B,CACsB;;AACjD,cAAI,CAACf,MAAL,EAAa;AACT,mBADS,CACD;AACX;;AACD,eAAKC,eAAL,GAAuBD,MAAM,CAACI,IAAP,CAAYY,WAAZ,EAAvB,CAL2B,CAKuB;;AAClD;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,aAAtB,CAAoC,IAApC;AACH;AAED;AACJ;AACA;AACA;;;AACIN,QAAAA,WAAW,CAACC,KAAD,EAAmB;AAC1B,cAAI;AAAA;AAAA,kCAAQM,gBAAR,CAAyBC,SAAzB,IAAsC;AAAA;AAAA,oCAASC,SAAT,CAAmBC,MAA7D,EAAqE;AACjE,mBADiE,CACzD;AACX;;AAED,cAAItB,MAAM,GAAG;AAAA;AAAA,kCAAQc,gBAAR,CAAyBC,SAAtC,CAL0B,CAKuB;;AACjD,cAAI,CAACf,MAAL,EAAa;AACT,mBADS,CACD;AACX;;AAED,cAAIuB,QAAQ,GAAGV,KAAK,CAACW,kBAAN,EAAf;AACA,cAAIC,QAAQ,GAAGZ,KAAK,CAACa,aAAN,EAAf,CAX0B,CAWc;;AAExC,cAAIC,IAAI,GAAGF,QAAQ,CAACG,CAAT,GAAaL,QAAQ,CAACK,CAAtB,GAA0B,KAAK3B,eAAL,CAAqB2B,CAA1D;AACA,cAAIC,IAAI,GAAGJ,QAAQ,CAACK,CAAT,GAAaP,QAAQ,CAACO,CAAtB,GAA0B,KAAK7B,eAAL,CAAqB6B,CAA1D;AACA9B,UAAAA,MAAM,CAAC+B,SAAP,CAAiBJ,IAAjB,EAAuBE,IAAvB,EAf0B,CAeI;AACjC;AAED;AACJ;AACA;AACA;;;AACIpB,QAAAA,UAAU,CAACI,KAAD,EAAmB;AACzB;AAAA;AAAA,kCAAQI,aAAR,CAAsBC,aAAtB,CAAoC,KAApC;AACH;;AA/DqC,O", "sourcesContent": ["import { _decorator, Component, Animation, NodeEventType, EventTouch, Vec2, Vec3 } from 'cc';\r\nimport { GameIns } from '../../GameIns';\r\nimport {GameEnum} from '../../const/GameEnum';\r\nimport { type MainPlane } from '../plane/mainPlane/MainPlane';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Controller')\r\nexport class Controller extends Component {\r\n\r\n    target: MainPlane | null = null; // 目标对象（主飞机）\r\n    _targetStartPos: Vec3 = new Vec3(0, 0); // 目标起始位置\r\n\r\n    /**\r\n     * 加载时初始化\r\n     */\r\n    onLoad() {\r\n        \r\n    }\r\n\r\n    /**\r\n     * 开始时绑定触摸事件\r\n     */\r\n    start() {\r\n        this.node.on(NodeEventType.TOUCH_START, this.onTouchStart, this);\r\n        this.node.on(NodeEventType.TOUCH_END, this.onTouchEnd, this);\r\n        this.node.on(NodeEventType.TOUCH_CANCEL, this.onTouchEnd, this);\r\n        this.node.on(NodeEventType.TOUCH_MOVE, this.onTouchMove, this);\r\n    }\r\n\r\n    /**\r\n     * 触摸开始事件\r\n     * @param {Event.EventTouch} event 触摸事件\r\n     */\r\n    onTouchStart(event:EventTouch) {\r\n        let target = GameIns.mainPlaneManager.mainPlane; // 获取主飞机\r\n        if (!target) {\r\n            return; // 如果主飞机不存在，则不处理\r\n        }\r\n        this._targetStartPos = target.node.getPosition(); // 记录主飞机的起始位置\r\n        GameIns.battleManager.setTouchState(true);\r\n    }\r\n\r\n    /**\r\n     * 触摸移动事件\r\n     * @param {Event.EventTouch} event 触摸事件\r\n     */\r\n    onTouchMove(event:EventTouch) {\r\n        if (GameIns.gameStateManager.gameState != GameEnum.GameState.Battle) {\r\n            return; // 游戏未进入战斗状态时不处理\r\n        }\r\n\r\n        let target = GameIns.mainPlaneManager.mainPlane; // 获取主飞机\r\n        if (!target) {\r\n            return; // 如果主飞机不存在，则不处理\r\n        }\r\n\r\n        let startPos = event.getUIStartLocation();\r\n        let location = event.getUILocation();   //得到手指鼠标位置,得到的是世界坐标\r\n\r\n        let posX = location.x - startPos.x + this._targetStartPos.x;\r\n        let posY = location.y - startPos.y + this._targetStartPos.y;\r\n        target.onControl(posX, posY); // 控制主飞机移动\r\n    }\r\n\r\n    /**\r\n     * 触摸结束事件\r\n     * @param {Event.EventTouch} event 触摸事件\r\n     */\r\n    onTouchEnd(event:EventTouch) {\r\n        GameIns.battleManager.setTouchState(false);\r\n    }\r\n}"]}