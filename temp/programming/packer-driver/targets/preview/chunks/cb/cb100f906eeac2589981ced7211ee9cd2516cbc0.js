System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Prefab, instantiate, MyApp, SingletonBase, GameResourceList, GameIns, BattleLayer, MainPlaneManager, _crd;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneData(extras) {
    _reporterNs.report("PlaneData", "db://assets/bundles/common/script/data/plane/PlaneData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../../../../scripts/core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameResourceList(extras) {
    _reporterNs.report("GameResourceList", "../const/GameResourceList", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleLayer(extras) {
    _reporterNs.report("BattleLayer", "../ui/layer/BattleLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainPlane(extras) {
    _reporterNs.report("MainPlane", "../ui/plane/mainPlane/MainPlane", _context.meta, extras);
  }

  _export("MainPlaneManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Prefab = _cc.Prefab;
      instantiate = _cc.instantiate;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      SingletonBase = _unresolved_3.SingletonBase;
    }, function (_unresolved_4) {
      GameResourceList = _unresolved_4.default;
    }, function (_unresolved_5) {
      GameIns = _unresolved_5.GameIns;
    }, function (_unresolved_6) {
      BattleLayer = _unresolved_6.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "33796Bg3oRMVJbIb/vss6Db", "MainPlaneManager", undefined);

      __checkObsolete__(['Prefab', 'instantiate']);

      _export("MainPlaneManager", MainPlaneManager = class MainPlaneManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor() {
          super(...arguments);
          this._planeData = null;
          //飞机数据
          this.mainPlane = null;
          //飞机战斗UI
          this.hurtTotal = 0;
          //造成的总伤害
          this.reviveCount = 0;
          //已复活次数
          this.maxReviveCount = 0;
        }

        //可复活次数
        setPlaneData(planeData) {
          this._planeData = planeData;
        }

        preload() {
          var _this = this;

          return _asyncToGenerator(function* () {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.addLoadCount(1);
            yield _this.createMainPlane();
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.checkLoadFinish();

            _this.reset();
          })();
        }

        reset() {
          this.hurtTotal = 0;
          this.reviveCount = 0;
          this.maxReviveCount = 1; // 默认可复活1次
        }
        /**
         * 创建主飞机
         * @param isTrans 是否为特殊状态
         * @returns 主飞机对象
         */


        createMainPlane() {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            var _me, _this2$mainPlane;

            if (_this2.mainPlane) {
              _this2.mainPlane.resetPlane();

              return _this2.mainPlane;
            }

            var prefab = yield (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.loadAsync((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
              error: Error()
            }), GameResourceList) : GameResourceList).MainPlane, Prefab);
            var planeNode = instantiate(prefab);
            _this2.mainPlane = planeNode.getComponent("MainPlane");
            (_me = (_crd && BattleLayer === void 0 ? (_reportPossibleCrUseOfBattleLayer({
              error: Error()
            }), BattleLayer) : BattleLayer).me) == null || _me.addMainPlane();
            (_this2$mainPlane = _this2.mainPlane) == null || _this2$mainPlane.initPlane(_this2._planeData);
            return _this2.mainPlane;
          })();
        }

        mainReset() {
          if (this.mainPlane) {
            this.mainPlane.node.destroy();
            this.mainPlane = null;
          }
        }

        checkCanRevive() {
          return this.reviveCount < this.maxReviveCount;
        }

        revive() {
          var _this$mainPlane;

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.reviveCount += 1; // 增加复活次数

          (_this$mainPlane = this.mainPlane) == null || _this$mainPlane.revive();
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=cb100f906eeac2589981ced7211ee9cd2516cbc0.js.map