{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/manager/MainPlaneManager.ts"], "names": ["MainPlaneManager", "Prefab", "instantiate", "MyApp", "SingletonBase", "GameResourceList", "GameIns", "BattleLayer", "_planeData", "mainPlane", "hurtTotal", "reviveCount", "maxReviveCount", "setPlaneData", "planeData", "preload", "battleManager", "addLoadCount", "createMainPlane", "checkLoadFinish", "reset", "reset<PERSON>lane", "prefab", "resMgr", "loadAsync", "MainPlane", "planeNode", "getComponent", "me", "addMainPlane", "initPlane", "mainReset", "node", "destroy", "checkCanRevive", "revive", "mainPlaneManager"], "mappings": ";;;kKAUaA,gB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AATJC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AACRC,MAAAA,K,iBAAAA,K;;AAEAC,MAAAA,a,iBAAAA,a;;AACFC,MAAAA,gB;;AACEC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,W;;;;;;;;;kCAGMP,gB,GAAN,MAAMA,gBAAN;AAAA;AAAA,0CAA+D;AAAA;AAAA;AAAA,eAElEQ,UAFkE,GAEnC,IAFmC;AAE9B;AAF8B,eAGlEC,SAHkE,GAGpC,IAHoC;AAG/B;AAH+B,eAKlEC,SALkE,GAK9C,CAL8C;AAK5C;AAL4C,eAMlEC,WANkE,GAM5C,CAN4C;AAM1C;AAN0C,eAOlEC,cAPkE,GAOzC,CAPyC;AAAA;;AAOvC;AAE3BC,QAAAA,YAAY,CAACC,SAAD,EAAuB;AAC/B,eAAKN,UAAL,GAAkBM,SAAlB;AACH;;AAEKC,QAAAA,OAAO,GAAG;AAAA;;AAAA;AACZ;AAAA;AAAA,oCAAQC,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA,kBAAM,KAAI,CAACC,eAAL,EAAN;AACA;AAAA;AAAA,oCAAQF,aAAR,CAAsBG,eAAtB;;AACA,YAAA,KAAI,CAACC,KAAL;AAJY;AAKf;;AAEDA,QAAAA,KAAK,GAAG;AACJ,eAAKV,SAAL,GAAiB,CAAjB;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKC,cAAL,GAAsB,CAAtB,CAHI,CAGqB;AAC5B;AAED;AACJ;AACA;AACA;AACA;;;AACUM,QAAAA,eAAe,GAA8B;AAAA;;AAAA;AAAA;;AAC/C,gBAAI,MAAI,CAACT,SAAT,EAAoB;AAChB,cAAA,MAAI,CAACA,SAAL,CAAeY,UAAf;;AACA,qBAAO,MAAI,CAACZ,SAAZ;AACH;;AAED,gBAAMa,MAAM,SAAS;AAAA;AAAA,gCAAMC,MAAN,CAAaC,SAAb,CAAuB;AAAA;AAAA,sDAAiBC,SAAxC,EAAmDxB,MAAnD,CAArB;AACA,gBAAIyB,SAAS,GAAGxB,WAAW,CAACoB,MAAD,CAA3B;AACA,YAAA,MAAI,CAACb,SAAL,GAAiBiB,SAAS,CAACC,YAAV,CAAuB,WAAvB,CAAjB;AACA;AAAA;AAAA,4CAAYC,EAAZ,iBAAgBC,YAAhB;AACA,gCAAA,MAAI,CAACpB,SAAL,8BAAgBqB,SAAhB,CAA0B,MAAI,CAACtB,UAA/B;AACA,mBAAO,MAAI,CAACC,SAAZ;AAX+C;AAYlD;;AAEDsB,QAAAA,SAAS,GAAG;AACR,cAAI,KAAKtB,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAeuB,IAAf,CAAoBC,OAApB;AACA,iBAAKxB,SAAL,GAAiB,IAAjB;AACH;AACJ;;AAEDyB,QAAAA,cAAc,GAAY;AACtB,iBAAO,KAAKvB,WAAL,GAAmB,KAAKC,cAA/B;AACH;;AAEDuB,QAAAA,MAAM,GAAG;AAAA;;AACL;AAAA;AAAA,kCAAQC,gBAAR,CAAyBzB,WAAzB,IAAwC,CAAxC,CADK,CACsC;;AAC3C,kCAAKF,SAAL,6BAAgB0B,MAAhB;AACH;;AA3DiE,O", "sourcesContent": ["\r\nimport { Prefab, instantiate } from \"cc\";\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { PlaneData } from \"db://assets/bundles/common/script/data/plane/PlaneData\";\r\nimport { SingletonBase } from \"../../../../../scripts/core/base/SingletonBase\";\r\nimport GameResourceList from \"../const/GameResourceList\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport BattleLayer from \"../ui/layer/BattleLayer\";\r\nimport { type MainPlane } from \"../ui/plane/mainPlane/MainPlane\";\r\n\r\nexport class MainPlaneManager extends SingletonBase<MainPlaneManager> {\r\n\r\n    _planeData: PlaneData | null = null;//飞机数据\r\n    mainPlane: MainPlane | null = null;//飞机战斗UI\r\n\r\n    hurtTotal: number = 0;//造成的总伤害\r\n    reviveCount: number = 0;//已复活次数\r\n    maxReviveCount: number = 0;//可复活次数\r\n\r\n    setPlaneData(planeData: PlaneData) {\r\n        this._planeData = planeData;\r\n    }\r\n\r\n    async preload() {\r\n        GameIns.battleManager.addLoadCount(1);\r\n        await this.createMainPlane();\r\n        GameIns.battleManager.checkLoadFinish();\r\n        this.reset();\r\n    }\r\n\r\n    reset() {\r\n        this.hurtTotal = 0;\r\n        this.reviveCount = 0;\r\n        this.maxReviveCount = 1; // 默认可复活1次\r\n    }\r\n\r\n    /**\r\n     * 创建主飞机\r\n     * @param isTrans 是否为特殊状态\r\n     * @returns 主飞机对象\r\n     */\r\n    async createMainPlane(): Promise<MainPlane | null> {\r\n        if (this.mainPlane) {\r\n            this.mainPlane.resetPlane();\r\n            return this.mainPlane;\r\n        }\r\n\r\n        const prefab = await MyApp.resMgr.loadAsync(GameResourceList.MainPlane, Prefab);\r\n        let planeNode = instantiate(prefab);\r\n        this.mainPlane = planeNode.getComponent(\"MainPlane\") as MainPlane\r\n        BattleLayer.me?.addMainPlane();\r\n        this.mainPlane?.initPlane(this._planeData!);\r\n        return this.mainPlane;\r\n    }\r\n\r\n    mainReset() {\r\n        if (this.mainPlane) {\r\n            this.mainPlane.node.destroy();\r\n            this.mainPlane = null;\r\n        }\r\n    }\r\n\r\n    checkCanRevive(): boolean {\r\n        return this.reviveCount < this.maxReviveCount;\r\n    }\r\n\r\n    revive() {\r\n        GameIns.mainPlaneManager.reviveCount += 1; // 增加复活次数\r\n        this.mainPlane?.revive();\r\n    }\r\n}\r\n"]}