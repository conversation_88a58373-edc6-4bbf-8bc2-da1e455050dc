{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/bullet/Emitter.ts"], "names": ["_decorator", "assetManager", "misc", "EDITOR", "MyApp", "BulletData", "EmitterData", "Bullet", "BulletProperty", "BulletSystem", "EventGroupContext", "ObjectPool", "PropertyContainerComponent", "ccclass", "executeInEditMode", "property", "disallowMultiple", "menu", "degreesToRadians", "radiansToDegrees", "eEmitterStatus", "eEmitterProp", "ePropMask", "Emitter", "displayName", "editor<PERSON><PERSON><PERSON>", "type", "onBulletCreatedCallback", "onEmitterStatusChangedCallback", "isActive", "isOnlyInScreen", "isPreWarm", "isLoop", "initialDelay", "preWarmDuration", "emitDuration", "emitInterval", "emitPower", "loopInterval", "perEmitCount", "perEmitInterval", "perEmitOffsetX", "angle", "count", "arc", "radius", "elapsedTime", "bulletProp", "eventGroups", "_emitterId", "_status", "None", "_statusElapsedTime", "_totalElapsedTime", "_isEmitting", "_nextEmitTime", "_bulletPrefab", "_prewarmEffectPrefab", "_emitEffectPrefab", "_entity", "_emitterConfig", "undefined", "_perEmitBulletQueue", "isEmitting", "status", "statusElapsedTime", "totalElapsedTime", "emitterId", "config", "id", "loadConfigByID", "onLoad", "createProperties", "createEventGroups", "resetProperties", "onLostFocusInEditor", "updatePropertiesInEditor", "emitterData", "value", "eval", "notifyAll", "setIsActive", "active", "notify", "reset", "changeStatus", "length", "for<PERSON>ach", "group", "setEntity", "entity", "getEntity", "clear", "addProperty", "IsActive", "ElapsedTime", "IsOnlyInScreen", "IsPreWarm", "IsLoop", "InitialDelay", "PrewarmDuration", "EmitDuration", "EmitInterval", "EmitPower", "LoopInterval", "PerEmitCount", "PerEmitInterval", "PerEmitOffsetX", "<PERSON><PERSON>", "Count", "Arc", "<PERSON><PERSON>", "on", "Prewarm", "onCreateEmitter", "onDestroyEmitter", "eventGroupData", "ctx", "emitter", "<PERSON><PERSON><PERSON>", "eventGroup", "createEmitterEventGroup", "resetFromData", "bulletData", "evalProperty", "prop", "canWrite", "ReEval", "isFixedValue", "oldStatus", "tryStop", "tryStart", "scheduleNextEmit", "startEmitting", "stopEmitting", "unscheduleAllCallbacks", "canEmit", "emit", "j", "targetTime", "i", "push", "index", "perEmitIndex", "emitSingle", "processPerEmitQueue", "nextBullet", "shift", "tryEmit", "direction", "getSpawnDirection", "position", "getSpawnPosition", "createBullet", "angleOffset", "radian", "x", "Math", "cos", "y", "sin", "getEmitOffsetX", "interval", "stepsFromMiddle", "floor", "ceil", "perpendicular", "prefab", "createBulletInEditor", "bullet", "instantiateBullet", "onCreateBullet", "emitterPos", "node", "getWorldPosition", "setWorldPosition", "z", "speedAngle", "atan2", "speed", "onReady", "prefabPath", "Editor", "Message", "request", "then", "uuid", "loadAny", "err", "console", "error", "bulletNode", "getNode", "bulletParent", "getComponent", "destroy", "name", "kBulletNameInEditor", "GetInstance", "lubanMgr", "lubanTables", "TbResEmitter", "get", "playEffect", "rotation", "duration", "effectNode", "setWorldRotation", "scheduleOnce", "returnNode", "isInScreen", "tick", "deltaTime", "updateStatusNone", "updateStatusPrewarm", "Emitting", "updateStatusEmitting", "Loop<PERSON>ndReached", "updateStatusLoopEndReached", "Completed", "updateStatusCompleted", "wasEmitting"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,I,OAAAA,I;;AAC1BC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,K,iBAAAA,K;;AAIAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,c,iBAAAA,c;;AACRC,MAAAA,Y,iBAAAA,Y;;AACYC,MAAAA,iB,iBAAAA,iB;;AACZC,MAAAA,U,iBAAAA,U;;AACUC,MAAAA,0B,iBAAAA,0B;;;;;;;;;AAInB;AACA;OAEM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA,gBAAxC;AAA0DC,QAAAA;AAA1D,O,GAAmEjB,U;OACnE;AAAEkB,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCjB,I;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;gCACYkB,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;cAIZ;;;8BACYC,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;;AAQZ;AACA;AACA;AACA;AACA;AACA;;;2BACYC,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;;;AAQZ;AACA;AACA;AACA;AACA;yBAMaC,O,WALZV,OAAO,CAAC,SAAD,C,UAEPI,IAAI,CAAC,UAAD,C,UACJH,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,UAKZD,QAAQ,CAAC;AAACS,QAAAA,WAAW,EAAE,IAAd;AAAoBC,QAAAA,UAAU,EAAE;AAAhC,OAAD,C,UAKRV,QAAQ,CAAC;AAAEW,QAAAA,IAAI;AAAA;AAAA,sCAAN;AAAqBF,QAAAA,WAAW,EAAE;AAAlC,OAAD,C,UAGRT,QAAQ,CAAC;AAAEW,QAAAA,IAAI;AAAA;AAAA,oCAAN;AAAoBF,QAAAA,WAAW,EAAE;AAAjC,OAAD,C,mFAjBb,MAKaD,OALb;AAAA;AAAA,oEAKsE;AAAA;AAAA;;AAAA;;AAKzB;AAEzC;AACA;AARkE;;AAAA;;AAelE;AAfkE,eAgBlEI,uBAhBkE,GAgBR,IAhBQ;AAAA,eAiBlEC,8BAjBkE,GAiBM,IAjBN;AAmBlE;AAnBkE,eAoB3DC,QApB2D;AAAA,eAqB3DC,cArB2D;AAAA,eAsB3DC,SAtB2D;AAAA,eAuB3DC,MAvB2D;AAAA,eAwB3DC,YAxB2D;AAAA,eAyB3DC,eAzB2D;AAAA,eA0B3DC,YA1B2D;AAAA,eA2B3DC,YA3B2D;AAAA,eA4B3DC,SA5B2D;AAAA,eA6B3DC,YA7B2D;AAAA,eA8B3DC,YA9B2D;AAAA,eA+B3DC,eA/B2D;AAAA,eAgC3DC,cAhC2D;AAAA,eAiC3DC,KAjC2D;AAAA,eAkC3DC,KAlC2D;AAAA,eAmC3DC,GAnC2D;AAAA,eAoC3DC,MApC2D;AAAA,eAqC3DC,WArC2D;AAsClE;AAtCkE,eAuC3DC,UAvC2D;AAyClE;AAzCkE,eA0C3DC,WA1C2D,GA0C/B,EA1C+B;AA4ClE;AA5CkE,eA6CxDC,UA7CwD,GA6CnC,CA7CmC;AAAA,eA8CxDC,OA9CwD,GA8C9B9B,cAAc,CAAC+B,IA9Ce;AAAA,eA+CxDC,kBA/CwD,GA+C3B,CA/C2B;AAAA,eAgDxDC,iBAhDwD,GAgD5B,CAhD4B;AAAA,eAiDxDC,WAjDwD,GAiDjC,KAjDiC;AAAA,eAkDxDC,aAlDwD,GAkDhC,CAlDgC;AAAA,eAoDxDC,aApDwD,GAoDzB,IApDyB;AAAA,eAqDxDC,oBArDwD,GAqDlB,IArDkB;AAAA,eAsDxDC,iBAtDwD,GAsDrB,IAtDqB;AAAA,eAuDxDC,OAvDwD,GAuD5B,IAvD4B;AAAA,eAwDxDC,cAxDwD,GAwDfC,SAxDe;AA0DlE;AA1DkE,eA2DxDC,mBA3DwD,GA2DkC,EA3DlC;AAAA;;AA6DpD,YAAVC,UAAU,GAAY;AAAE,iBAAO,KAAKT,WAAZ;AAA0B;;AAC5C,YAANU,MAAM,GAAmB;AAAE,iBAAO,KAAKd,OAAZ;AAAsB;;AAChC,YAAjBe,iBAAiB,GAAW;AAAE,iBAAO,KAAKb,kBAAZ;AAAiC;;AAC/C,YAAhBc,gBAAgB,GAAW;AAAE,iBAAO,KAAKb,iBAAZ;AAAgC;;AACpD,YAATc,SAAS,GAAW;AAAE,iBAAO,KAAKlB,UAAZ;AAAyB;;AACzC,YAANmB,MAAM,GAA2B;AAAE,iBAAO,KAAKR,cAAZ;AAA6B;;AACvD,YAATO,SAAS,CAACE,EAAD,EAAa;AACtB,eAAKpB,UAAL,GAAkBoB,EAAlB;AACA,eAAKC,cAAL,CAAoBD,EAApB;AACH;;AAESE,QAAAA,MAAM,GAAS;AACrB,eAAKC,gBAAL;AACA,eAAKC,iBAAL,GAFqB,CAIrB;;AACA,eAAKC,eAAL;AACH,SA9EiE,CAgFlE;;;AACOC,QAAAA,mBAAmB,GAAS;AAC/B,eAAKC,wBAAL;AACA,eAAKH,iBAAL;AACH;;AAEMG,QAAAA,wBAAwB,GAAG;AAC9B,cAAI,CAAC,KAAKC,WAAV,EAAuB;AAEvB,eAAKhD,QAAL,CAAciD,KAAd,GAAsB,IAAtB;AACA,eAAKhD,cAAL,CAAoBgD,KAApB,GAA4B,KAAKD,WAAL,CAAiB/C,cAA7C;AACA,eAAKC,SAAL,CAAe+C,KAAf,GAAuB,KAAKD,WAAL,CAAiB9C,SAAxC;AACA,eAAKC,MAAL,CAAY8C,KAAZ,GAAoB,KAAKD,WAAL,CAAiB7C,MAArC;AAEA,eAAKC,YAAL,CAAkB6C,KAAlB,GAA0B,KAAKD,WAAL,CAAiB5C,YAAjB,CAA8B8C,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAK7C,eAAL,CAAqB4C,KAArB,GAA6B,KAAKD,WAAL,CAAiB3C,eAAjB,CAAiC6C,IAAjC,CAAsC,IAAtC,EAA4C,IAA5C,CAA7B;AACA,eAAK5C,YAAL,CAAkB2C,KAAlB,GAA0B,KAAKD,WAAL,CAAiB1C,YAAjB,CAA8B4C,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAK3C,YAAL,CAAkB0C,KAAlB,GAA0B,KAAKD,WAAL,CAAiBzC,YAAjB,CAA8B2C,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAK1C,SAAL,CAAeyC,KAAf,GAAuB,KAAKD,WAAL,CAAiBxC,SAAjB,CAA2B0C,IAA3B,CAAgC,IAAhC,EAAsC,IAAtC,CAAvB;AACA,eAAKzC,YAAL,CAAkBwC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBvC,YAAjB,CAA8ByC,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAKxC,YAAL,CAAkBuC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBtC,YAAjB,CAA8BwC,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAKvC,eAAL,CAAqBsC,KAArB,GAA6B,KAAKD,WAAL,CAAiBrC,eAAjB,CAAiCuC,IAAjC,CAAsC,IAAtC,EAA4C,IAA5C,CAA7B;AACA,eAAKtC,cAAL,CAAoBqC,KAApB,GAA4B,KAAKD,WAAL,CAAiBpC,cAAjB,CAAgCsC,IAAhC,CAAqC,IAArC,EAA2C,IAA3C,CAA5B;AACA,eAAKrC,KAAL,CAAWoC,KAAX,GAAmB,KAAKD,WAAL,CAAiBnC,KAAjB,CAAuBqC,IAAvB,CAA4B,IAA5B,EAAkC,IAAlC,CAAnB;AACA,eAAKpC,KAAL,CAAWmC,KAAX,GAAmB,KAAKD,WAAL,CAAiBlC,KAAjB,CAAuBoC,IAAvB,CAA4B,IAA5B,EAAkC,IAAlC,CAAnB;AACA,eAAKnC,GAAL,CAASkC,KAAT,GAAiB,KAAKD,WAAL,CAAiBjC,GAAjB,CAAqBmC,IAArB,CAA0B,IAA1B,EAAgC,IAAhC,CAAjB;AACA,eAAKlC,MAAL,CAAYiC,KAAZ,GAAoB,KAAKD,WAAL,CAAiBhC,MAAjB,CAAwBkC,IAAxB,CAA6B,IAA7B,EAAmC,IAAnC,CAApB;AAEA,eAAKC,SAAL,CAAe,IAAf;AACH,SA7GiE,CA8GlE;AAEA;;;AACOC,QAAAA,WAAW,CAACC,MAAD,EAAkB;AAChC,eAAKrD,QAAL,CAAciD,KAAd,GAAsBI,MAAtB;AACA,eAAKrD,QAAL,CAAcsD,MAAd;AACH,SApHiE,CAsHlE;;;AACOC,QAAAA,KAAK,GAAG;AACX,eAAK9B,WAAL,GAAmB,KAAnB;AACA,eAAK+B,YAAL,CAAkBjE,cAAc,CAAC+B,IAAjC;AACA,eAAKuB,eAAL;;AACA,cAAI,KAAK1B,WAAL,CAAiBsC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,iBAAKtC,WAAL,CAAiBuC,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAACJ,KAAN,EAAlC;AACH;AACJ;;AAEMK,QAAAA,SAAS,CAACC,MAAD,EAAoB;AAChC,eAAK/B,OAAL,GAAe+B,MAAf;AACH;;AAEMC,QAAAA,SAAS,GAAqB;AACjC,iBAAO,KAAKhC,OAAZ;AACH;;AAESa,QAAAA,gBAAgB,GAAG;AACzB,eAAKoB,KAAL;AAEA,eAAK/D,QAAL,GAAgB,KAAKgE,WAAL,CAAiBxE,YAAY,CAACyE,QAA9B,EAAwC,KAAxC,CAAhB;AACA,eAAKhD,WAAL,GAAmB,KAAK+C,WAAL,CAAiBxE,YAAY,CAAC0E,WAA9B,EAA2C,CAA3C,CAAnB;AACA,eAAKjE,cAAL,GAAsB,KAAK+D,WAAL,CAAiBxE,YAAY,CAAC2E,cAA9B,EAA8C,IAA9C,CAAtB;AACA,eAAKjE,SAAL,GAAiB,KAAK8D,WAAL,CAAiBxE,YAAY,CAAC4E,SAA9B,EAAyC,IAAzC,CAAjB;AACA,eAAKjE,MAAL,GAAc,KAAK6D,WAAL,CAAiBxE,YAAY,CAAC6E,MAA9B,EAAsC,IAAtC,CAAd;AAEA,eAAKjE,YAAL,GAAoB,KAAK4D,WAAL,CAAiBxE,YAAY,CAAC8E,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKjE,eAAL,GAAuB,KAAK2D,WAAL,CAAiBxE,YAAY,CAAC+E,eAA9B,EAA+C,CAA/C,CAAvB;AACA,eAAKjE,YAAL,GAAoB,KAAK0D,WAAL,CAAiBxE,YAAY,CAACgF,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKjE,YAAL,GAAoB,KAAKyD,WAAL,CAAiBxE,YAAY,CAACiF,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKjE,SAAL,GAAiB,KAAKwD,WAAL,CAAiBxE,YAAY,CAACkF,SAA9B,EAAyC,CAAzC,CAAjB;AACA,eAAKjE,YAAL,GAAoB,KAAKuD,WAAL,CAAiBxE,YAAY,CAACmF,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKjE,YAAL,GAAoB,KAAKsD,WAAL,CAAiBxE,YAAY,CAACoF,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKjE,eAAL,GAAuB,KAAKqD,WAAL,CAAiBxE,YAAY,CAACqF,eAA9B,EAA+C,CAA/C,CAAvB;AACA,eAAKjE,cAAL,GAAsB,KAAKoD,WAAL,CAAiBxE,YAAY,CAACsF,cAA9B,EAA8C,CAA9C,CAAtB;AACA,eAAKjE,KAAL,GAAa,KAAKmD,WAAL,CAAiBxE,YAAY,CAACuF,KAA9B,EAAqC,CAArC,CAAb;AACA,eAAKjE,KAAL,GAAa,KAAKkD,WAAL,CAAiBxE,YAAY,CAACwF,KAA9B,EAAqC,CAArC,CAAb;AACA,eAAKjE,GAAL,GAAW,KAAKiD,WAAL,CAAiBxE,YAAY,CAACyF,GAA9B,EAAmC,CAAnC,CAAX;AACA,eAAKjE,MAAL,GAAc,KAAKgD,WAAL,CAAiBxE,YAAY,CAAC0F,MAA9B,EAAsC,CAAtC,CAAd,CArByB,CAuBzB;;AACA,eAAKhE,UAAL,GAAkB;AAAA;AAAA,iDAAlB;AAEA,eAAKlB,QAAL,CAAcmF,EAAd,CAAkBlC,KAAD,IAAW;AACxB,gBAAIA,KAAJ,EAAW;AACP,mBAAKO,YAAL,CAAkBjE,cAAc,CAAC6F,OAAjC;AACA;AAAA;AAAA,gDAAaC,eAAb,CAA6B,IAA7B;AACH,aAHD,MAGO;AACH;AAAA;AAAA,gDAAaC,gBAAb,CAA8B,IAA9B;AACH;AACJ,WAPD;AAQH;;AAES1C,QAAAA,iBAAiB,GAAG;AAC1B,cAAI,CAAC,KAAKI,WAAN,IAAqB,KAAKA,WAAL,CAAiBuC,cAAjB,CAAgC9B,MAAhC,IAA0C,CAAnE,EAAsE;AAEtE,eAAKtC,WAAL,GAAmB,EAAnB;AACA,cAAIqE,GAAG,GAAG;AAAA;AAAA,uDAAV;AACAA,UAAAA,GAAG,CAACC,OAAJ,GAAc,IAAd;AACAD,UAAAA,GAAG,CAACE,WAAJ,GAAkB;AAAA;AAAA,4CAAaA,WAA/B;;AACA,eAAK,MAAMC,UAAX,IAAyB,KAAK3C,WAAL,CAAiBuC,cAA1C,EAA0D;AACtD;AAAA;AAAA,8CAAaK,uBAAb,CAAqCJ,GAArC,EAA0CG,UAA1C;AACH;AACJ,SAtLiE,CAwLlE;;;AACU9C,QAAAA,eAAe,GAAG;AACxB,cAAI,CAAC,KAAKG,WAAV,EAAuB;AAEvB,eAAKhD,QAAL,CAAciD,KAAd,GAAsB,KAAtB;AACA,eAAKhC,WAAL,CAAiBgC,KAAjB,GAAyB,CAAzB;AACA,eAAKhD,cAAL,CAAoBgD,KAApB,GAA4B,KAAKD,WAAL,CAAiB/C,cAA7C;AACA,eAAKC,SAAL,CAAe+C,KAAf,GAAuB,KAAKD,WAAL,CAAiB9C,SAAxC;AACA,eAAKC,MAAL,CAAY8C,KAAZ,GAAoB,KAAKD,WAAL,CAAiB7C,MAArC;AAEA,eAAKC,YAAL,CAAkB6C,KAAlB,GAA0B,KAAKD,WAAL,CAAiB5C,YAAjB,CAA8B8C,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAK7C,eAAL,CAAqB4C,KAArB,GAA6B,KAAKD,WAAL,CAAiB3C,eAAjB,CAAiC6C,IAAjC,CAAsC,IAAtC,EAA4C,IAA5C,CAA7B;AACA,eAAK5C,YAAL,CAAkB2C,KAAlB,GAA0B,KAAKD,WAAL,CAAiB1C,YAAjB,CAA8B4C,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAK3C,YAAL,CAAkB0C,KAAlB,GAA0B,KAAKD,WAAL,CAAiBzC,YAAjB,CAA8B2C,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAK1C,SAAL,CAAeyC,KAAf,GAAuB,KAAKD,WAAL,CAAiBxC,SAAjB,CAA2B0C,IAA3B,CAAgC,IAAhC,EAAsC,IAAtC,CAAvB;AACA,eAAKzC,YAAL,CAAkBwC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBvC,YAAjB,CAA8ByC,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAKxC,YAAL,CAAkBuC,KAAlB,GAA0B,KAAKD,WAAL,CAAiBtC,YAAjB,CAA8BwC,IAA9B,CAAmC,IAAnC,EAAyC,IAAzC,CAA1B;AACA,eAAKvC,eAAL,CAAqBsC,KAArB,GAA6B,KAAKD,WAAL,CAAiBrC,eAAjB,CAAiCuC,IAAjC,CAAsC,IAAtC,EAA4C,IAA5C,CAA7B;AACA,eAAKtC,cAAL,CAAoBqC,KAApB,GAA4B,KAAKD,WAAL,CAAiBpC,cAAjB,CAAgCsC,IAAhC,CAAqC,IAArC,EAA2C,IAA3C,CAA5B;AACA,eAAKrC,KAAL,CAAWoC,KAAX,GAAmB,KAAKD,WAAL,CAAiBnC,KAAjB,CAAuBqC,IAAvB,CAA4B,IAA5B,EAAkC,IAAlC,CAAnB;AACA,eAAKpC,KAAL,CAAWmC,KAAX,GAAmB,KAAKD,WAAL,CAAiBlC,KAAjB,CAAuBoC,IAAvB,CAA4B,IAA5B,EAAkC,IAAlC,CAAnB;AACA,eAAKnC,GAAL,CAASkC,KAAT,GAAiB,KAAKD,WAAL,CAAiBjC,GAAjB,CAAqBmC,IAArB,CAA0B,IAA1B,EAAgC,IAAhC,CAAjB;AACA,eAAKlC,MAAL,CAAYiC,KAAZ,GAAoB,KAAKD,WAAL,CAAiBhC,MAAjB,CAAwBkC,IAAxB,CAA6B,IAA7B,EAAmC,IAAnC,CAApB;AAEA,eAAKhC,UAAL,CAAgB2E,aAAhB,CAA8B,KAAKC,UAAnC;AAEA,eAAK3C,SAAL,CAAe,IAAf;AACH;;AAES4C,QAAAA,YAAY,CAACC,IAAD,EAAyB/C,KAAzB,EAAiD;AACnE;AACA;AACA,cAAI+C,IAAI,CAACC,QAAL,CAAcxG,SAAS,CAACyG,MAAxB,KAAmC,CAACjD,KAAK,CAACkD,YAA9C,EAA4D;AACxDH,YAAAA,IAAI,CAAC/C,KAAL,GAAaA,KAAK,CAACC,IAAN,EAAb;AACH;AACJ;AAED;AACJ;AACA;;;AACIM,QAAAA,YAAY,CAACrB,MAAD,EAAyB;AACjC,cAAI,KAAKd,OAAL,KAAiBc,MAArB,EAA6B;AAE7B,gBAAMiE,SAAS,GAAG,KAAK/E,OAAvB;AACA,eAAKA,OAAL,GAAec,MAAf;AACA,eAAKZ,kBAAL,GAA0B,CAA1B;AACA,eAAKG,aAAL,GAAqB,CAArB,CANiC,CAOjC;;AACA,eAAKO,mBAAL,GAA2B,EAA3B;;AAEA,cAAIE,MAAM,KAAK5C,cAAc,CAAC6F,OAA9B,EAAuC;AACnC,iBAAKnE,WAAL,CAAiBgC,KAAjB,GAAyB,CAAzB,CADmC,CAEnC;;AACA,iBAAKD,WAAL,CAAiBzC,YAAjB,CAA8BgD,KAA9B;AACH;;AAED,cAAIpB,MAAM,KAAK5C,cAAc,CAAC+B,IAA9B,EAAoC;AAChC,gBAAI,KAAKH,WAAL,CAAiBsC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,mBAAKtC,WAAL,CAAiBuC,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAAC0C,OAAN,EAAlC;AACH;AACJ,WAJD,MAKK;AACD;AACA,gBAAI,KAAKlF,WAAL,CAAiBsC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,mBAAKtC,WAAL,CAAiBuC,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAAC2C,QAAN,EAAlC;AACH;AACJ;;AAED,cAAI,KAAKvG,8BAAL,IAAuC,IAA3C,EAAiD;AAC7C,iBAAKA,8BAAL,CAAoC,IAApC,EAA0CqG,SAA1C,EAAqDjE,MAArD;AACH;AACJ;;AAESoE,QAAAA,gBAAgB,GAAG;AACzB;AACA,eAAKR,YAAL,CAAkB,KAAKxF,YAAvB,EAAqC,KAAKyC,WAAL,CAAiBzC,YAAtD,EAFyB,CAIzB;;AACA,eAAKmB,aAAL,GAAqB,KAAKH,kBAAL,GAA0B,KAAKhB,YAAL,CAAkB0C,KAAjE,CALyB,CAMzB;AACH;;AAESuD,QAAAA,aAAa,GAAG;AACtB,eAAK/E,WAAL,GAAmB,IAAnB,CADsB,CAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;;AAESgF,QAAAA,YAAY,GAAG;AACrB,eAAKhF,WAAL,GAAmB,KAAnB,CADqB,CAErB;;AACA,eAAKQ,mBAAL,GAA2B,EAA3B;AACA,eAAKyE,sBAAL;AACH;;AAESC,QAAAA,OAAO,GAAY;AACzB;AACA;AACA,iBAAO,IAAP;AACH;;AAESC,QAAAA,IAAI,GAAS;AACnB;AACA,eAAKb,YAAL,CAAkB,KAAKjF,KAAvB,EAA8B,KAAKkC,WAAL,CAAiBlC,KAA/C;AACA,eAAKiF,YAAL,CAAkB,KAAKhF,GAAvB,EAA4B,KAAKiC,WAAL,CAAiBjC,GAA7C;AACA,eAAKgF,YAAL,CAAkB,KAAK/E,MAAvB,EAA+B,KAAKgC,WAAL,CAAiBhC,MAAhD;AACA,eAAK+E,YAAL,CAAkB,KAAKrF,YAAvB,EAAqC,KAAKsC,WAAL,CAAiBtC,YAAtD;;AAEA,cAAI,KAAKC,eAAL,CAAqBsC,KAArB,GAA6B,CAAjC,EAAoC;AAChC;AACA,iBAAK,IAAI4D,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKnG,YAAL,CAAkBuC,KAAtC,EAA6C4D,CAAC,EAA9C,EAAkD;AAC9C,mBAAKd,YAAL,CAAkB,KAAKpF,eAAvB,EAAwC,KAAKqC,WAAL,CAAiBrC,eAAzD;AACA,oBAAMmG,UAAU,GAAG,KAAKvF,kBAAL,GAA2B,KAAKZ,eAAL,CAAqBsC,KAArB,GAA6B4D,CAA3E;;AACA,mBAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKjG,KAAL,CAAWmC,KAA/B,EAAsC8D,CAAC,EAAvC,EAA2C;AACvC,qBAAK9E,mBAAL,CAAyB+E,IAAzB,CAA8B;AAC1BC,kBAAAA,KAAK,EAAEF,CADmB;AAE1BG,kBAAAA,YAAY,EAAEL,CAFY;AAG1BC,kBAAAA,UAAU,EAAEA;AAHc,iBAA9B;AAKH;AACJ;AACJ,WAbD,MAcK;AACD;AACA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKjG,KAAL,CAAWmC,KAA/B,EAAsC8D,CAAC,EAAvC,EAA2C;AACvC,mBAAK,IAAIF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKnG,YAAL,CAAkBuC,KAAtC,EAA6C4D,CAAC,EAA9C,EAAkD;AAC9C,qBAAKM,UAAL,CAAgBJ,CAAhB,EAAmBF,CAAnB;AACH;AACJ;AACJ;AACJ;;AAESO,QAAAA,mBAAmB,GAAS;AAClC;AACA,iBAAO,KAAKnF,mBAAL,CAAyBwB,MAAzB,GAAkC,CAAzC,EAA4C;AACxC,kBAAM4D,UAAU,GAAG,KAAKpF,mBAAL,CAAyB,CAAzB,CAAnB,CADwC,CAGxC;;AACA,gBAAI,KAAKV,kBAAL,IAA2B8F,UAAU,CAACP,UAA1C,EAAsD;AAClD;AACA,mBAAK7E,mBAAL,CAAyBqF,KAAzB;;AACA,mBAAKH,UAAL,CAAgBE,UAAU,CAACJ,KAA3B,EAAkCI,UAAU,CAACH,YAA7C;AACH,aAJD,MAIO;AACH;AACA;AACH;AACJ;AACJ;;AAESK,QAAAA,OAAO,GAAY;AACzB,cAAI,KAAKZ,OAAL,EAAJ,EAAoB;AAChB,iBAAKC,IAAL;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAESO,QAAAA,UAAU,CAACF,KAAD,EAAgBC,YAAhB,EAAsC;AACtD,gBAAMM,SAAS,GAAG,KAAKC,iBAAL,CAAuBR,KAAvB,CAAlB;AACA,gBAAMS,QAAQ,GAAG,KAAKC,gBAAL,CAAsBV,KAAtB,EAA6BC,YAA7B,CAAjB;AACA,eAAKU,YAAL,CAAkBJ,SAAlB,EAA6BE,QAA7B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACID,QAAAA,iBAAiB,CAACR,KAAD,EAA0C;AACvD;AACA,eAAKlB,YAAL,CAAkB,KAAKlF,KAAvB,EAA8B,KAAKmC,WAAL,CAAiBnC,KAA/C,EAFuD,CAGvD;;AACA,gBAAMgH,WAAW,GAAG,KAAK/G,KAAL,CAAWmC,KAAX,GAAmB,CAAnB,GAAwB,KAAKlC,GAAL,CAASkC,KAAT,IAAkB,KAAKnC,KAAL,CAAWmC,KAAX,GAAmB,CAArC,CAAD,GAA4CgE,KAA5C,GAAoD,KAAKlG,GAAL,CAASkC,KAAT,GAAiB,CAA5F,GAAgG,CAApH;AACA,gBAAM6E,MAAM,GAAGzI,gBAAgB,CAAC,KAAKwB,KAAL,CAAWoC,KAAX,GAAmB4E,WAApB,CAA/B;AAEA,iBAAO;AACHE,YAAAA,CAAC,EAAEC,IAAI,CAACC,GAAL,CAASH,MAAT,CADA;AAEHI,YAAAA,CAAC,EAAEF,IAAI,CAACG,GAAL,CAASL,MAAT;AAFA,WAAP;AAIH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIH,QAAAA,gBAAgB,CAACV,KAAD,EAAgBC,YAAhB,EAAgE;AAC5E;AACA;AACA,gBAAMkB,cAAc,GAAG,CAAClB,YAAD,EAAuBxG,YAAvB,EAA6CE,cAA7C,KAAwE;AAC3F,gBAAIF,YAAY,IAAI,CAAhB,IAAqBE,cAAc,KAAK,CAA5C,EAA+C,OAAO,CAAP;AAC/C,kBAAMyH,QAAQ,GAAGzH,cAAc,IAAIF,YAAY,GAAG,CAAnB,CAA/B,CAF2F,CAG3F;;AAEA,gBAAIA,YAAY,GAAG,CAAf,KAAqB,CAAzB,EAA4B;AACxB;AACA,kBAAIwG,YAAY,KAAK,CAArB,EAAwB,OAAO,CAAP;;AACxB,kBAAIA,YAAY,GAAG,CAAf,KAAqB,CAAzB,EAA4B;AACxB;AACA,sBAAMoB,eAAe,GAAGN,IAAI,CAACO,KAAL,CAAWrB,YAAY,GAAG,CAA1B,CAAxB;AACA,uBAAO,CAACoB,eAAD,GAAmBD,QAA1B;AACH,eAJD,MAKK;AACD;AACA,sBAAMC,eAAe,GAAGN,IAAI,CAACQ,IAAL,CAAUtB,YAAY,GAAG,CAAzB,CAAxB;AACA,uBAAOoB,eAAe,GAAGD,QAAzB;AACH;AACJ,aAbD,MAaO;AACH;AACA,kBAAInB,YAAY,KAAK,CAArB,EAAwB,OAAO,CAACmB,QAAD,GAAY,CAAnB;;AACxB,kBAAInB,YAAY,GAAG,CAAf,KAAqB,CAAzB,EAA4B;AACxB;AACA,sBAAMoB,eAAe,GAAGN,IAAI,CAACO,KAAL,CAAWrB,YAAY,GAAG,CAA1B,CAAxB;AACA,uBAAO,CAACmB,QAAD,GAAY,CAAZ,GAAgBC,eAAe,GAAGD,QAAzC;AACH,eAJD,MAKK;AACD;AACA,sBAAMC,eAAe,GAAGN,IAAI,CAACO,KAAL,CAAWrB,YAAY,GAAG,CAA1B,CAAxB;AACA,uBAAOmB,QAAQ,GAAG,CAAX,GAAeC,eAAe,GAAGD,QAAxC;AACH;AACJ;AACJ,WAhCD;;AAkCA,eAAKtC,YAAL,CAAkB,KAAKnF,cAAvB,EAAuC,KAAKoC,WAAL,CAAiBpC,cAAxD;AACA,gBAAMA,cAAc,GAAGwH,cAAc,CAAClB,YAAD,EAAe,KAAKxG,YAAL,CAAkBuC,KAAjC,EAAwC,KAAKrC,cAAL,CAAoBqC,KAA5D,CAArC;;AACA,cAAI,KAAKjC,MAAL,CAAYiC,KAAZ,IAAqB,CAAzB,EAA4B;AACxB,mBAAO;AAAE8E,cAAAA,CAAC,EAAEnH,cAAL;AAAqBsH,cAAAA,CAAC,EAAE;AAAxB,aAAP;AACH;;AAED,gBAAMV,SAAS,GAAG,KAAKC,iBAAL,CAAuBR,KAAvB,CAAlB,CA3C4E,CA4C5E;;AACA,gBAAMwB,aAAa,GAAG;AAAEV,YAAAA,CAAC,EAAE,CAACP,SAAS,CAACU,CAAhB;AAAmBA,YAAAA,CAAC,EAAEV,SAAS,CAACO;AAAhC,WAAtB;;AACA,cAAI,KAAK/G,MAAL,CAAYiC,KAAZ,IAAqB,CAAzB,EAA4B;AACxB,mBAAO;AACH8E,cAAAA,CAAC,EAAEU,aAAa,CAACV,CAAd,GAAkBnH,cADlB;AAEHsH,cAAAA,CAAC,EAAEO,aAAa,CAACP,CAAd,GAAkBtH;AAFlB,aAAP;AAIH;;AAED,iBAAO;AACHmH,YAAAA,CAAC,EAAEP,SAAS,CAACO,CAAV,GAAc,KAAK/G,MAAL,CAAYiC,KAA1B,GAAkCwF,aAAa,CAACV,CAAd,GAAkBnH,cADpD;AAEHsH,YAAAA,CAAC,EAAEV,SAAS,CAACU,CAAV,GAAc,KAAKlH,MAAL,CAAYiC,KAA1B,GAAkCwF,aAAa,CAACP,CAAd,GAAkBtH;AAFpD,WAAP;AAIH;;AAEDgH,QAAAA,YAAY,CAACJ,SAAD,EAAsCE,QAAtC,EAAgF;AACxF,cAAI,CAAC,KAAK/F,aAAV,EAAyB;AACrB,iBAAKA,aAAL,GAAqB,KAAKmE,UAAL,CAAgB4C,MAArC;;AACA,gBAAI,CAAC,KAAK/G,aAAV,EAAyB;AACrB,kBAAIrD,MAAJ,EAAY;AACR,qBAAKqK,oBAAL,CAA0BnB,SAA1B,EAAqCE,QAArC;AACH;;AACD;AACH;AACJ;;AAED,gBAAMkB,MAAM,GAAG,KAAKC,iBAAL,EAAf;AACA,cAAI,CAACD,MAAL,EAAa;AAEb;AAAA;AAAA,4CAAaE,cAAb,CAA4B,IAA5B,EAAkCF,MAAlC,EAdwF,CAexF;;AACA,gBAAMG,UAAU,GAAG,KAAKC,IAAL,CAAUC,gBAAV,EAAnB;AACAL,UAAAA,MAAM,CAACI,IAAP,CAAYE,gBAAZ,CACIH,UAAU,CAAChB,CAAX,GAAeL,QAAQ,CAACK,CAD5B,EAEIgB,UAAU,CAACb,CAAX,GAAeR,QAAQ,CAACQ,CAF5B,EAGIa,UAAU,CAACI,CAHf;AAKAP,UAAAA,MAAM,CAAC5C,IAAP,CAAYoD,UAAZ,CAAuBnG,KAAvB,GAA+B3D,gBAAgB,CAAC0I,IAAI,CAACqB,KAAL,CAAW7B,SAAS,CAACU,CAArB,EAAwBV,SAAS,CAACO,CAAlC,CAAD,CAA/C;AACAa,UAAAA,MAAM,CAAC5C,IAAP,CAAYsD,KAAZ,CAAkBrG,KAAlB,IAA2B,KAAKzC,SAAL,CAAeyC,KAA1C,CAvBwF,CAwBxF;AACA;;AACA2F,UAAAA,MAAM,CAACW,OAAP;;AAEA,cAAI,KAAKzJ,uBAAL,IAAgC,IAApC,EAA0C;AACtC,iBAAKA,uBAAL,CAA6B8I,MAA7B;AACH;AACJ;;AAEmC,cAApBD,oBAAoB,CAACnB,SAAD,EAAsCE,QAAtC,EAA0E;AAC1G;AACA,gBAAM8B,UAAU,GAAG,sDAAnB,CAF0G,CAG1G;;AACAC,UAAAA,MAAM,CAACC,OAAP,CAAeC,OAAf,CAAuB,UAAvB,EAAmC,YAAnC,EAAiDH,UAAjD,EACKI,IADL,CACWC,IAAD,IAAkB;AACpBzL,YAAAA,YAAY,CAAC0L,OAAb,CAAqB;AAAED,cAAAA,IAAI,EAAEA;AAAR,aAArB,EAAqC,CAACE,GAAD,EAAMrB,MAAN,KAAiB;AAClD,kBAAIqB,GAAJ,EAAS;AACLC,gBAAAA,OAAO,CAACC,KAAR,CAAcF,GAAd;AACA;AACH;;AACD,mBAAKpI,aAAL,GAAqB+G,MAArB;AACA,oBAAME,MAAM,GAAG,KAAKC,iBAAL,EAAf;AACA,kBAAI,CAACD,MAAL,EAAa;AAEb;AAAA;AAAA,gDAAaE,cAAb,CAA4B,IAA5B,EAAkCF,MAAlC,EATkD,CAUlD;;AACA,oBAAMG,UAAU,GAAG,KAAKC,IAAL,CAAUC,gBAAV,EAAnB;AACAL,cAAAA,MAAM,CAACI,IAAP,CAAYE,gBAAZ,CACIH,UAAU,CAAChB,CAAX,GAAeL,QAAQ,CAACK,CAD5B,EAEIgB,UAAU,CAACb,CAAX,GAAeR,QAAQ,CAACQ,CAF5B,EAGIa,UAAU,CAACI,CAHf;AAKAP,cAAAA,MAAM,CAAC5C,IAAP,CAAYoD,UAAZ,CAAuBnG,KAAvB,GAA+B3D,gBAAgB,CAAC0I,IAAI,CAACqB,KAAL,CAAW7B,SAAS,CAACU,CAArB,EAAwBV,SAAS,CAACO,CAAlC,CAAD,CAA/C;AACAa,cAAAA,MAAM,CAAC5C,IAAP,CAAYsD,KAAZ,CAAkBrG,KAAlB,IAA2B,KAAKzC,SAAL,CAAeyC,KAA1C;AACA2F,cAAAA,MAAM,CAACW,OAAP;AACH,aApBD;AAqBH,WAvBL;AAwBH;;AAESV,QAAAA,iBAAiB,GAAkB;AACzC,gBAAMqB,UAAU,GAAG;AAAA;AAAA,wCAAWC,OAAX,CAAmB;AAAA;AAAA,4CAAaC,YAAhC,EAA8C,KAAKzI,aAAnD,CAAnB;;AACA,cAAI,CAACuI,UAAL,EAAiB;AACbF,YAAAA,OAAO,CAACC,KAAR,CAAc,8CAAd;AACA,mBAAO,IAAP;AACH,WALwC,CAOzC;;;AACA,gBAAMrB,MAAM,GAAGsB,UAAU,CAACG,YAAX;AAAA;AAAA,+BAAf;;AACA,cAAI,CAACzB,MAAL,EAAa;AACToB,YAAAA,OAAO,CAACC,KAAR,CAAc,uDAAd;AACAC,YAAAA,UAAU,CAACI,OAAX;AACA,mBAAO,IAAP;AACH;;AAED,cAAIhM,MAAJ,EAAY;AACR4L,YAAAA,UAAU,CAACK,IAAX,GAAkB7K,OAAO,CAAC8K,mBAA1B;AACH;;AAED,iBAAO5B,MAAP;AACH;;AAESnG,QAAAA,cAAc,CAACH,SAAD,EAAoB;AACxC,cAAIA,SAAS,GAAG,CAAZ,IAAiB;AAAA;AAAA,8BAAMmI,WAAN,EAAjB,IAAwC;AAAA;AAAA,8BAAMC,QAAlD,EAA4D;AACxD,iBAAK3I,cAAL,GAAsB;AAAA;AAAA,gCAAM4I,WAAN,CAAkBC,YAAlB,CAA+BC,GAA/B,CAAmCvI,SAAnC,CAAtB,CADwD,CAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH,WAZuC,CAaxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACH;;AAEDwI,QAAAA,UAAU,CAACpC,MAAD,EAAiBhB,QAAjB,EAAiCqD,QAAjC,EAAiDC,QAAjD,EAAmE;AACzE,cAAI,CAACtC,MAAL,EAAa;AAEb,gBAAMuC,UAAU,GAAG;AAAA;AAAA,wCAAWd,OAAX,CAAmB,KAAKnB,IAAxB,EAA8BN,MAA9B,CAAnB;AACA,cAAI,CAACuC,UAAL,EAAiB;AAEjBA,UAAAA,UAAU,CAAC/B,gBAAX,CAA4BxB,QAA5B;AACAuD,UAAAA,UAAU,CAACC,gBAAX,CAA4BH,QAA5B,EAPyE,CAQzE;AACA;;AACA,eAAKI,YAAL,CAAkB,MAAM;AACpB;AAAA;AAAA,0CAAWC,UAAX,CAAsBH,UAAtB;AACH,WAFD,EAEGD,QAFH;AAGH;AAED;AACJ;AACA;;;AACcK,QAAAA,UAAU,GAAY;AAC5B;AACA,iBAAO,IAAP;AACH;;AAEMC,QAAAA,IAAI,CAACC,SAAD,EAA0B;AACjC,cAAI,CAAC,KAAKvL,QAAN,IAAkB,CAAC,KAAKA,QAAL,CAAciD,KAArC,EAA4C;AACxC;AACH;;AAED,kBAAQ,KAAK5B,OAAb;AACI,iBAAK9B,cAAc,CAAC+B,IAApB;AACI,mBAAKkK,gBAAL;AACA;;AACJ,iBAAKjM,cAAc,CAAC6F,OAApB;AACI,mBAAKqG,mBAAL;AACA;;AACJ,iBAAKlM,cAAc,CAACmM,QAApB;AACI,mBAAKC,oBAAL;AACA;;AACJ,iBAAKpM,cAAc,CAACqM,cAApB;AACI,mBAAKC,0BAAL;AACA;;AACJ,iBAAKtM,cAAc,CAACuM,SAApB;AACI,mBAAKC,qBAAL;AACA;;AACJ;AACI;AAjBR;;AAoBA,eAAK9K,WAAL,CAAiBgC,KAAjB,IAA0BsI,SAA1B;AACA,eAAKhK,kBAAL,IAA2BgK,SAA3B;AACA,eAAK/J,iBAAL,IAA0B+J,SAA1B;AAEA,eAAKpI,SAAL;AACH;;AAESqI,QAAAA,gBAAgB,GAAG;AACzB,cAAI,KAAKjK,kBAAL,IAA2B,KAAKnB,YAAL,CAAkB6C,KAAjD,EAAwD;AACpD,iBAAKO,YAAL,CAAkBjE,cAAc,CAAC6F,OAAjC;AACH;AACJ;;AAESqG,QAAAA,mBAAmB,GAAG;AAC5B,cAAI,CAAC,KAAKvL,SAAL,CAAe+C,KAApB,EACI,KAAKO,YAAL,CAAkBjE,cAAc,CAACmM,QAAjC,EADJ,KAEK;AACD,gBAAI,KAAKnK,kBAAL,IAA2B,KAAKlB,eAAL,CAAqB4C,KAApD,EAA2D;AACvD,mBAAKO,YAAL,CAAkBjE,cAAc,CAACmM,QAAjC;AACH;AACJ;AACJ;;AAESC,QAAAA,oBAAoB,GAAG;AAC7B,cAAI,KAAKpK,kBAAL,GAA0B,KAAKjB,YAAL,CAAkB2C,KAAhD,EAAuD;AACnD,iBAAKwD,YAAL;AACA,gBAAI,KAAKtG,MAAL,CAAY8C,KAAhB,EACI,KAAKO,YAAL,CAAkBjE,cAAc,CAACqM,cAAjC,EADJ,KAGI,KAAKpI,YAAL,CAAkBjE,cAAc,CAACuM,SAAjC;AACJ;AACH,WAR4B,CAU7B;;;AACA,cAAI,CAAC,KAAKrK,WAAV,EAAuB;AACnB,iBAAK+E,aAAL;AACH,WAFD,MAGK,IAAI,KAAKjF,kBAAL,IAA2B,KAAKG,aAApC,EAAmD;AACpD,iBAAK6F,OAAL;;AACA,gBAAI,KAAK5G,eAAL,CAAqBsC,KAArB,IAA8B,CAAlC,EAAqC;AACjC,mBAAKsD,gBAAL;AACH,aAFD,MAGK;AACD;AACA,mBAAK7E,aAAL,GAAqB,KAAKH,kBAAL,GAA0B,QAA/C;AACH;AACJ;;AAED,cAAIyK,WAAW,GAAG,KAAK/J,mBAAL,CAAyBwB,MAAzB,GAAkC,CAApD,CAzB6B,CA0B7B;;AACA,eAAK2D,mBAAL;;AACA,cAAI4E,WAAW,IAAI,KAAK/J,mBAAL,CAAyBwB,MAAzB,IAAmC,CAAtD,EAAyD;AACrD,iBAAK8C,gBAAL;AACH;AACJ;;AAESsF,QAAAA,0BAA0B,GAAG;AACnC,cAAI,KAAKtK,kBAAL,IAA2B,KAAKd,YAAL,CAAkBwC,KAAjD,EAAwD;AACpD,iBAAKO,YAAL,CAAkBjE,cAAc,CAAC6F,OAAjC;AACH;AACJ;;AAES2G,QAAAA,qBAAqB,GAAG;AAC9B;AACA,eAAK/L,QAAL,CAAciD,KAAd,GAAsB,KAAtB;AACA,eAAKjD,QAAL,CAAcsD,MAAd;AACH;;AAlqBiE,O,UAE3DkH,mB,GAA8B,U;;;;;iBAGf,E;;;;;;;iBAKc;AAAA;AAAA,2C;;;;;;;iBAGF;AAAA;AAAA,yC", "sourcesContent": ["import { _decorator, assetManager, misc, Prefab, Quat, Vec3, Enum, CCString } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { ResEmitter } from 'db://assets/bundles/common/script/autogen/luban/schema';\r\nimport { LubanMgr } from 'db://assets/bundles/common/script/luban/LubanMgr';\r\nimport Entity from 'db://assets/bundles/common/script/game/ui/base/Entity';\r\nimport { BulletData } from '../data/bullet/BulletData';\r\nimport { EmitterData } from '../data/bullet/EmitterData';\r\nimport { Bullet, BulletProperty } from './Bullet';\r\nimport { BulletSystem } from './BulletSystem';\r\nimport { EventGroup, EventGroupContext } from \"./EventGroup\";\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { Property, PropertyContainerComponent } from './PropertyContainer';\r\nimport type PlaneBase from '../ui/plane/PlaneBase';\r\nimport { ExpressionValue } from '../data/bullet/ExpressionValue';\r\n\r\n// // 这个import仅用于编辑功能\r\n// import { BulletEnum } from 'db://assets/editor/enum-gen/BulletEnum'\r\n\r\nconst { ccclass, executeInEditMode, property, disallowMultiple, menu } = _decorator;\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\n\r\n/**\r\n * 发射器状态变换\r\n * [None] -> <InitialDelay> -> [Prewarm] -> <PrewarmDuration> -> [Emitting]\r\n *                                 ^                                 |\r\n *                                 |                                 v\r\n *                                 |                           <EmitDuration>\r\n *                                 |                                 |\r\n *                                 |                              isLoop? ---no---> [Completed] \r\n *                                 |                                 |\r\n *                                 |                                 |yes\r\n *                                 |                                 |\r\n *                                 |                                 v\r\n *                                 -------<LoopInterval>------[LoopEndReached]\r\n */\r\nexport enum eEmitterStatus {\r\n    None, Prewarm, Emitting, LoopEndReached, Completed\r\n}\r\n\r\n// 用枚举定义属性\r\nexport enum eEmitterProp {\r\n    IsActive = 1, IsOnlyInScreen, IsPreWarm, IsLoop,\r\n    InitialDelay, PrewarmDuration, EmitDuration, EmitInterval, EmitPower, LoopInterval,\r\n    PerEmitCount, PerEmitInterval, PerEmitOffsetX,\r\n    Angle, Count, Arc, Radius,\r\n    ElapsedTime,\r\n}\r\n\r\n/**\r\n * 说明：\r\n * 因为发射器属性可能需要从emitterData里的公式计算，如: randi(0,360);\r\n * 但同时也可能被事件组修改，参考: EmitterEventActions\r\n * 事件组的优先级要高于emitterData的公式计算, 因此, 如果一个属性带了ePropMask.EventGroup标记, 后续ReEval时就直接跳过\r\n */\r\nexport enum ePropMask {\r\n    ReEval = 1 << 0, // 需要重新从公式计算\r\n    EventGroup = 1 << 1, // 需要被事件组修改\r\n}\r\n\r\nexport type onBulletCreatedDelegate = (bullet: Bullet) => void;\r\nexport type onEmitterStatusChangedDelegate = (emitter: Emitter, oldStatus: eEmitterStatus, newStatus: eEmitterStatus) => void;\r\n\r\n/**\r\n * 目前Emitter,EventGroup,BulletSystem的状态管理还是比较混乱\r\n * 需要看下怎么调整，使代码不论是运行时，还是编辑器下，都更加健壮\r\n * - young\r\n */\r\n@ccclass('Emitter')\r\n// @inspector('editor/inspector/components/emitter')\r\n@menu('子弹系统/发射器')\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class Emitter extends PropertyContainerComponent<eEmitterProp> {\r\n\r\n    static kBulletNameInEditor: string = \"_bullet_\";\r\n\r\n    @property({displayName: '名称', editorOnly: true})\r\n    emitterName: string = '';                // 备注(策划用)\r\n\r\n    // @property({ type: Enum(BulletEnum), displayName: \"子弹ID\" })\r\n    // readonly bulletID: number = 0;\r\n    @property({ type: EmitterData, displayName: \"发射器属性\" })\r\n    readonly emitterData: EmitterData = new EmitterData();\r\n\r\n    @property({ type: BulletData, displayName: \"子弹属性\" })\r\n    readonly bulletData: BulletData = new BulletData();\r\n\r\n    // callbacks\r\n    onBulletCreatedCallback: onBulletCreatedDelegate | null = null;\r\n    onEmitterStatusChangedCallback: onEmitterStatusChangedDelegate | null = null;\r\n\r\n    // 以下属性缓存为了性能优化(减少this.getProperty<T>的调用)\r\n    public isActive!: Property<boolean>;\r\n    public isOnlyInScreen!: Property<boolean>;\r\n    public isPreWarm!: Property<boolean>;\r\n    public isLoop!: Property<boolean>;\r\n    public initialDelay!: Property<number>;\r\n    public preWarmDuration!: Property<number>;\r\n    public emitDuration!: Property<number>;\r\n    public emitInterval!: Property<number>;\r\n    public emitPower!: Property<number>;\r\n    public loopInterval!: Property<number>;\r\n    public perEmitCount!: Property<number>;\r\n    public perEmitInterval!: Property<number>;\r\n    public perEmitOffsetX!: Property<number>;\r\n    public angle!: Property<number>;\r\n    public count!: Property<number>;\r\n    public arc!: Property<number>;\r\n    public radius!: Property<number>;\r\n    public elapsedTime!: Property<number>; \r\n    // 以下用于事件组修改子弹的属性，（不直接修改bulletData)\r\n    public bulletProp!: BulletProperty;\r\n\r\n    // 发射器自己的事件组\r\n    public eventGroups: EventGroup[] = [];\r\n\r\n    // 私有变量\r\n    protected _emitterId: number = 0;\r\n    protected _status: eEmitterStatus = eEmitterStatus.None;\r\n    protected _statusElapsedTime: number = 0;\r\n    protected _totalElapsedTime: number = 0;\r\n    protected _isEmitting: boolean = false;\r\n    protected _nextEmitTime: number = 0;\r\n\r\n    protected _bulletPrefab: Prefab | null = null;\r\n    protected _prewarmEffectPrefab: Prefab | null = null;\r\n    protected _emitEffectPrefab: Prefab | null = null;\r\n    protected _entity: PlaneBase | null = null;\r\n    protected _emitterConfig: ResEmitter | undefined = undefined;\r\n    \r\n    // Per-emit timing tracking\r\n    protected _perEmitBulletQueue: Array<{ index: number, perEmitIndex: number, targetTime: number }> = [];\r\n\r\n    get isEmitting(): boolean { return this._isEmitting; }\r\n    get status(): eEmitterStatus { return this._status; }\r\n    get statusElapsedTime(): number { return this._statusElapsedTime; }\r\n    get totalElapsedTime(): number { return this._totalElapsedTime; }\r\n    get emitterId(): number { return this._emitterId; }\r\n    get config(): ResEmitter | undefined { return this._emitterConfig; }\r\n    set emitterId(id: number) {\r\n        this._emitterId = id;\r\n        this.loadConfigByID(id);\r\n    }\r\n\r\n    protected onLoad(): void {\r\n        this.createProperties();\r\n        this.createEventGroups();\r\n\r\n        // 更新属性\r\n        this.resetProperties();\r\n    }\r\n\r\n    //#region \"Editor Region\"\r\n    public onLostFocusInEditor(): void {\r\n        this.updatePropertiesInEditor();\r\n        this.createEventGroups();\r\n    }\r\n\r\n    public updatePropertiesInEditor() {\r\n        if (!this.emitterData) return;\r\n\r\n        this.isActive.value = true;\r\n        this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;\r\n        this.isPreWarm.value = this.emitterData.isPreWarm;\r\n        this.isLoop.value = this.emitterData.isLoop;\r\n\r\n        this.initialDelay.value = this.emitterData.initialDelay.eval(null, true);\r\n        this.preWarmDuration.value = this.emitterData.preWarmDuration.eval(null, true);\r\n        this.emitDuration.value = this.emitterData.emitDuration.eval(null, true);\r\n        this.emitInterval.value = this.emitterData.emitInterval.eval(null, true);\r\n        this.emitPower.value = this.emitterData.emitPower.eval(null, true);\r\n        this.loopInterval.value = this.emitterData.loopInterval.eval(null, true);\r\n        this.perEmitCount.value = this.emitterData.perEmitCount.eval(null, true);\r\n        this.perEmitInterval.value = this.emitterData.perEmitInterval.eval(null, true);\r\n        this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval(null, true);\r\n        this.angle.value = this.emitterData.angle.eval(null, true);\r\n        this.count.value = this.emitterData.count.eval(null, true);\r\n        this.arc.value = this.emitterData.arc.eval(null, true);\r\n        this.radius.value = this.emitterData.radius.eval(null, true);\r\n\r\n        this.notifyAll(true);\r\n    }\r\n    //#endregion \"Editor Region\"\r\n\r\n    // 通过这个接口来启用和禁用发射器\r\n    public setIsActive(active: boolean) {\r\n        this.isActive.value = active;\r\n        this.isActive.notify();\r\n    }\r\n\r\n    // 这个接口清理发射器的状态，全部从头开始\r\n    public reset() {\r\n        this._isEmitting = false;\r\n        this.changeStatus(eEmitterStatus.None);\r\n        this.resetProperties();\r\n        if (this.eventGroups.length > 0) {\r\n            this.eventGroups.forEach(group => group.reset());\r\n        }\r\n    }\r\n\r\n    public setEntity(entity: PlaneBase) {\r\n        this._entity = entity;\r\n    }\r\n\r\n    public getEntity(): PlaneBase | null {\r\n        return this._entity;\r\n    }\r\n\r\n    protected createProperties() {\r\n        this.clear();\r\n\r\n        this.isActive = this.addProperty(eEmitterProp.IsActive, false);\r\n        this.elapsedTime = this.addProperty(eEmitterProp.ElapsedTime, 0);\r\n        this.isOnlyInScreen = this.addProperty(eEmitterProp.IsOnlyInScreen, true);\r\n        this.isPreWarm = this.addProperty(eEmitterProp.IsPreWarm, true);\r\n        this.isLoop = this.addProperty(eEmitterProp.IsLoop, true);\r\n\r\n        this.initialDelay = this.addProperty(eEmitterProp.InitialDelay, 0);\r\n        this.preWarmDuration = this.addProperty(eEmitterProp.PrewarmDuration, 0);\r\n        this.emitDuration = this.addProperty(eEmitterProp.EmitDuration, 0);\r\n        this.emitInterval = this.addProperty(eEmitterProp.EmitInterval, 0);\r\n        this.emitPower = this.addProperty(eEmitterProp.EmitPower, 1);\r\n        this.loopInterval = this.addProperty(eEmitterProp.LoopInterval, 0);\r\n        this.perEmitCount = this.addProperty(eEmitterProp.PerEmitCount, 1);\r\n        this.perEmitInterval = this.addProperty(eEmitterProp.PerEmitInterval, 0);\r\n        this.perEmitOffsetX = this.addProperty(eEmitterProp.PerEmitOffsetX, 0);\r\n        this.angle = this.addProperty(eEmitterProp.Angle, 0);\r\n        this.count = this.addProperty(eEmitterProp.Count, 1);\r\n        this.arc = this.addProperty(eEmitterProp.Arc, 0);\r\n        this.radius = this.addProperty(eEmitterProp.Radius, 0);\r\n\r\n        // 子弹相关属性\r\n        this.bulletProp = new BulletProperty();\r\n\r\n        this.isActive.on((value) => {\r\n            if (value) {\r\n                this.changeStatus(eEmitterStatus.Prewarm);\r\n                BulletSystem.onCreateEmitter(this);\r\n            } else {\r\n                BulletSystem.onDestroyEmitter(this);\r\n            }\r\n        });\r\n    }\r\n\r\n    protected createEventGroups() {\r\n        if (!this.emitterData || this.emitterData.eventGroupData.length <= 0) return;\r\n\r\n        this.eventGroups = [];\r\n        let ctx = new EventGroupContext();\r\n        ctx.emitter = this;\r\n        ctx.playerPlane = BulletSystem.playerPlane;\r\n        for (const eventGroup of this.emitterData.eventGroupData) {\r\n            BulletSystem.createEmitterEventGroup(ctx, eventGroup);\r\n        }\r\n    }\r\n\r\n    // reset properties from emitterData\r\n    protected resetProperties() {\r\n        if (!this.emitterData) return;\r\n\r\n        this.isActive.value = false;\r\n        this.elapsedTime.value = 0;\r\n        this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;\r\n        this.isPreWarm.value = this.emitterData.isPreWarm;\r\n        this.isLoop.value = this.emitterData.isLoop;\r\n\r\n        this.initialDelay.value = this.emitterData.initialDelay.eval(null, true);\r\n        this.preWarmDuration.value = this.emitterData.preWarmDuration.eval(null, true);\r\n        this.emitDuration.value = this.emitterData.emitDuration.eval(null, true);\r\n        this.emitInterval.value = this.emitterData.emitInterval.eval(null, true);\r\n        this.emitPower.value = this.emitterData.emitPower.eval(null, true);\r\n        this.loopInterval.value = this.emitterData.loopInterval.eval(null, true);\r\n        this.perEmitCount.value = this.emitterData.perEmitCount.eval(null, true);\r\n        this.perEmitInterval.value = this.emitterData.perEmitInterval.eval(null, true);\r\n        this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval(null, true);\r\n        this.angle.value = this.emitterData.angle.eval(null, true);\r\n        this.count.value = this.emitterData.count.eval(null, true);\r\n        this.arc.value = this.emitterData.arc.eval(null, true);\r\n        this.radius.value = this.emitterData.radius.eval(null, true);\r\n\r\n        this.bulletProp.resetFromData(this.bulletData);\r\n\r\n        this.notifyAll(true);\r\n    }\r\n\r\n    protected evalProperty(prop: Property<number>, value: ExpressionValue) {\r\n        // 为什么这样写，而不是直接：prop.setValue(value.eval(), ePropMask.ReEval);\r\n        // 是为了避免非必要的eval()调用\r\n        if (prop.canWrite(ePropMask.ReEval) && !value.isFixedValue) {\r\n            prop.value = value.eval();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * public apis\r\n     */\r\n    changeStatus(status: eEmitterStatus) {\r\n        if (this._status === status) return;\r\n\r\n        const oldStatus = this._status;\r\n        this._status = status;\r\n        this._statusElapsedTime = 0;\r\n        this._nextEmitTime = 0;\r\n        // Clear per-emit queue when changing status\r\n        this._perEmitBulletQueue = [];\r\n\r\n        if (status === eEmitterStatus.Prewarm) {\r\n            this.elapsedTime.value = 0;\r\n            // emitInterval可能是一个序列，每次尽量从第一个序列从头开始\r\n            this.emitterData.emitInterval.reset();\r\n        }\r\n        \r\n        if (status === eEmitterStatus.None) {\r\n            if (this.eventGroups.length > 0) {\r\n                this.eventGroups.forEach(group => group.tryStop());\r\n            }\r\n        }\r\n        else {\r\n            // 所有其他状态，都尝试开始执行eventGroup\r\n            if (this.eventGroups.length > 0) {\r\n                this.eventGroups.forEach(group => group.tryStart());\r\n            }\r\n        }\r\n\r\n        if (this.onEmitterStatusChangedCallback != null) {\r\n            this.onEmitterStatusChangedCallback(this, oldStatus, status);\r\n        }\r\n    }\r\n\r\n    protected scheduleNextEmit() {\r\n        // re-eval\r\n        this.evalProperty(this.emitInterval, this.emitterData.emitInterval);\r\n\r\n        // Schedule the next emit after emitInterval\r\n        this._nextEmitTime = this._statusElapsedTime + this.emitInterval.value;\r\n        // console.log('scheduleNextEmit: ', this._nextEmitTime, ', ', this._statusElapsedTime, ', ', this.emitInterval.value);\r\n    }\r\n\r\n    protected startEmitting() {\r\n        this._isEmitting = true;\r\n        // 下一次update时触发发射\r\n        // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射\r\n        // this.tryEmit();\r\n        // if (this.perEmitInterval.value <= 0) {\r\n        //     this.scheduleNextEmit();\r\n        // }\r\n        // else {\r\n        //     // 开始这一波\r\n        //     this._nextEmitTime = this._statusElapsedTime + 10000000;\r\n        // }\r\n        // // reset status time \r\n        // this._statusElapsedTime = 0;\r\n    }\r\n\r\n    protected stopEmitting() {\r\n        this._isEmitting = false;\r\n        // Clear the per-emit bullet queue\r\n        this._perEmitBulletQueue = [];\r\n        this.unscheduleAllCallbacks();\r\n    }\r\n\r\n    protected canEmit(): boolean {\r\n        // 检查是否可以触发发射\r\n        // Override this method in subclasses to add custom trigger conditions\r\n        return true;\r\n    }\r\n\r\n    protected emit(): void {\r\n        // re-eval\r\n        this.evalProperty(this.count, this.emitterData.count);\r\n        this.evalProperty(this.arc, this.emitterData.arc);\r\n        this.evalProperty(this.radius, this.emitterData.radius);\r\n        this.evalProperty(this.perEmitCount, this.emitterData.perEmitCount);\r\n        \r\n        if (this.perEmitInterval.value > 0) {\r\n            // Generate bullets in time-sorted order directly\r\n            for (let j = 0; j < this.perEmitCount.value; j++) {\r\n                this.evalProperty(this.perEmitInterval, this.emitterData.perEmitInterval);\r\n                const targetTime = this._statusElapsedTime + (this.perEmitInterval.value * j);\r\n                for (let i = 0; i < this.count.value; i++) {\r\n                    this._perEmitBulletQueue.push({\r\n                        index: i,\r\n                        perEmitIndex: j,\r\n                        targetTime: targetTime\r\n                    });\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            // Immediate emission - no timing needed\r\n            for (let i = 0; i < this.count.value; i++) {\r\n                for (let j = 0; j < this.perEmitCount.value; j++) {\r\n                    this.emitSingle(i, j);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    protected processPerEmitQueue(): void {\r\n        // Process bullets that should be emitted based on current time\r\n        while (this._perEmitBulletQueue.length > 0) {\r\n            const nextBullet = this._perEmitBulletQueue[0];\r\n\r\n            // Check if it's time to emit this bullet\r\n            if (this._statusElapsedTime >= nextBullet.targetTime) {\r\n                // Remove from queue and emit\r\n                this._perEmitBulletQueue.shift();\r\n                this.emitSingle(nextBullet.index, nextBullet.perEmitIndex);\r\n            } else {\r\n                // No more bullets ready to emit yet\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    protected tryEmit(): boolean {\r\n        if (this.canEmit()) {\r\n            this.emit();\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    protected emitSingle(index: number, perEmitIndex: number) {\r\n        const direction = this.getSpawnDirection(index);\r\n        const position = this.getSpawnPosition(index, perEmitIndex);\r\n        this.createBullet(direction, position);\r\n    }\r\n\r\n    /**\r\n     * Calculate the direction for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Direction vector {x, y}\r\n     */\r\n    getSpawnDirection(index: number): { x: number, y: number } {\r\n        // 期望如果配了公式，每次发射方向都随机下\r\n        this.evalProperty(this.angle, this.emitterData.angle);\r\n        // 计算发射方向\r\n        const angleOffset = this.count.value > 1 ? (this.arc.value / (this.count.value - 1)) * index - this.arc.value / 2 : 0;\r\n        const radian = degreesToRadians(this.angle.value + angleOffset);\r\n\r\n        return {\r\n            x: Math.cos(radian),\r\n            y: Math.sin(radian)\r\n        };\r\n    }\r\n    \r\n    /**\r\n     * Get the spawn position for a bullet at the given index\r\n     * odd number to the right, even number to the left\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Position offset from emitter center\r\n     */\r\n    getSpawnPosition(index: number, perEmitIndex: number): { x: number, y: number } {\r\n        // add perEmitOffsetX by perEmitIndex, with the rules:\r\n        // by the following order:0, 1; 2, 0, 1; 2, 0, 1, 3;\r\n        const getEmitOffsetX = (perEmitIndex: number, perEmitCount: number, perEmitOffsetX: number) => {\r\n            if (perEmitCount <= 1 || perEmitOffsetX === 0) return 0;\r\n            const interval = perEmitOffsetX / (perEmitCount - 1);\r\n            //const middle = 0;\r\n\r\n            if (perEmitCount % 2 === 1) {\r\n                // 奇数情况\r\n                if (perEmitIndex === 0) return 0;\r\n                if (perEmitIndex % 2 === 0) {\r\n                    // 偶数索引在左边\r\n                    const stepsFromMiddle = Math.floor(perEmitIndex / 2);\r\n                    return -stepsFromMiddle * interval;\r\n                }\r\n                else {\r\n                    // 奇数索引在右边\r\n                    const stepsFromMiddle = Math.ceil(perEmitIndex / 2);\r\n                    return stepsFromMiddle * interval;\r\n                }\r\n            } else {\r\n                // 偶数情况\r\n                if (perEmitIndex === 0) return -interval / 2;\r\n                if (perEmitIndex % 2 === 0) {\r\n                    // 偶数索引在左边\r\n                    const stepsFromMiddle = Math.floor(perEmitIndex / 2);\r\n                    return -interval / 2 - stepsFromMiddle * interval;\r\n                }\r\n                else {\r\n                    // 奇数索引在右边\r\n                    const stepsFromMiddle = Math.floor(perEmitIndex / 2);\r\n                    return interval / 2 + stepsFromMiddle * interval;\r\n                }\r\n            }\r\n        }\r\n\r\n        this.evalProperty(this.perEmitOffsetX, this.emitterData.perEmitOffsetX);\r\n        const perEmitOffsetX = getEmitOffsetX(perEmitIndex, this.perEmitCount.value, this.perEmitOffsetX.value);\r\n        if (this.radius.value <= 0) {\r\n            return { x: perEmitOffsetX, y: 0 };\r\n        }\r\n\r\n        const direction = this.getSpawnDirection(index);\r\n        // 计算垂直于发射方向的向量（逆时针90度旋转）\r\n        const perpendicular = { x: -direction.y, y: direction.x };\r\n        if (this.radius.value <= 0) {\r\n            return {\r\n                x: perpendicular.x * perEmitOffsetX,\r\n                y: perpendicular.y * perEmitOffsetX\r\n            };\r\n        }\r\n\r\n        return {\r\n            x: direction.x * this.radius.value + perpendicular.x * perEmitOffsetX,\r\n            y: direction.y * this.radius.value + perpendicular.y * perEmitOffsetX\r\n        };\r\n    }\r\n\r\n    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }): void {\r\n        if (!this._bulletPrefab) {\r\n            this._bulletPrefab = this.bulletData.prefab;\r\n            if (!this._bulletPrefab) {\r\n                if (EDITOR) {\r\n                    this.createBulletInEditor(direction, position);\r\n                }\r\n                return;\r\n            }\r\n        }\r\n\r\n        const bullet = this.instantiateBullet();\r\n        if (!bullet) return;\r\n\r\n        BulletSystem.onCreateBullet(this, bullet);\r\n        // Set bullet position relative to emitter\r\n        const emitterPos = this.node.getWorldPosition();\r\n        bullet.node.setWorldPosition(\r\n            emitterPos.x + position.x,\r\n            emitterPos.y + position.y,\r\n            emitterPos.z\r\n        );\r\n        bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));\r\n        bullet.prop.speed.value *= this.emitPower.value;\r\n        // 为什么需要在这里resetEventGroups?\r\n        // 因为EventGroups的条件初始化依赖上面先初始化子弹的属性\r\n        bullet.onReady();\r\n\r\n        if (this.onBulletCreatedCallback != null) {\r\n            this.onBulletCreatedCallback(bullet);\r\n        }\r\n    }\r\n\r\n    protected async createBulletInEditor(direction: { x: number, y: number }, position: { x: number, y: number }) {\r\n        // use a default bullet prefab\r\n        const prefabPath = 'db://assets/resources/game/prefabs/Bullet_New.prefab';\r\n        // @ts-ignore\r\n        Editor.Message.request('asset-db', 'query-uuid', prefabPath)\r\n            .then((uuid: string) => {\r\n                assetManager.loadAny({ uuid: uuid }, (err, prefab) => {\r\n                    if (err) {\r\n                        console.error(err);\r\n                        return;\r\n                    }\r\n                    this._bulletPrefab = prefab;\r\n                    const bullet = this.instantiateBullet();\r\n                    if (!bullet) return;\r\n\r\n                    BulletSystem.onCreateBullet(this, bullet);\r\n                    // Set bullet position relative to emitter\r\n                    const emitterPos = this.node.getWorldPosition();\r\n                    bullet.node.setWorldPosition(\r\n                        emitterPos.x + position.x,\r\n                        emitterPos.y + position.y,\r\n                        emitterPos.z\r\n                    );\r\n                    bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));\r\n                    bullet.prop.speed.value *= this.emitPower.value;\r\n                    bullet.onReady();\r\n                });\r\n            });\r\n    }\r\n\r\n    protected instantiateBullet(): Bullet | null {\r\n        const bulletNode = ObjectPool.getNode(BulletSystem.bulletParent, this._bulletPrefab!);\r\n        if (!bulletNode) {\r\n            console.error(\"Emitter: Failed to instantiate bullet prefab\");\r\n            return null;\r\n        }\r\n\r\n        // Get the bullet component\r\n        const bullet = bulletNode.getComponent(Bullet);\r\n        if (!bullet) {\r\n            console.error(\"Emitter: Bullet prefab does not have Bullet component\");\r\n            bulletNode.destroy();\r\n            return null;\r\n        }\r\n\r\n        if (EDITOR) {\r\n            bulletNode.name = Emitter.kBulletNameInEditor;\r\n        }\r\n\r\n        return bullet;\r\n    }\r\n\r\n    protected loadConfigByID(emitterId: number) {\r\n        if (emitterId > 0 && MyApp.GetInstance() && MyApp.lubanMgr) {\r\n            this._emitterConfig = MyApp.lubanTables.TbResEmitter.get(emitterId);\r\n            // if (this._bulletConfig) {\r\n            //     MyApp.resMgr.load(this._bulletConfig.prefab, Prefab, (error: any, prefab: Prefab) => {\r\n            //         if (error) {\r\n            //             console.error(\"Emitter load bullet prefab err\", error);\r\n            //             return;\r\n            //         }\r\n            //         this._bulletPrefab = prefab;\r\n            //     });\r\n            // }\r\n        }\r\n        // else if (EDITOR) {\r\n        //     let lubanMgr = new LubanMgr();\r\n        //     lubanMgr.initInEditor().then(() => {\r\n        //         this._bulletConfig = lubanMgr.table.TbResBullet.get(bulletID);\r\n        //         if (this._bulletConfig) {\r\n        //             const prefabPath = 'db://assets/resources/' + this._bulletConfig.prefab + '.prefab';\r\n        //             // @ts-ignore\r\n        //             Editor.Message.request('asset-db', 'query-uuid', prefabPath)\r\n        //                 .then((uuid: string) => {\r\n        //                     assetManager.loadAny({ uuid: uuid }, (err, prefab) => {\r\n        //                         if (err) {\r\n        //                             console.error(err);\r\n        //                             return;\r\n        //                         }\r\n        //                         this._bulletPrefab = prefab;\r\n        //                     });\r\n        //                 });\r\n        //         }\r\n        //      });\r\n        // }\r\n    }\r\n\r\n    playEffect(prefab: Prefab, position: Vec3, rotation: Quat, duration: number) {\r\n        if (!prefab) return;\r\n\r\n        const effectNode = ObjectPool.getNode(this.node, prefab);\r\n        if (!effectNode) return;\r\n\r\n        effectNode.setWorldPosition(position);\r\n        effectNode.setWorldRotation(rotation);\r\n        // Play the effect and destroy it after duration\r\n        // effectNode.getComponent(ParticleSystem)?.play();\r\n        this.scheduleOnce(() => {\r\n            ObjectPool.returnNode(effectNode);\r\n        }, duration);\r\n    }\r\n\r\n    /**\r\n     * Return true if this.node is in screen\r\n     */\r\n    protected isInScreen(): boolean {\r\n        // TODO: Get mainCamera.containsNode(this.node)\r\n        return true;\r\n    }\r\n\r\n    public tick(deltaTime: number): void {\r\n        if (!this.isActive || !this.isActive.value) {\r\n            return;\r\n        }\r\n\r\n        switch (this._status) {\r\n            case eEmitterStatus.None:\r\n                this.updateStatusNone();\r\n                break;\r\n            case eEmitterStatus.Prewarm:\r\n                this.updateStatusPrewarm();\r\n                break;\r\n            case eEmitterStatus.Emitting:\r\n                this.updateStatusEmitting();\r\n                break;\r\n            case eEmitterStatus.LoopEndReached:\r\n                this.updateStatusLoopEndReached();\r\n                break;\r\n            case eEmitterStatus.Completed:\r\n                this.updateStatusCompleted();\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n\r\n        this.elapsedTime.value += deltaTime;\r\n        this._statusElapsedTime += deltaTime;\r\n        this._totalElapsedTime += deltaTime;\r\n\r\n        this.notifyAll();\r\n    }\r\n\r\n    protected updateStatusNone() {\r\n        if (this._statusElapsedTime >= this.initialDelay.value) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusPrewarm() {\r\n        if (!this.isPreWarm.value)\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        else {\r\n            if (this._statusElapsedTime >= this.preWarmDuration.value) {\r\n                this.changeStatus(eEmitterStatus.Emitting);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected updateStatusEmitting() {\r\n        if (this._statusElapsedTime > this.emitDuration.value) {\r\n            this.stopEmitting();\r\n            if (this.isLoop.value)\r\n                this.changeStatus(eEmitterStatus.LoopEndReached);\r\n            else\r\n                this.changeStatus(eEmitterStatus.Completed);\r\n            return;\r\n        }\r\n\r\n        // Start emitting if not already started\r\n        if (!this._isEmitting) {\r\n            this.startEmitting();\r\n        }\r\n        else if (this._statusElapsedTime >= this._nextEmitTime) {\r\n            this.tryEmit();\r\n            if (this.perEmitInterval.value <= 0) {\r\n                this.scheduleNextEmit();\r\n            }\r\n            else {\r\n                // 开始这一波\r\n                this._nextEmitTime = this._statusElapsedTime + 10000000;\r\n            }\r\n        }\r\n\r\n        let wasEmitting = this._perEmitBulletQueue.length > 0;\r\n        // Process per-emit bullet queue based on precise timing\r\n        this.processPerEmitQueue();\r\n        if (wasEmitting && this._perEmitBulletQueue.length <= 0) {\r\n            this.scheduleNextEmit();\r\n        }\r\n    }\r\n\r\n    protected updateStatusLoopEndReached() {\r\n        if (this._statusElapsedTime >= this.loopInterval.value) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusCompleted() {\r\n        // Do nothing or cleanup if needed\r\n        this.isActive.value = false;\r\n        this.isActive.notify();\r\n    }\r\n}\r\n"]}