{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlaneStat.ts"], "names": ["MainPlaneStat", "<PERSON><PERSON><PERSON><PERSON>", "killEnemy", "usedNuclear", "usedSuper"], "mappings": ";;;iBACqBA,a;;;;;;;;;;;;;yBAAAA,a,GAAN,MAAMA,aAAN,CAAoB;AAAA;AAAA,eAC/BC,WAD+B,GACT,CADS;AAAA,eAE/BC,SAF+B,GAEX,CAFW;AAAA,eAG/BC,WAH+B,GAGT,CAHS;AAAA,eAI/BC,SAJ+B,GAIX,CAJW;AAAA;;AAAA,O", "sourcesContent": ["\r\nexport default class MainPlaneStat {\r\n    pickDiamond: number = 0;\r\n    killEnemy: number = 0;\r\n    usedNuclear: number = 0;\r\n    usedSuper: number = 0;\r\n}"]}