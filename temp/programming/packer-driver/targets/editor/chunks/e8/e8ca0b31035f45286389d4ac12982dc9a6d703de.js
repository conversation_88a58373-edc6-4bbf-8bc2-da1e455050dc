System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, ResSkillConditionElem, ExCondition, ExConditionNum, _crd;

  function _reportPossibleCrUseOfResSkillConditionElem(extras) {
    _reporterNs.report("ResSkillConditionElem", "../../../../autogen/luban/schema", _context.meta, extras);
  }

  _export({
    ExCondition: void 0,
    ExConditionNum: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      ResSkillConditionElem = _unresolved_2.ResSkillConditionElem;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c06b7XZX+xKH7Thcp5WvS29", "ExCondition", undefined);

      _export("ExCondition", ExCondition = class ExCondition extends (_crd && ResSkillConditionElem === void 0 ? (_reportPossibleCrUseOfResSkillConditionElem({
        error: Error()
      }), ResSkillConditionElem) : ResSkillConditionElem) {
        constructor(res) {
          super(res);
        }

        reset() {}

      });

      _export("ExConditionNum", ExConditionNum = class ExConditionNum extends ExCondition {
        constructor(res, num) {
          super(res);
          this.num = 0;
          this.num = num;
        }

        reset() {
          this.num -= this.value;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e8ca0b31035f45286389d4ac12982dc9a6d703de.js.map