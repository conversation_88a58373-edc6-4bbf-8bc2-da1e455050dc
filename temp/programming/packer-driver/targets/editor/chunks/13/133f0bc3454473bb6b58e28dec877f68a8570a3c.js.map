{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/wave/WaveEventGroup.ts"], "names": ["WaveEventGroupContext", "WaveEventGroup", "WaveConditionFactory", "WaveActionFactory", "_decorator", "eWaveConditionType", "eWaveActionType", "EventGroupBase", "Condition<PERSON><PERSON><PERSON>", "wave_cond", "wave_act", "ccclass", "property", "type", "emitter", "bullet", "<PERSON><PERSON><PERSON>", "wave", "reset", "buildConditions", "chain", "data", "conditions", "for<PERSON>ach", "condData", "index", "condition", "create", "onLoad", "context", "push", "buildActions", "actions", "map", "actionData", "action", "Spawn_Count", "WaveCondition_SpawnCount", "Player_Level", "WaveCondition_PlayerLevel", "Error", "Spawn_Interval", "WaveAction_SpawnInterval", "Spawn_Angle", "WaveAction_SpawnAngle"], "mappings": ";;;qLAWaA,qB,EAmBAC,c,EAqBPC,oB,EAaAC,iB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA3DGC,MAAAA,U,OAAAA,U;;AALuDC,MAAAA,kB,iBAAAA,kB;AAAoBC,MAAAA,e,iBAAAA,e;;AAE3EC,MAAAA,c,iBAAAA,c;;AACiBC,MAAAA,c,iBAAAA,c;;AAIdC,MAAAA,S;;AACAC,MAAAA,Q;;;;;;;;;OACN;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA8BT,U;;uCAEvBJ,qB,GAAN,MAAMA,qBAAN,CAA0D;AAAA;AAC7D;AAD6D,eAE7Dc,OAF6D,GAE7C,IAF6C;AAG7D;AAH6D,eAI7DC,MAJ6D,GAI9C,IAJ8C;AAAA,eAK7DC,WAL6D,GAKzC,IALyC;AAAA,eAO7DC,IAP6D,GAO3C,IAP2C;AAAA;;AAS7DC,QAAAA,KAAK,GAAG;AACJ,eAAKJ,OAAL,GAAe,IAAf;AACA,eAAKC,MAAL,GAAc,IAAd;AACA,eAAKC,WAAL,GAAmB,IAAnB;AACA,eAAKC,IAAL,GAAY,IAAZ;AACH;;AAd4D,O,GAiBjE;AACA;;;gCACahB,c,GAAN,MAAMA,cAAN;AAAA;AAAA,4CAAuF;AAChFkB,QAAAA,eAAe,GAAmB;AACxC,gBAAMC,KAAK,GAAG;AAAA;AAAA,iDAAd;AACA,eAAKC,IAAL,CAAUC,UAAV,CAAqBC,OAArB,CAA6B,CAACC,QAAD,EAAWC,KAAX,KAAqB;AAC9C,kBAAMC,SAAS,GAAGxB,oBAAoB,CAACyB,MAArB,CAA4BH,QAA5B,CAAlB;;AACA,gBAAIE,SAAJ,EAAe;AACXA,cAAAA,SAAS,CAACE,MAAV,CAAiB,KAAKC,OAAtB;AACAT,cAAAA,KAAK,CAACE,UAAN,CAAiBQ,IAAjB,CAAsBJ,SAAtB;AACH;AACJ,WAND;AAOA,iBAAON,KAAP;AACH;;AAESW,QAAAA,YAAY,GAAmB;AACrC,iBAAO,KAAKV,IAAL,CAAUW,OAAV,CAAkBC,GAAlB,CAAsBC,UAAU,IAAI;AACvC,gBAAIC,MAAM,GAAGhC,iBAAiB,CAACwB,MAAlB,CAAyBO,UAAzB,CAAb;AACA,mBAAOC,MAAP;AACH,WAHM,CAAP;AAIH;;AAlByF,O;;AAqBxFjC,MAAAA,oB,GAAN,MAAMA,oBAAN,CAA2B;AACV,eAANyB,MAAM,CAACN,IAAD,EAA2C;AACpD,kBAAQA,IAAI,CAACR,IAAb;AACI,iBAAK;AAAA;AAAA,0DAAmBuB,WAAxB;AACI,qBAAO,IAAI3B,SAAS,CAAC4B,wBAAd,CAAuChB,IAAvC,CAAP;;AACJ,iBAAK;AAAA;AAAA,0DAAmBiB,YAAxB;AACI,qBAAO,IAAI7B,SAAS,CAAC8B,yBAAd,CAAwClB,IAAxC,CAAP;;AACJ;AACI,oBAAM,IAAImB,KAAJ,CAAW,2BAA0BnB,IAAI,CAACR,IAAK,EAA/C,CAAN;AANR;AAQH;;AAVsB,O;AAarBV,MAAAA,iB,GAAN,MAAMA,iBAAN,CAAwB;AACP,eAANwB,MAAM,CAACN,IAAD,EAAqC;AAC9C,kBAAQA,IAAI,CAACR,IAAb;AACI,iBAAK;AAAA;AAAA,oDAAgB4B,cAArB;AACI,qBAAO,IAAI/B,QAAQ,CAACgC,wBAAb,CAAsCrB,IAAtC,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgBsB,WAArB;AACI,qBAAO,IAAIjC,QAAQ,CAACkC,qBAAb,CAAmCvB,IAAnC,CAAP;;AACJ;AACI,oBAAM,IAAImB,KAAJ,CAAW,wBAAuBnB,IAAI,CAACR,IAAK,EAA5C,CAAN;AANR;AAQH;;AAVmB,O", "sourcesContent": ["import { WaveConditionData, WaveEventGroupData, WaveActionData, eWaveConditionType, eWaveActionType } from \"../data/WaveData\";\r\nimport { IEventGroupContext } from \"db://assets/bundles/common/script/game/eventgroup/IEventGroupContext\";\r\nimport { EventGroupBase } from \"db://assets/bundles/common/script/game/eventgroup/IEventGroup\";\r\nimport { IEventCondition, ConditionChain } from \"db://assets/bundles/common/script/game/eventgroup/IEventCondition\";\r\nimport { IEventAction } from \"db://assets/bundles/common/script/game/eventgroup/IEventAction\";\r\nimport { _decorator, CCInteger } from \"cc\";\r\nimport { Wave } from \"./Wave\";\r\nimport * as wave_cond from \"./WaveEventConditions\";\r\nimport * as wave_act from \"./WaveEventActions\";\r\nconst { ccclass, property, type } = _decorator;\r\n\r\nexport class WaveEventGroupContext implements IEventGroupContext {\r\n    // 继承来的，在波次这里不使用\r\n    emitter: null = null;\r\n    // 继承来的，在波次这里不使用\r\n    bullet: null = null;\r\n    playerPlane: null = null;\r\n\r\n    wave: Wave|null = null;\r\n\r\n    reset() {\r\n        this.emitter = null;\r\n        this.bullet = null;\r\n        this.playerPlane = null;\r\n        this.wave = null;\r\n    }\r\n}\r\n\r\n/// Wave事件组\r\n/// 和子弹&发射器事件组主要差异在于数据源不同: WaveEventGroupData vs EventGroupData\r\nexport class WaveEventGroup extends EventGroupBase<WaveEventGroupContext, WaveEventGroupData> {\r\n    protected buildConditions(): ConditionChain {\r\n        const chain = new ConditionChain();\r\n        this.data.conditions.forEach((condData, index) => {\r\n            const condition = WaveConditionFactory.create(condData);\r\n            if (condition) {\r\n                condition.onLoad(this.context);\r\n                chain.conditions.push(condition);\r\n            }\r\n        });\r\n        return chain;\r\n    }\r\n\r\n    protected buildActions(): IEventAction[] {\r\n        return this.data.actions.map(actionData => {\r\n            let action = WaveActionFactory.create(actionData);\r\n            return action;\r\n        });\r\n    }\r\n}\r\n\r\nclass WaveConditionFactory {\r\n    static create(data: WaveConditionData): IEventCondition {\r\n        switch (data.type) {\r\n            case eWaveConditionType.Spawn_Count:\r\n                return new wave_cond.WaveCondition_SpawnCount(data);\r\n            case eWaveConditionType.Player_Level:\r\n                return new wave_cond.WaveCondition_PlayerLevel(data);\r\n            default: \r\n                throw new Error(`Unknown condition type: ${data.type}`);\r\n        }\r\n    }\r\n}\r\n\r\nclass WaveActionFactory {\r\n    static create(data: WaveActionData): IEventAction {\r\n        switch (data.type) {\r\n            case eWaveActionType.Spawn_Interval:\r\n                return new wave_act.WaveAction_SpawnInterval(data);\r\n            case eWaveActionType.Spawn_Angle:\r\n                return new wave_act.WaveAction_SpawnAngle(data);\r\n            default:\r\n                throw new Error(`Unknown action type: ${data.type}`);\r\n        }\r\n    }\r\n}"]}