{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/skill/BuffComp.ts"], "names": ["TriggerCondition", "Buff", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Vec2", "MyApp", "BoolOpType", "CondOPType", "SkillConditionType", "TargetType", "logInfo", "log<PERSON>arn", "BaseComp", "GameIns", "ExCondition", "ExConditionNum", "forEachEntityByTargetType", "constructor", "res", "isOutside", "data", "target", "id", "removeConditionRes", "undefined", "removeConditionElems", "forbinConditionRes", "forbinConditionElems", "triggerConditionRes", "triggerConditionElems", "time", "cycleTimes", "forbin", "stack", "incID", "removeCondition", "lubanTables", "TbResSkillCondition", "get", "conditionRes2Elems", "forbinCondition", "triggerCondition", "elems", "Array", "from", "conditions", "i", "length", "type", "<PERSON><PERSON><PERSON><PERSON>", "pickDiamond<PERSON>um", "KillEnemyNum", "killEnemyNum", "UseNuclear", "usedNuclearNum", "UserSuper", "usedSuperNum", "checkCondition", "self", "entity", "ret", "boolType", "AND", "for<PERSON>ach", "condition", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "params", "buff<PERSON><PERSON>p", "GetBuff", "CurHP<PERSON>er", "curHp", "maxHp", "CurHP", "MaxHP", "BeAttackTime", "battleManager", "_gameTime", "hurtTime", "num", "KillEnemy", "RemainNuclearNum", "nuclearNum", "UsedNuclearNum", "LevelStart", "BossBeKilled", "GameTime", "EnemyCount", "radiusSqr", "enemy", "squaredDistance", "node", "position", "mainPlaneManager", "mainPlane", "Enemy", "isDead", "WaveNo", "Distance", "distance", "ret2", "op", "EQ", "GE", "GT", "LE", "LT", "NE", "checkRemoveCondition", "duration", "resetTriggerCondition", "reset", "buffs", "Map", "A<PERSON><PERSON><PERSON><PERSON>", "buff<PERSON>", "buffD<PERSON>", "TbRes<PERSON>uffer", "buff", "set", "refreshType", "maxStack", "Math", "min", "effects", "applyEffect", "ApplyBuffEffect", "<PERSON><PERSON><PERSON>", "has", "updateGameLogic", "dt", "removeBuff", "RemoveBuffEffect", "cycle", "delete"], "mappings": ";;;iPAUMA,gB,EAOOC,I,EA6MQC,Q;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA9NCC,MAAAA,I,OAAAA,I;;AACbC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;AAAYC,MAAAA,U,iBAAAA,U;AAAiEC,MAAAA,kB,iBAAAA,kB;AAAoBC,MAAAA,U,iBAAAA,U;;AACjGC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,O,iBAAAA,O;;AACXC,MAAAA,Q;;AAEEC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,c,iBAAAA,c;;AACfC,MAAAA,yB;;;;;;;;;AAEDf,MAAAA,gB,GAAN,MAAMA,gBAAN,CAAuB;AAEnBgB,QAAAA,WAAW,CAACC,GAAD,EAAyB;AAAA,eADpCA,GACoC;AAChC,eAAKA,GAAL,GAAWA,GAAX;AACH;;AAJkB,O;;sBAOVhB,I,GAAN,MAAMA,IAAN,CAAW;AAede,QAAAA,WAAW,CAACE,SAAD,EAAqBC,IAArB,EAAsCC,MAAtC,EAAwD;AAAA,eAdnEC,EAcmE;AAAA,eAbnEJ,GAamE;AAAA,eAZnEK,kBAYmE,GAZjBC,SAYiB;AAAA,eAXnEC,oBAWmE,GAXd,IAWc;AAAA,eAVnEC,kBAUmE,GAVjBF,SAUiB;AAAA,eATnEG,oBASmE,GATd,IASc;AAAA,eARnEC,mBAQmE,GARhBJ,SAQgB;AAAA,eAPnEK,qBAOmE,GAPb,IAOa;AAAA,eANnEC,IAMmE,GAN5D,CAM4D;AAAA,eALnEC,UAKmE,GALtD,CAKsD;AAAA,eAJnEZ,SAImE;AAAA,eAHnEa,MAGmE,GAHlD,KAGkD;AAAA,eAFnEC,KAEmE,GAFpD,CAEoD;AAC/D,eAAKX,EAAL,GAAUpB,IAAI,CAACgC,KAAL,EAAV;AACA,eAAKhB,GAAL,GAAWE,IAAX;AACA,eAAKD,SAAL,GAAiBA,SAAjB;;AACA,cAAI,KAAKD,GAAL,CAASiB,eAAb,EAA8B;AAC1B,iBAAKZ,kBAAL,GAA0B;AAAA;AAAA,gCAAMa,WAAN,CAAkBC,mBAAlB,CAAsCC,GAAtC,CAA0C,KAAKpB,GAAL,CAASiB,eAAnD,CAA1B;AACA,iBAAKV,oBAAL,GAA4BvB,IAAI,CAACqC,kBAAL,CAAwBlB,MAAxB,EAAgC,KAAKE,kBAArC,CAA5B;AACH;;AACD,cAAI,KAAKL,GAAL,CAASsB,eAAb,EAA8B;AAC1B,iBAAKd,kBAAL,GAA0B;AAAA;AAAA,gCAAMU,WAAN,CAAkBC,mBAAlB,CAAsCC,GAAtC,CAA0C,KAAKpB,GAAL,CAASsB,eAAnD,CAA1B;AACA,iBAAKb,oBAAL,GAA4BzB,IAAI,CAACqC,kBAAL,CAAwBlB,MAAxB,EAAgC,KAAKK,kBAArC,CAA5B;AACH;;AACD,cAAI,KAAKR,GAAL,CAASuB,gBAAb,EAA+B;AAC3B,iBAAKb,mBAAL,GAA2B;AAAA;AAAA,gCAAMQ,WAAN,CAAkBC,mBAAlB,CAAsCC,GAAtC,CAA0C,KAAKpB,GAAL,CAASuB,gBAAnD,CAA3B;AACA,iBAAKZ,qBAAL,GAA6B3B,IAAI,CAACqC,kBAAL,CAAwBlB,MAAxB,EAAgC,KAAKO,mBAArC,CAA7B;AACH;AACJ;;AAEwB,eAAlBW,kBAAkB,CAAClB,MAAD,EAAoBH,GAApB,EAAsD;AAC3E,cAAI,CAACA,GAAL,EAAU;AACN,mBAAO,IAAP;AACH;;AACD,gBAAMwB,KAAK,GAAGC,KAAK,CAACC,IAAN,CAAW1B,GAAG,CAAC2B,UAAf,CAAd;;AACA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,KAAK,CAACK,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;AACnC,oBAAQJ,KAAK,CAACI,CAAD,CAAL,CAASE,IAAjB;AACI,mBAAK;AAAA;AAAA,4DAAmBC,WAAxB;AACIP,gBAAAA,KAAK,CAACI,CAAD,CAAL,GAAW;AAAA;AAAA,sDAAmBJ,KAAK,CAACI,CAAD,CAAxB,EAA6BzB,MAAM,CAAC6B,cAApC,CAAX;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBC,YAAxB;AACIT,gBAAAA,KAAK,CAACI,CAAD,CAAL,GAAW;AAAA;AAAA,sDAAmBJ,KAAK,CAACI,CAAD,CAAxB,EAA6BzB,MAAM,CAAC+B,YAApC,CAAX;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBC,UAAxB;AACIX,gBAAAA,KAAK,CAACI,CAAD,CAAL,GAAW;AAAA;AAAA,sDAAmBJ,KAAK,CAACI,CAAD,CAAxB,EAA6BzB,MAAM,CAACiC,cAApC,CAAX;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBC,SAAxB;AACIb,gBAAAA,KAAK,CAACI,CAAD,CAAL,GAAW;AAAA;AAAA,sDAAmBJ,KAAK,CAACI,CAAD,CAAxB,EAA6BzB,MAAM,CAACmC,YAApC,CAAX;AACA;AAZR;AAcH;;AACD,iBAAOtC,GAAG,CAAC2B,UAAX;AACH;;AAEoB,eAAdY,cAAc,CAACC,IAAD,EAAkBxC,GAAlB,EAA0CwB,KAA1C,EAA+E;AAChG,cAAIrB,MAAwB,GAAG,IAA/B;AACA;AAAA;AAAA,sEAA0BqC,IAA1B,EAAgCxC,GAAG,CAACG,MAApC,EAA6CsC,MAAD,IAAY;AACpDtC,YAAAA,MAAM,GAAGsC,MAAT;AACH,WAFD;;AAGA,cAAI,CAACtC,MAAL,EAAa;AACT,mBAAO,KAAP;AACH;;AACD,cAAIH,GAAG,CAAC2B,UAAJ,CAAeE,MAAf,IAAyB,CAA7B,EAAgC;AAC5B,mBAAO,IAAP;AACH;;AACD,cAAIa,GAAG,GAAG1C,GAAG,CAAC2C,QAAJ,IAAgB;AAAA;AAAA,wCAAWC,GAA3B,GAAiC,IAAjC,GAAwC,KAAlD;AACApB,UAAAA,KAAK,QAAL,IAAAA,KAAK,CAAEqB,OAAP,CAAgBC,SAAD,IAAe;AAC1B,gBAAIC,KAAK,GAAG,CAAZ;;AACA,oBAAOD,SAAS,CAAChB,IAAjB;AACI,mBAAK;AAAA;AAAA,4DAAmBkB,SAAxB;AACI;AAAA;;AACI,sBAAIF,SAAS,CAACG,MAAV,CAAiBpB,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B;AACH;;AACDkB,kBAAAA,KAAK,GAAG,aAAA5C,MAAM,CAAE+C,QAAR,CAAkBC,OAAlB,CAA0BL,SAAS,CAACG,MAAV,CAAiB,CAAjB,CAA1B,+BAAgDlC,KAAhD,KAAyD,CAAjE;AACH;AACD;;AACJ,mBAAK;AAAA;AAAA,4DAAmBqC,QAAxB;AACIL,gBAAAA,KAAK,GAAG5C,MAAM,CAAEkD,KAAR,GAAgBlD,MAAM,CAAEmD,KAAxB,GAAgC,KAAxC;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBC,KAAxB;AACIR,gBAAAA,KAAK,GAAG5C,MAAM,CAAEkD,KAAhB;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBG,KAAxB;AACIT,gBAAAA,KAAK,GAAG5C,MAAM,CAAEmD,KAAhB;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBG,YAAxB;AACIV,gBAAAA,KAAK,GAAG;AAAA;AAAA,wCAAQW,aAAR,CAAsBC,SAAtB,GAAkCxD,MAAM,CAAEyD,QAAlD;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmB7B,WAAxB;AACI,oBAAIe,SAAS;AAAA;AAAA,qDAAb,EAAyC;AACrCC,kBAAAA,KAAK,GAAG5C,MAAM,CAAE6B,cAAR,GAAyBc,SAAS,CAACe,GAA3C;AACH;;AACL,mBAAK;AAAA;AAAA,4DAAmB5B,YAAxB;AACA,mBAAK;AAAA;AAAA,4DAAmB6B,SAAxB;AACI,oBAAIhB,SAAS;AAAA;AAAA,qDAAb,EAAyC;AACrCC,kBAAAA,KAAK,GAAG5C,MAAM,CAAE+B,YAAR,GAAuBY,SAAS,CAACe,GAAzC;AACH;;AACD;;AACJ,mBAAK;AAAA;AAAA,4DAAmBE,gBAAxB;AACIhB,gBAAAA,KAAK,GAAG5C,MAAM,CAAE6D,UAAhB;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBC,cAAxB;AACIlB,gBAAAA,KAAK,GAAG5C,MAAM,CAAEiC,cAAhB;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmB8B,UAAxB;AACInB,gBAAAA,KAAK,GAAG;AAAA;AAAA,wCAAQW,aAAR,CAAsBC,SAAtB,IAAmC,CAAnC,GAAuC,CAAvC,GAA2C,CAAnD;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBQ,YAAxB;AACI;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBhC,UAAxB;AACI,oBAAIW,SAAS;AAAA;AAAA,qDAAb,EAAyC;AACrCC,kBAAAA,KAAK,GAAG5C,MAAM,CAAEiC,cAAR,GAAyBU,SAAS,CAACe,GAA3C;AACH;;AACL,mBAAK;AAAA;AAAA,4DAAmBxB,SAAxB;AACI,oBAAIS,SAAS;AAAA;AAAA,qDAAb,EAAyC;AACrCC,kBAAAA,KAAK,GAAG5C,MAAM,CAAEmC,YAAR,GAAuBQ,SAAS,CAACe,GAAzC;AACH;;AACD;;AACJ,mBAAK;AAAA;AAAA,4DAAmBO,QAAxB;AACIrB,gBAAAA,KAAK,GAAG;AAAA;AAAA,wCAAQW,aAAR,CAAsBC,SAA9B;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBU,UAAxB;AACI,oBAAIvB,SAAS,CAACG,MAAV,CAAiBpB,MAAjB,IAA2B,CAA/B,EAAkC;AAAA;;AAC9B,wBAAMyC,SAAS,GAAGxB,SAAS,CAACG,MAAV,CAAiB,CAAjB,IAAoBH,SAAS,CAACG,MAAV,CAAiB,CAAjB,CAAtC;;AACA,iCAAI9C,MAAJ,aAAI,QAAQoE,KAAZ,EAAmB;AACf,wBAAIrF,IAAI,CAACsF,eAAL,CAAqBrE,MAAM,CAAEsE,IAAR,CAAaC,QAAlC,EAA4C;AAAA;AAAA,4CAAQC,gBAAR,CAAyBC,SAAzB,CAAoCH,IAApC,CAAyCC,QAArF,KAAkGJ,SAAtG,EAAiH;AAC7GvB,sBAAAA,KAAK;AACR;AACJ,mBAJD,MAIO;AACH;AAAA;AAAA,gFAA0B5C,MAA1B,EAAmC;AAAA;AAAA,kDAAW0E,KAA9C,EAAsDpC,MAAD,IAAY;AAC7D,0BAAIA,MAAM,CAACqC,MAAP,IAAiB5F,IAAI,CAACsF,eAAL,CAAqB/B,MAAM,CAACgC,IAAP,CAAYC,QAAjC,EAA2CvE,MAAM,CAAEsE,IAAR,CAAaC,QAAxD,KAAqEJ,SAA1F,EAAqG;AACjGvB,wBAAAA,KAAK;AACR;AACJ,qBAJD;AAKH;AACJ;;AACD;;AACJ,mBAAK;AAAA;AAAA,4DAAmBgC,MAAxB;AACI;AACA;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBC,QAAxB;AACIjC,gBAAAA,KAAK,GAAG7D,IAAI,CAAC+F,QAAL,CAAc9E,MAAM,CAAEsE,IAAR,CAAaC,QAA3B,EAAqC;AAAA;AAAA,wCAAQC,gBAAR,CAAyBC,SAAzB,CAAoCH,IAApC,CAAyCC,QAA9E,CAAR;AACA;;AACJ,mBAAK;AAAA;AAAA,4DAAmBZ,SAAxB;AACI,oBAAIhB,SAAS;AAAA;AAAA,qDAAb,EAAyC;AACrCC,kBAAAA,KAAK,GAAG5C,MAAM,CAAE+B,YAAR,GAAuBY,SAAS,CAACe,GAAzC;AACH;;AACD;AAlFR;;AAqFA,gBAAIqB,IAAI,GAAG,KAAX;;AACA,oBAAOpC,SAAS,CAACqC,EAAjB;AACI,mBAAK;AAAA;AAAA,4CAAWC,EAAhB;AACIF,gBAAAA,IAAI,GAAGnC,KAAK,IAAID,SAAS,CAACC,KAA1B;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAWsC,EAAhB;AACIH,gBAAAA,IAAI,GAAGnC,KAAK,IAAID,SAAS,CAACC,KAA1B;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAWuC,EAAhB;AACIJ,gBAAAA,IAAI,GAAGnC,KAAK,GAAGD,SAAS,CAACC,KAAzB;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAWwC,EAAhB;AACIL,gBAAAA,IAAI,GAAGnC,KAAK,IAAID,SAAS,CAACC,KAA1B;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAWyC,EAAhB;AACIN,gBAAAA,IAAI,GAAGnC,KAAK,GAAGD,SAAS,CAACC,KAAzB;AACA;;AACJ,mBAAK;AAAA;AAAA,4CAAW0C,EAAhB;AACIP,gBAAAA,IAAI,GAAGnC,KAAK,IAAID,SAAS,CAACC,KAA1B;AACA;AAlBR;;AAoBA,gBAAI/C,GAAG,CAAC2C,QAAJ,IAAgB;AAAA;AAAA,0CAAWC,GAA/B,EAAoC;AAChCF,cAAAA,GAAG,GAAGA,GAAG,IAAIwC,IAAb;AACH,aAFD,MAEO;AACHxC,cAAAA,GAAG,GAAGA,GAAG,IAAIwC,IAAb;AACH;AACJ,WAjHD;AAmHA,iBAAOxC,GAAP;AACH;;AAEDgD,QAAAA,oBAAoB,CAAClD,IAAD,EAAkB;AAClC,cAAI,KAAKxC,GAAL,CAAS2F,QAAT,IAAqB,CAAC,CAAtB,IAA2B,KAAK/E,IAAL,IAAa,KAAKZ,GAAL,CAAS2F,QAArD,EAA+D;AAC3D,mBAAO,IAAP;AACH;;AACD,cAAI,CAAC,KAAKtF,kBAAV,EAA8B;AAC1B,mBAAO,KAAP;AACH;;AACD,iBAAOrB,IAAI,CAACuD,cAAL,CAAoBC,IAApB,EAA0B,KAAKnC,kBAA/B,EAAmD,KAAKE,oBAAxD,CAAP;AACH;;AACDqF,QAAAA,qBAAqB,GAAG;AAAA;;AACpB,wCAAKjF,qBAAL,mCAA4BkC,OAA5B,CAAqCC,SAAD,IAAe;AAC/C,gBAAIA,SAAS;AAAA;AAAA,2CAAb,EAAsC;AAClCA,cAAAA,SAAS,CAAC+C,KAAV;AACH;AACJ,WAJD;AAKH;;AA1Ma,O;;AAAL7G,MAAAA,I,CAcFgC,K,GAAQ,C;;yBA+LE/B,Q,GAAN,MAAMA,QAAN;AAAA;AAAA,gCAAgC;AAAA;AAAA;AAAA,eACnC6G,KADmC,GACR,IAAIC,GAAJ,EADQ;AAAA;;AAG3CC,QAAAA,SAAS,CAAC/F,SAAD,EAAoBgG,MAApB,EAAoC;AACzC;AAAA;AAAA,kCAAQ,MAAR,EAAiB,cAAaA,MAAO,EAArC;AACA,cAAIC,QAAQ,GAAG;AAAA;AAAA,8BAAMhF,WAAN,CAAkBiF,WAAlB,CAA8B/E,GAA9B,CAAkC6E,MAAlC,CAAf;;AACA,cAAI,CAACC,QAAL,EAAe;AACX;AAAA;AAAA,oCAAQ,MAAR,EAAiB,cAAaD,MAAO,uBAArC;AACA;AACH;;AACD,gBAAM5F,kBAAkB,GAAG6F,QAAQ,CAACjF,eAAT,GAA2B;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,mBAAlB,CAAsCC,GAAtC,CAA0C8E,QAAQ,CAACjF,eAAnD,CAA3B,GAAiGX,SAA5H;;AACA,cAAID,kBAAJ,EAAwB;AACpB,gBAAIrB,IAAI,CAACuD,cAAL,CAAoB,KAAKE,MAAzB,EAA8CpC,kBAA9C,EAAkEA,kBAAkB,CAACsB,UAArF,CAAJ,EAAsG;AAClG;AACH;AACJ;;AACD,cAAIyE,IAAiB,GAAG,IAAxB;;AACA,cAAIF,QAAQ,CAACP,QAAT,IAAqB,CAAzB,EAA4B;AACxB,gBAAIS,IAAI,GAAG,KAAKN,KAAL,CAAW1E,GAAX,CAAe6E,MAAf,CAAX;;AACA,gBAAI,CAACG,IAAL,EAAW;AACPA,cAAAA,IAAI,GAAG,IAAIpH,IAAJ,CAASiB,SAAT,EAAoBiG,QAApB,EAA8B,KAAKzD,MAAnC,CAAP;AACA,mBAAKqD,KAAL,CAAWO,GAAX,CAAeJ,MAAf,EAAuBG,IAAvB;AACH;;AACD,gBAAIF,QAAQ,CAACI,WAAT,IAAwBJ,QAAQ,CAACP,QAAT,IAAqB,CAAC,CAAlD,EAAqD;AACjDS,cAAAA,IAAI,CAACxF,IAAL,GAAY,CAAZ;AACH;;AACD,gBAAIG,KAAK,GAAGmF,QAAQ,CAACK,QAAT,GAAoB,CAApB,GAAwB,CAAxB,GAA4BL,QAAQ,CAACK,QAAjD;AACAH,YAAAA,IAAI,CAACrF,KAAL,GAAayF,IAAI,CAACC,GAAL,CAAS1F,KAAT,EAAgBqF,IAAI,CAACrF,KAAL,GAAa,CAA7B,CAAb;AACH;;AACDmF,UAAAA,QAAQ,CAACQ,OAAT,CAAiB7D,OAAjB,CAA0B8D,WAAD,IAAiB;AACtC;AAAA;AAAA,wEAA0B,KAAKlE,MAA/B,EAAoDkE,WAAW,CAACxG,MAAhE,EAAyEsC,MAAD,IAAY;AAChFA,cAAAA,MAAM,CAACmE,eAAP,CAAuBR,IAAvB,EAA6BO,WAA7B;AACH,aAFD;AAGH,WAJD;AAKH;;AAEDE,QAAAA,OAAO,CAACZ,MAAD,EAAiB;AACpB,iBAAO,KAAKH,KAAL,CAAWgB,GAAX,CAAeb,MAAf,CAAP;AACH;;AAED9C,QAAAA,OAAO,CAAC8C,MAAD,EAAiB;AACpB,iBAAO,KAAKH,KAAL,CAAW1E,GAAX,CAAe6E,MAAf,CAAP;AACH;;AAEDc,QAAAA,eAAe,CAACC,EAAD,EAAmB;AAC9B,eAAKlB,KAAL,CAAWjD,OAAX,CAAmB,CAACuD,IAAD,EAAOH,MAAP,KAAkB;AACjCG,YAAAA,IAAI,CAACxF,IAAL,IAAaoG,EAAE,GAAG,IAAlB;;AACA,gBAAIZ,IAAI,CAACV,oBAAL,CAA0B,KAAKjD,MAA/B,CAAJ,EAAyD;AACrD,mBAAKwE,UAAL,CAAgBb,IAAhB,EAAsBH,MAAtB;AACH;;AACD,gBAAIG,IAAI,CAAC5F,kBAAL,IAA2BxB,IAAI,CAACuD,cAAL,CAAoB,KAAKE,MAAzB,EAA8C2D,IAAI,CAAC5F,kBAAnD,EAAuE4F,IAAI,CAAC3F,oBAA5E,CAA/B,EAAkI;AAC9H,kBAAI,CAAC2F,IAAI,CAACtF,MAAV,EAAkB;AACdsF,gBAAAA,IAAI,CAACpG,GAAL,CAAS0G,OAAT,CAAiB7D,OAAjB,CAA0B8D,WAAD,IAAiB;AACtC;AAAA;AAAA,8EAA0B,KAAKlE,MAA/B,EAAoDkE,WAAW,CAACxG,MAAhE,EAAyEsC,MAAD,IAAY;AAChFA,oBAAAA,MAAM,CAACyE,gBAAP,CAAwBd,IAAxB,EAA8BO,WAA9B;AACH,mBAFD;AAGH,iBAJD;AAKH;;AACDP,cAAAA,IAAI,CAACtF,MAAL,GAAc,IAAd;AACH,aATD,MASO;AACH,kBAAIsF,IAAI,CAACtF,MAAT,EAAiB;AACbsF,gBAAAA,IAAI,CAACpG,GAAL,CAAS0G,OAAT,CAAiB7D,OAAjB,CAA0B8D,WAAD,IAAiB;AACtC;AAAA;AAAA,8EAA0B,KAAKlE,MAA/B,EAAoDkE,WAAW,CAACxG,MAAhE,EAAyEsC,MAAD,IAAY;AAChFA,oBAAAA,MAAM,CAACmE,eAAP,CAAuBR,IAAvB,EAA6BO,WAA7B;AACH,mBAFD;AAGH,iBAJD;AAKH;;AACDP,cAAAA,IAAI,CAACtF,MAAL,GAAc,KAAd;AACH;;AAED,gBAAKsF,IAAI,CAACpG,GAAL,CAASmH,KAAT,GAAiB,CAAjB,IACGf,IAAI,CAACxF,IAAL,IAAa,CAACwF,IAAI,CAACvF,UAAL,GAAkB,CAAnB,IAAwBuF,IAAI,CAACpG,GAAL,CAASmH,KADjD,KAEIf,IAAI,CAACpG,GAAL,CAASa,UAAT,IAAuB,CAAvB,IAA4BuF,IAAI,CAACvF,UAAL,GAAkBuF,IAAI,CAACpG,GAAL,CAASa,UAF3D,CAAD,IAGIuF,IAAI,CAACzF,qBAAL,IAA8B3B,IAAI,CAACuD,cAAL,CAAoB,KAAKE,MAAzB,EAA8C2D,IAAI,CAAC1F,mBAAnD,EAAyE0F,IAAI,CAACzF,qBAA9E,CAHtC,EAKE;AACEyF,cAAAA,IAAI,CAACvF,UAAL;AACAuF,cAAAA,IAAI,CAACR,qBAAL;AACAQ,cAAAA,IAAI,CAACpG,GAAL,CAAS0G,OAAT,CAAiB7D,OAAjB,CAA0B8D,WAAD,IAAiB;AACtC;AAAA;AAAA,4EAA0B,KAAKlE,MAA/B,EAAoDkE,WAAW,CAACxG,MAAhE,EAAyEsC,MAAD,IAAY;AAChFA,kBAAAA,MAAM,CAACmE,eAAP,CAAuBR,IAAvB,EAA6BO,WAA7B;AACH,iBAFD;AAGH,eAJD;AAKH;AACJ,WAvCD;AAwCH;;AAEOM,QAAAA,UAAU,CAACb,IAAD,EAAaH,MAAb,EAA6B;AAC3CG,UAAAA,IAAI,CAACpG,GAAL,CAAS0G,OAAT,CAAiB7D,OAAjB,CAA0B8D,WAAD,IAAiB;AACtC;AACA;AACA;AACA;AAAA;AAAA,wEAA0B,KAAKlE,MAA/B,EAAoDkE,WAAW,CAACxG,MAAhE,EAAyEsC,MAAD,IAAY;AAChFA,cAAAA,MAAM,CAACyE,gBAAP,CAAwBd,IAAxB,EAA8BO,WAA9B;AACH,aAFD;AAGH,WAPD;AAQA,eAAKb,KAAL,CAAWsB,MAAX,CAAkBnB,MAAlB;AACH;;AAjG0C,O", "sourcesContent": ["import { randomRange, Vec2 } from \"cc\";\r\nimport { MyApp } from \"db://assets/bundles/common/script/app/MyApp\";\r\nimport { BoolOpType, CondOPType, ResBuffer, ResSkillCondition, ResSkillConditionElem, SkillConditionType, TargetType } from \"db://assets/bundles/common/script/autogen/luban/schema\";\r\nimport { logInfo, logWarn } from \"db://assets/scripts/utils/Logger\";\r\nimport BaseComp from \"db://assets/bundles/common/script/game/ui/base/BaseComp\";\r\nimport type PlaneBase from \"db://assets/bundles/common/script/game/ui/plane/PlaneBase\";\r\nimport { GameIns } from \"db://assets/bundles/common/script/game/GameIns\";\r\nimport { ExCondition, ExConditionNum } from \"./ExCondition\";\r\nimport forEachEntityByTargetType from \"./SearchTarget\";\r\n\r\nclass TriggerCondition {\r\n    res: ResSkillCondition\r\n    constructor(res: ResSkillCondition) {\r\n        this.res = res;\r\n    }\r\n}\r\n\r\nexport class Buff {\r\n    id: number;\r\n    res: ResBuffer\r\n    removeConditionRes: ResSkillCondition|undefined = undefined\r\n    removeConditionElems: ResSkillConditionElem[]|null = null\r\n    forbinConditionRes: ResSkillCondition|undefined = undefined\r\n    forbinConditionElems: ResSkillConditionElem[]|null = null\r\n    triggerConditionRes: ResSkillCondition|undefined = undefined\r\n    triggerConditionElems: ResSkillConditionElem[]|null = null\r\n    time = 0;\r\n    cycleTimes = 0;\r\n    isOutside: boolean;\r\n    forbin:boolean = false;\r\n    stack:number = 0;\r\n    static incID = 1;\r\n    constructor(isOutside: boolean, data: ResBuffer, target:PlaneBase) {\r\n        this.id = Buff.incID++;\r\n        this.res = data;\r\n        this.isOutside = isOutside;\r\n        if (this.res.removeCondition) {\r\n            this.removeConditionRes = MyApp.lubanTables.TbResSkillCondition.get(this.res.removeCondition);\r\n            this.removeConditionElems = Buff.conditionRes2Elems(target, this.removeConditionRes);\r\n        }\r\n        if (this.res.forbinCondition) {\r\n            this.forbinConditionRes = MyApp.lubanTables.TbResSkillCondition.get(this.res.forbinCondition);\r\n            this.forbinConditionElems = Buff.conditionRes2Elems(target, this.forbinConditionRes);\r\n        }\r\n        if (this.res.triggerCondition) {\r\n            this.triggerConditionRes = MyApp.lubanTables.TbResSkillCondition.get(this.res.triggerCondition);\r\n            this.triggerConditionElems = Buff.conditionRes2Elems(target, this.triggerConditionRes);\r\n        }\r\n    }\r\n\r\n    static conditionRes2Elems(target: PlaneBase, res: ResSkillCondition|undefined) {\r\n        if (!res) {\r\n            return null;\r\n        }\r\n        const elems = Array.from(res.conditions);\r\n        for (let i = 0; i < elems.length; i++) {\r\n            switch (elems[i].type) {\r\n                case SkillConditionType.PickDiamond:\r\n                    elems[i] = new ExConditionNum(elems[i], target.pickDiamondNum);\r\n                    break;\r\n                case SkillConditionType.KillEnemyNum:\r\n                    elems[i] = new ExConditionNum(elems[i], target.killEnemyNum);\r\n                    break;\r\n                case SkillConditionType.UseNuclear:\r\n                    elems[i] = new ExConditionNum(elems[i], target.usedNuclearNum);\r\n                    break;\r\n                case SkillConditionType.UserSuper:\r\n                    elems[i] = new ExConditionNum(elems[i], target.usedSuperNum);\r\n                    break;\r\n            }\r\n        }\r\n        return res.conditions;\r\n    }\r\n\r\n    static checkCondition(self: PlaneBase, res: ResSkillCondition, elems: ResSkillConditionElem[]|null) {\r\n        let target: PlaneBase | null = null;\r\n        forEachEntityByTargetType(self, res.target, (entity) => {\r\n            target = entity;\r\n        });\r\n        if (!target) {\r\n            return false\r\n        }\r\n        if (res.conditions.length == 0) {\r\n            return true;\r\n        }\r\n        let ret = res.boolType == BoolOpType.AND ? true : false;\r\n        elems?.forEach((condition) => {\r\n            let value = 0;\r\n            switch(condition.type) {\r\n                case SkillConditionType.BuffStack:\r\n                    {\r\n                        if (condition.params.length < 1) {\r\n                            break\r\n                        }\r\n                        value = target!.buffComp!.GetBuff(condition.params[0])?.stack || 0;\r\n                    }\r\n                    break\r\n                case SkillConditionType.CurHPPer:\r\n                    value = target!.curHp / target!.maxHp * 10000;\r\n                    break;\r\n                case SkillConditionType.CurHP:\r\n                    value = target!.curHp;\r\n                    break;\r\n                case SkillConditionType.MaxHP:\r\n                    value = target!.maxHp;\r\n                    break;\r\n                case SkillConditionType.BeAttackTime:\r\n                    value = GameIns.battleManager._gameTime - target!.hurtTime;\r\n                    break;\r\n                case SkillConditionType.PickDiamond:\r\n                    if (condition instanceof ExConditionNum) {\r\n                        value = target!.pickDiamondNum - condition.num;\r\n                    }\r\n                case SkillConditionType.KillEnemyNum:\r\n                case SkillConditionType.KillEnemy:\r\n                    if (condition instanceof ExConditionNum) {\r\n                        value = target!.killEnemyNum - condition.num;\r\n                    }\r\n                    break;\r\n                case SkillConditionType.RemainNuclearNum:\r\n                    value = target!.nuclearNum;\r\n                    break;\r\n                case SkillConditionType.UsedNuclearNum:\r\n                    value = target!.usedNuclearNum;\r\n                    break;\r\n                case SkillConditionType.LevelStart:\r\n                    value = GameIns.battleManager._gameTime == 0 ? 1 : 0;\r\n                    break;\r\n                case SkillConditionType.BossBeKilled:\r\n                    // TODO ybgg\r\n                    break;\r\n                case SkillConditionType.UseNuclear:\r\n                    if (condition instanceof ExConditionNum) {\r\n                        value = target!.usedNuclearNum - condition.num;\r\n                    }\r\n                case SkillConditionType.UserSuper:\r\n                    if (condition instanceof ExConditionNum) {\r\n                        value = target!.usedSuperNum - condition.num;\r\n                    }\r\n                    break;\r\n                case SkillConditionType.GameTime:\r\n                    value = GameIns.battleManager._gameTime;\r\n                    break;\r\n                case SkillConditionType.EnemyCount:\r\n                    if (condition.params.length >= 2) {\r\n                        const radiusSqr = condition.params[1]*condition.params[1];\r\n                        if (target?.enemy) {\r\n                            if (Vec2.squaredDistance(target!.node.position, GameIns.mainPlaneManager.mainPlane!.node.position) <= radiusSqr) {\r\n                                value++;\r\n                            }\r\n                        } else {\r\n                            forEachEntityByTargetType(target!, TargetType.Enemy, (entity) => {\r\n                                if (entity.isDead && Vec2.squaredDistance(entity.node.position, target!.node.position) <= radiusSqr) {\r\n                                    value++;\r\n                                }\r\n                            });\r\n                        }\r\n                    }\r\n                    break;\r\n                case SkillConditionType.WaveNo:\r\n                    // TODO ybgg\r\n                    // value = GameIns.battleManager.waveNo;\r\n                    break;\r\n                case SkillConditionType.Distance:\r\n                    value = Vec2.distance(target!.node.position, GameIns.mainPlaneManager.mainPlane!.node.position);\r\n                    break;\r\n                case SkillConditionType.KillEnemy:\r\n                    if (condition instanceof ExConditionNum) {\r\n                        value = target!.killEnemyNum - condition.num;\r\n                    }\r\n                    break;\r\n\r\n            }\r\n            let ret2 = false;\r\n            switch(condition.op) {\r\n                case CondOPType.EQ:\r\n                    ret2 = value == condition.value;\r\n                    break;\r\n                case CondOPType.GE:\r\n                    ret2 = value >= condition.value;\r\n                    break;\r\n                case CondOPType.GT:\r\n                    ret2 = value > condition.value;\r\n                    break;\r\n                case CondOPType.LE:\r\n                    ret2 = value <= condition.value;\r\n                    break;\r\n                case CondOPType.LT:\r\n                    ret2 = value < condition.value;\r\n                    break;\r\n                case CondOPType.NE:\r\n                    ret2 = value != condition.value;\r\n                    break;\r\n            }\r\n            if (res.boolType == BoolOpType.AND) {\r\n                ret = ret && ret2;\r\n            } else {\r\n                ret = ret || ret2;\r\n            }\r\n        })\r\n        \r\n        return ret;\r\n    }\r\n\r\n    checkRemoveCondition(self: PlaneBase) {\r\n        if (this.res.duration != -1 && this.time >= this.res.duration) {\r\n            return true;\r\n        }\r\n        if (!this.removeConditionRes) {\r\n            return false;\r\n        }\r\n        return Buff.checkCondition(self, this.removeConditionRes, this.removeConditionElems);\r\n    }\r\n    resetTriggerCondition() {\r\n        this.triggerConditionElems?.forEach((condition) => {\r\n            if (condition instanceof ExCondition) {\r\n                condition.reset()\r\n            }\r\n        })\r\n    }\r\n}\r\n\r\nexport default class BuffComp extends BaseComp {\r\n    private buffs: Map<number, Buff> = new Map();\r\n\r\n    ApplyBuff(isOutside:boolean, buffID: number) {\r\n        logInfo(\"Buff\", `apply buff ${buffID}`)\r\n        let buffData = MyApp.lubanTables.TbResBuffer.get(buffID);\r\n        if (!buffData) {\r\n            logWarn(\"Buff\", `apply buff ${buffID} but config not found`)\r\n            return;\r\n        }\r\n        const removeConditionRes = buffData.removeCondition ? MyApp.lubanTables.TbResSkillCondition.get(buffData.removeCondition) : undefined;\r\n        if (removeConditionRes) {\r\n            if (Buff.checkCondition(this.entity as PlaneBase, removeConditionRes, removeConditionRes.conditions)) {\r\n                return;\r\n            }\r\n        }\r\n        let buff: Buff | null = null\r\n        if (buffData.duration != 0) {\r\n            let buff = this.buffs.get(buffID)\r\n            if (!buff) {\r\n                buff = new Buff(isOutside, buffData, this.entity as PlaneBase)!;\r\n                this.buffs.set(buffID, buff);\r\n            }\r\n            if (buffData.refreshType && buffData.duration != -1) {\r\n                buff.time = 0;\r\n            }\r\n            let stack = buffData.maxStack < 1 ? 1 : buffData.maxStack;\r\n            buff.stack = Math.min(stack, buff.stack + 1);\r\n        }\r\n        buffData.effects.forEach((applyEffect) => {\r\n            forEachEntityByTargetType(this.entity as PlaneBase, applyEffect.target, (entity) => {\r\n                entity.ApplyBuffEffect(buff, applyEffect);\r\n            })\r\n        })\r\n    }\r\n\r\n    HasBuff(buffID: number) {\r\n        return this.buffs.has(buffID);\r\n    }\r\n\r\n    GetBuff(buffID: number) {\r\n        return this.buffs.get(buffID);\r\n    }\r\n\r\n    updateGameLogic(dt: number): void {\r\n        this.buffs.forEach((buff, buffID) => {\r\n            buff.time += dt * 1000;\r\n            if (buff.checkRemoveCondition(this.entity as PlaneBase)) {\r\n                this.removeBuff(buff, buffID);\r\n            }\r\n            if (buff.forbinConditionRes && Buff.checkCondition(this.entity as PlaneBase, buff.forbinConditionRes, buff.forbinConditionElems)) {\r\n                if (!buff.forbin) {\r\n                    buff.res.effects.forEach((applyEffect) => {\r\n                        forEachEntityByTargetType(this.entity as PlaneBase, applyEffect.target, (entity) => {\r\n                            entity.RemoveBuffEffect(buff, applyEffect);\r\n                        })\r\n                    })\r\n                }\r\n                buff.forbin = true;\r\n            } else {\r\n                if (buff.forbin) {\r\n                    buff.res.effects.forEach((applyEffect) => {\r\n                        forEachEntityByTargetType(this.entity as PlaneBase, applyEffect.target, (entity) => {\r\n                            entity.ApplyBuffEffect(buff, applyEffect);\r\n                        })\r\n                    })\r\n                }\r\n                buff.forbin = false;\r\n            }\r\n            \r\n            if ((buff.res.cycle > 0 &&\r\n                    buff.time >= (buff.cycleTimes + 1) * buff.res.cycle &&\r\n                    (buff.res.cycleTimes == 0 || buff.cycleTimes < buff.res.cycleTimes))\r\n                || (buff.triggerConditionElems && Buff.checkCondition(this.entity as PlaneBase, buff.triggerConditionRes!, buff.triggerConditionElems)\r\n                )\r\n            ) {\r\n                buff.cycleTimes++;\r\n                buff.resetTriggerCondition();\r\n                buff.res.effects.forEach((applyEffect) => {\r\n                    forEachEntityByTargetType(this.entity as PlaneBase, applyEffect.target, (entity) => {\r\n                        entity.ApplyBuffEffect(buff, applyEffect);\r\n                    })\r\n                })\r\n            }\r\n        })\r\n    }\r\n\r\n    private removeBuff(buff: Buff, buffID: number) {\r\n        buff.res.effects.forEach((applyEffect) => {\r\n            // 这个地方和加的时候查出来的target会不同\r\n            // 1. 需要保证查出来的target只多不少\r\n            // 2. remove接口里面需要判断时候是这个buff的效果\r\n            forEachEntityByTargetType(this.entity as PlaneBase, applyEffect.target, (entity) => {\r\n                entity.RemoveBuffEffect(buff, applyEffect);\r\n            })\r\n        })\r\n        this.buffs.delete(buffID);\r\n    }\r\n}"]}