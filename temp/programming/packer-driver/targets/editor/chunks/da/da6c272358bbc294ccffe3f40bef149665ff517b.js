System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Vec2, MyApp, BoolOpType, CondOPType, SkillConditionType, TargetType, logInfo, logWarn, BaseComp, GameIns, ExCondition, ExConditionNum, forEachEntityByTargetType, TriggerCondition, Buff, BuffComp, _crd;

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBoolOpType(extras) {
    _reporterNs.report("BoolOpType", "db://assets/bundles/common/script/autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfCondOPType(extras) {
    _reporterNs.report("CondOPType", "db://assets/bundles/common/script/autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResBuffer(extras) {
    _reporterNs.report("ResBuffer", "db://assets/bundles/common/script/autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResSkillCondition(extras) {
    _reporterNs.report("ResSkillCondition", "db://assets/bundles/common/script/autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResSkillConditionElem(extras) {
    _reporterNs.report("ResSkillConditionElem", "db://assets/bundles/common/script/autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSkillConditionType(extras) {
    _reporterNs.report("SkillConditionType", "db://assets/bundles/common/script/autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTargetType(extras) {
    _reporterNs.report("TargetType", "db://assets/bundles/common/script/autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogInfo(extras) {
    _reporterNs.report("logInfo", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogWarn(extras) {
    _reporterNs.report("logWarn", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseComp(extras) {
    _reporterNs.report("BaseComp", "db://assets/bundles/common/script/game/ui/base/BaseComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "db://assets/bundles/common/script/game/ui/plane/PlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "db://assets/bundles/common/script/game/GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfExCondition(extras) {
    _reporterNs.report("ExCondition", "./ExCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfExConditionNum(extras) {
    _reporterNs.report("ExConditionNum", "./ExCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfforEachEntityByTargetType(extras) {
    _reporterNs.report("forEachEntityByTargetType", "./SearchTarget", _context.meta, extras);
  }

  _export({
    Buff: void 0,
    default: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Vec2 = _cc.Vec2;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      BoolOpType = _unresolved_3.BoolOpType;
      CondOPType = _unresolved_3.CondOPType;
      SkillConditionType = _unresolved_3.SkillConditionType;
      TargetType = _unresolved_3.TargetType;
    }, function (_unresolved_4) {
      logInfo = _unresolved_4.logInfo;
      logWarn = _unresolved_4.logWarn;
    }, function (_unresolved_5) {
      BaseComp = _unresolved_5.default;
    }, function (_unresolved_6) {
      GameIns = _unresolved_6.GameIns;
    }, function (_unresolved_7) {
      ExCondition = _unresolved_7.ExCondition;
      ExConditionNum = _unresolved_7.ExConditionNum;
    }, function (_unresolved_8) {
      forEachEntityByTargetType = _unresolved_8.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "43149/j3FZB+pqbrElL7T/l", "BuffComp", undefined);

      __checkObsolete__(['randomRange', 'Vec2']);

      TriggerCondition = class TriggerCondition {
        constructor(res) {
          this.res = void 0;
          this.res = res;
        }

      };

      _export("Buff", Buff = class Buff {
        constructor(isOutside, data, target) {
          this.id = void 0;
          this.res = void 0;
          this.removeConditionRes = undefined;
          this.removeConditionElems = null;
          this.forbinConditionRes = undefined;
          this.forbinConditionElems = null;
          this.triggerConditionRes = undefined;
          this.triggerConditionElems = null;
          this.time = 0;
          this.cycleTimes = 0;
          this.isOutside = void 0;
          this.forbin = false;
          this.stack = 0;
          this.id = Buff.incID++;
          this.res = data;
          this.isOutside = isOutside;

          if (this.res.removeCondition) {
            this.removeConditionRes = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbResSkillCondition.get(this.res.removeCondition);
            this.removeConditionElems = Buff.conditionRes2Elems(target, this.removeConditionRes);
          }

          if (this.res.forbinCondition) {
            this.forbinConditionRes = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbResSkillCondition.get(this.res.forbinCondition);
            this.forbinConditionElems = Buff.conditionRes2Elems(target, this.forbinConditionRes);
          }

          if (this.res.triggerCondition) {
            this.triggerConditionRes = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbResSkillCondition.get(this.res.triggerCondition);
            this.triggerConditionElems = Buff.conditionRes2Elems(target, this.triggerConditionRes);
          }
        }

        static conditionRes2Elems(target, res) {
          if (!res) {
            return null;
          }

          const elems = Array.from(res.conditions);

          for (let i = 0; i < elems.length; i++) {
            switch (elems[i].type) {
              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).PickDiamond:
                elems[i] = new (_crd && ExConditionNum === void 0 ? (_reportPossibleCrUseOfExConditionNum({
                  error: Error()
                }), ExConditionNum) : ExConditionNum)(elems[i], target.pickDiamondNum);
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).KillEnemyNum:
                elems[i] = new (_crd && ExConditionNum === void 0 ? (_reportPossibleCrUseOfExConditionNum({
                  error: Error()
                }), ExConditionNum) : ExConditionNum)(elems[i], target.killEnemyNum);
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).UseNuclear:
                elems[i] = new (_crd && ExConditionNum === void 0 ? (_reportPossibleCrUseOfExConditionNum({
                  error: Error()
                }), ExConditionNum) : ExConditionNum)(elems[i], target.usedNuclearNum);
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).UserSuper:
                elems[i] = new (_crd && ExConditionNum === void 0 ? (_reportPossibleCrUseOfExConditionNum({
                  error: Error()
                }), ExConditionNum) : ExConditionNum)(elems[i], target.usedSuperNum);
                break;
            }
          }

          return res.conditions;
        }

        static checkCondition(self, res, elems) {
          let target = null;
          (_crd && forEachEntityByTargetType === void 0 ? (_reportPossibleCrUseOfforEachEntityByTargetType({
            error: Error()
          }), forEachEntityByTargetType) : forEachEntityByTargetType)(self, res.target, entity => {
            target = entity;
          });

          if (!target) {
            return false;
          }

          if (res.conditions.length == 0) {
            return true;
          }

          let ret = res.boolType == (_crd && BoolOpType === void 0 ? (_reportPossibleCrUseOfBoolOpType({
            error: Error()
          }), BoolOpType) : BoolOpType).AND ? true : false;
          elems == null || elems.forEach(condition => {
            let value = 0;

            switch (condition.type) {
              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).BuffStack:
                {
                  var _GetBuff;

                  if (condition.params.length < 1) {
                    break;
                  }

                  value = ((_GetBuff = target.buffComp.GetBuff(condition.params[0])) == null ? void 0 : _GetBuff.stack) || 0;
                }
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).CurHPPer:
                value = target.curHp / target.maxHp * 10000;
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).CurHP:
                value = target.curHp;
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).MaxHP:
                value = target.maxHp;
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).BeAttackTime:
                value = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).battleManager._gameTime - target.hurtTime;
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).PickDiamond:
                if (condition instanceof (_crd && ExConditionNum === void 0 ? (_reportPossibleCrUseOfExConditionNum({
                  error: Error()
                }), ExConditionNum) : ExConditionNum)) {
                  value = target.pickDiamondNum - condition.num;
                }

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).KillEnemyNum:
              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).KillEnemy:
                if (condition instanceof (_crd && ExConditionNum === void 0 ? (_reportPossibleCrUseOfExConditionNum({
                  error: Error()
                }), ExConditionNum) : ExConditionNum)) {
                  value = target.killEnemyNum - condition.num;
                }

                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).RemainNuclearNum:
                value = target.nuclearNum;
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).UsedNuclearNum:
                value = target.usedNuclearNum;
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).LevelStart:
                value = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).battleManager._gameTime == 0 ? 1 : 0;
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).BossBeKilled:
                // TODO ybgg
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).UseNuclear:
                if (condition instanceof (_crd && ExConditionNum === void 0 ? (_reportPossibleCrUseOfExConditionNum({
                  error: Error()
                }), ExConditionNum) : ExConditionNum)) {
                  value = target.usedNuclearNum - condition.num;
                }

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).UserSuper:
                if (condition instanceof (_crd && ExConditionNum === void 0 ? (_reportPossibleCrUseOfExConditionNum({
                  error: Error()
                }), ExConditionNum) : ExConditionNum)) {
                  value = target.usedSuperNum - condition.num;
                }

                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).GameTime:
                value = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).battleManager._gameTime;
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).EnemyCount:
                if (condition.params.length >= 2) {
                  var _target;

                  const radiusSqr = condition.params[1] * condition.params[1];

                  if ((_target = target) != null && _target.enemy) {
                    if (Vec2.squaredDistance(target.node.position, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                      error: Error()
                    }), GameIns) : GameIns).mainPlaneManager.mainPlane.node.position) <= radiusSqr) {
                      value++;
                    }
                  } else {
                    (_crd && forEachEntityByTargetType === void 0 ? (_reportPossibleCrUseOfforEachEntityByTargetType({
                      error: Error()
                    }), forEachEntityByTargetType) : forEachEntityByTargetType)(target, (_crd && TargetType === void 0 ? (_reportPossibleCrUseOfTargetType({
                      error: Error()
                    }), TargetType) : TargetType).Enemy, entity => {
                      if (entity.isDead && Vec2.squaredDistance(entity.node.position, target.node.position) <= radiusSqr) {
                        value++;
                      }
                    });
                  }
                }

                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).WaveNo:
                // TODO ybgg
                // value = GameIns.battleManager.waveNo;
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).Distance:
                value = Vec2.distance(target.node.position, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).mainPlaneManager.mainPlane.node.position);
                break;

              case (_crd && SkillConditionType === void 0 ? (_reportPossibleCrUseOfSkillConditionType({
                error: Error()
              }), SkillConditionType) : SkillConditionType).KillEnemy:
                if (condition instanceof (_crd && ExConditionNum === void 0 ? (_reportPossibleCrUseOfExConditionNum({
                  error: Error()
                }), ExConditionNum) : ExConditionNum)) {
                  value = target.killEnemyNum - condition.num;
                }

                break;
            }

            let ret2 = false;

            switch (condition.op) {
              case (_crd && CondOPType === void 0 ? (_reportPossibleCrUseOfCondOPType({
                error: Error()
              }), CondOPType) : CondOPType).EQ:
                ret2 = value == condition.value;
                break;

              case (_crd && CondOPType === void 0 ? (_reportPossibleCrUseOfCondOPType({
                error: Error()
              }), CondOPType) : CondOPType).GE:
                ret2 = value >= condition.value;
                break;

              case (_crd && CondOPType === void 0 ? (_reportPossibleCrUseOfCondOPType({
                error: Error()
              }), CondOPType) : CondOPType).GT:
                ret2 = value > condition.value;
                break;

              case (_crd && CondOPType === void 0 ? (_reportPossibleCrUseOfCondOPType({
                error: Error()
              }), CondOPType) : CondOPType).LE:
                ret2 = value <= condition.value;
                break;

              case (_crd && CondOPType === void 0 ? (_reportPossibleCrUseOfCondOPType({
                error: Error()
              }), CondOPType) : CondOPType).LT:
                ret2 = value < condition.value;
                break;

              case (_crd && CondOPType === void 0 ? (_reportPossibleCrUseOfCondOPType({
                error: Error()
              }), CondOPType) : CondOPType).NE:
                ret2 = value != condition.value;
                break;
            }

            if (res.boolType == (_crd && BoolOpType === void 0 ? (_reportPossibleCrUseOfBoolOpType({
              error: Error()
            }), BoolOpType) : BoolOpType).AND) {
              ret = ret && ret2;
            } else {
              ret = ret || ret2;
            }
          });
          return ret;
        }

        checkRemoveCondition(self) {
          if (this.res.duration != -1 && this.time >= this.res.duration) {
            return true;
          }

          if (!this.removeConditionRes) {
            return false;
          }

          return Buff.checkCondition(self, this.removeConditionRes, this.removeConditionElems);
        }

        resetTriggerCondition() {
          var _this$triggerConditio;

          (_this$triggerConditio = this.triggerConditionElems) == null || _this$triggerConditio.forEach(condition => {
            if (condition instanceof (_crd && ExCondition === void 0 ? (_reportPossibleCrUseOfExCondition({
              error: Error()
            }), ExCondition) : ExCondition)) {
              condition.reset();
            }
          });
        }

      });

      Buff.incID = 1;

      _export("default", BuffComp = class BuffComp extends (_crd && BaseComp === void 0 ? (_reportPossibleCrUseOfBaseComp({
        error: Error()
      }), BaseComp) : BaseComp) {
        constructor(...args) {
          super(...args);
          this.buffs = new Map();
        }

        ApplyBuff(isOutside, buffID) {
          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("Buff", `apply buff ${buffID}`);
          let buffData = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbResBuffer.get(buffID);

          if (!buffData) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("Buff", `apply buff ${buffID} but config not found`);
            return;
          }

          const removeConditionRes = buffData.removeCondition ? (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbResSkillCondition.get(buffData.removeCondition) : undefined;

          if (removeConditionRes) {
            if (Buff.checkCondition(this.entity, removeConditionRes, removeConditionRes.conditions)) {
              return;
            }
          }

          let buff = null;

          if (buffData.duration != 0) {
            let buff = this.buffs.get(buffID);

            if (!buff) {
              buff = new Buff(isOutside, buffData, this.entity);
              this.buffs.set(buffID, buff);
            }

            if (buffData.refreshType && buffData.duration != -1) {
              buff.time = 0;
            }

            let stack = buffData.maxStack < 1 ? 1 : buffData.maxStack;
            buff.stack = Math.min(stack, buff.stack + 1);
          }

          buffData.effects.forEach(applyEffect => {
            (_crd && forEachEntityByTargetType === void 0 ? (_reportPossibleCrUseOfforEachEntityByTargetType({
              error: Error()
            }), forEachEntityByTargetType) : forEachEntityByTargetType)(this.entity, applyEffect.target, entity => {
              entity.ApplyBuffEffect(buff, applyEffect);
            });
          });
        }

        HasBuff(buffID) {
          return this.buffs.has(buffID);
        }

        GetBuff(buffID) {
          return this.buffs.get(buffID);
        }

        updateGameLogic(dt) {
          this.buffs.forEach((buff, buffID) => {
            buff.time += dt * 1000;

            if (buff.checkRemoveCondition(this.entity)) {
              this.removeBuff(buff, buffID);
            }

            if (buff.forbinConditionRes && Buff.checkCondition(this.entity, buff.forbinConditionRes, buff.forbinConditionElems)) {
              if (!buff.forbin) {
                buff.res.effects.forEach(applyEffect => {
                  (_crd && forEachEntityByTargetType === void 0 ? (_reportPossibleCrUseOfforEachEntityByTargetType({
                    error: Error()
                  }), forEachEntityByTargetType) : forEachEntityByTargetType)(this.entity, applyEffect.target, entity => {
                    entity.RemoveBuffEffect(buff, applyEffect);
                  });
                });
              }

              buff.forbin = true;
            } else {
              if (buff.forbin) {
                buff.res.effects.forEach(applyEffect => {
                  (_crd && forEachEntityByTargetType === void 0 ? (_reportPossibleCrUseOfforEachEntityByTargetType({
                    error: Error()
                  }), forEachEntityByTargetType) : forEachEntityByTargetType)(this.entity, applyEffect.target, entity => {
                    entity.ApplyBuffEffect(buff, applyEffect);
                  });
                });
              }

              buff.forbin = false;
            }

            if (buff.res.cycle > 0 && buff.time >= (buff.cycleTimes + 1) * buff.res.cycle && (buff.res.cycleTimes == 0 || buff.cycleTimes < buff.res.cycleTimes) || buff.triggerConditionElems && Buff.checkCondition(this.entity, buff.triggerConditionRes, buff.triggerConditionElems)) {
              buff.cycleTimes++;
              buff.resetTriggerCondition();
              buff.res.effects.forEach(applyEffect => {
                (_crd && forEachEntityByTargetType === void 0 ? (_reportPossibleCrUseOfforEachEntityByTargetType({
                  error: Error()
                }), forEachEntityByTargetType) : forEachEntityByTargetType)(this.entity, applyEffect.target, entity => {
                  entity.ApplyBuffEffect(buff, applyEffect);
                });
              });
            }
          });
        }

        removeBuff(buff, buffID) {
          buff.res.effects.forEach(applyEffect => {
            // 这个地方和加的时候查出来的target会不同
            // 1. 需要保证查出来的target只多不少
            // 2. remove接口里面需要判断时候是这个buff的效果
            (_crd && forEachEntityByTargetType === void 0 ? (_reportPossibleCrUseOfforEachEntityByTargetType({
              error: Error()
            }), forEachEntityByTargetType) : forEachEntityByTargetType)(this.entity, applyEffect.target, entity => {
              entity.RemoveBuffEffect(buff, applyEffect);
            });
          });
          this.buffs.delete(buffID);
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=da6c272358bbc294ccffe3f40bef149665ff517b.js.map