{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBase.ts"], "names": ["_decorator", "GameIns", "GameEnum", "PlaneBase", "Bullet", "Plane", "Weapon", "AttributeConst", "Movable", "eMoveEvent", "ccclass", "property", "EnemyPlaneBase", "_enemyData", "_moveCom", "removeAble", "bullets", "_weapons", "moveCom", "initPlane", "data", "enemy", "init", "getComponent", "addComponent", "removeAllListeners", "on", "onBecomeInvisible", "onBecameInvisible", "onBecomeVisible", "onBecameVisible", "getComponentsInChildren", "length", "weapon", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "mainPlaneManager", "mainPlane", "refreshProperty", "initMove", "angle", "speed", "config", "moveSpeed", "speedAngle", "turnSpeed", "setMovable", "_dieWhenOffScreen", "to<PERSON><PERSON>", "EnemyDestroyType", "Leave", "attribute", "setBaseAttribute", "MaxHPOutAdd", "baseHp", "AttackOutAdd", "baseAtk", "StatusImmuneBulletHurt", "immuneBulletDamage", "StatusImmuneCollisionHurt", "immuneCollideDamage", "StatusIgnoreBullet", "ignoreBullet", "StatusIgnoreCollision", "ignoreCollide", "StatusImmuneNuclearHurt", "immuneNuke", "StatusImmuneActiveSkillHurt", "immuneActiveSkill", "curHp", "maxHp", "getAttack", "destroyType", "Die", "colliderEnabled", "onDie", "will<PERSON><PERSON><PERSON>", "playDieAnim", "TrackOver", "TimeOver", "callBack", "collisionLevel", "collideLevel", "collisionHurt", "collideDamage", "onCollide", "collider", "isDead", "entity", "getFinalAttributeByKey", "damage", "calcDamage", "hurtEffectManager", "createHurtNumByType", "node", "getPosition", "hurt", "collisionPlane", "addBullet", "bullet", "push", "removeBullet", "index", "indexOf", "splice", "setPos", "x", "y", "setPosition"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,S;;AAEEC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,M,iBAAAA,M;;AAEAC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEZ;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;yBAGTY,c,WADpBF,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ;AAAA;AAAA,yB,2BAFb,MACqBC,cADrB;AAAA;AAAA,kCACsD;AAAA;AAAA;;AAAA;;AAAA,eAGlDC,UAHkD,GAGnB,IAHmB;AAAA,eAIlDC,QAJkD,GAIvB,IAJuB;AAAA,eAOlDC,UAPkD,GAO7B,KAP6B;AAAA,eAQlDC,OARkD,GAQ9B,EAR8B;AAAA,eAU1CC,QAV0C,GAUrB,EAVqB;AAAA;;AAKhC,YAAPC,OAAO,GAAG;AAAE,iBAAO,KAAKJ,QAAZ;AAAuB;;AAO9CK,QAAAA,SAAS,CAACC,IAAD,EAAkB;AACvB,eAAKC,KAAL,GAAa,IAAb;AACA,eAAKR,UAAL,GAAkBO,IAAlB;AACA,gBAAME,IAAN;AAEA,eAAKR,QAAL,GAAgB,KAAKS,YAAL;AAAA;AAAA,qCAA8B,KAAKC,YAAL;AAAA;AAAA,iCAA9C;;AACA,eAAKV,QAAL,CAAeW,kBAAf;;AACA,eAAKX,QAAL,CAAeY,EAAf,CAAkB;AAAA;AAAA,wCAAWC,iBAA7B,EAAgD,MAAM,KAAKC,iBAAL,EAAtD;;AACA,eAAKd,QAAL,CAAeY,EAAf,CAAkB;AAAA;AAAA,wCAAWG,eAA7B,EAA8C,MAAM,KAAKC,eAAL,EAApD,EARuB,CAUvB;;;AACA,eAAKb,QAAL,GAAgB,KAAKc,uBAAL;AAAA;AAAA,+BAAhB;;AACA,cAAI,KAAKd,QAAL,CAAce,MAAd,GAAuB,CAA3B,EAA8B;AAC1B,iBAAK,IAAIC,MAAT,IAAmB,KAAKhB,QAAxB,EAAkC;AAC9BgB,cAAAA,MAAM,CAACX,IAAP;AACAW,cAAAA,MAAM,CAACC,QAAP,CAAgB,IAAhB,EAAsBC,SAAtB,CAAgC;AAAA;AAAA,sCAAQC,gBAAR,CAAyBC,SAAzD;AACH;AACJ;;AAED,eAAKC,eAAL;AACH;;AAEDC,QAAAA,QAAQ,CAACC,KAAD,EAAgB;AAAA;;AACpB,cAAI,CAAC,KAAK1B,QAAV,EAAoB;AAChB;AACH,WAHmB,CAKpB;;;AACA,eAAKA,QAAL,CAAe2B,KAAf,GAAuB,iBAAK5B,UAAL,CAAiB6B,MAAjB,6BAAyBC,SAAzB,KAAsC,GAA7D;AACA,eAAK7B,QAAL,CAAe8B,UAAf,GAA4BJ,KAA5B;AACA,eAAK1B,QAAL,CAAe+B,SAAf,GAA2B,kBAAKhC,UAAL,CAAiB6B,MAAjB,8BAAyBG,SAAzB,KAAsC,CAAjE;;AACA,eAAK/B,QAAL,CAAegC,UAAf,CAA0B,IAA1B;AACH;;AAEDC,QAAAA,iBAAiB,GAAG;AAChB,eAAKC,KAAL,CAAW;AAAA;AAAA,oCAASC,gBAAT,CAA0BC,KAArC;AACH;;AAEOtB,QAAAA,iBAAiB,GAAG;AACxB;AACA,eAAKmB,iBAAL,GAFwB,CAGxB;;AACH;;AAEOjB,QAAAA,eAAe,GAAG,CACtB;AACH;;AAEDQ,QAAAA,eAAe,GAAG;AAAA;;AACd,gBAAMI,MAAM,uBAAG,KAAK7B,UAAR,qBAAG,iBAAiB6B,MAAhC;;AACA,cAAI,CAACA,MAAL,EAAa;AACT;AACH;;AACD,eAAKS,SAAL,CAAeC,gBAAf,CAAgC;AAAA;AAAA,gDAAeC,WAA/C,EAA4DX,MAAM,CAACY,MAAnE;AACA,eAAKH,SAAL,CAAeC,gBAAf,CAAgC;AAAA;AAAA,gDAAeG,YAA/C,EAA6Db,MAAM,CAACc,OAApE;AACA,eAAKL,SAAL,CAAeC,gBAAf,CAAgC;AAAA;AAAA,gDAAeK,sBAA/C,EAAuEf,MAAM,CAACgB,kBAAP,GAA0B,CAA1B,GAA4B,CAAnG;AACA,eAAKP,SAAL,CAAeC,gBAAf,CAAgC;AAAA;AAAA,gDAAeO,yBAA/C,EAA0EjB,MAAM,CAACkB,mBAAP,GAA2B,CAA3B,GAA6B,CAAvG;AACA,eAAKT,SAAL,CAAeC,gBAAf,CAAgC;AAAA;AAAA,gDAAeS,kBAA/C,EAAmEnB,MAAM,CAACoB,YAAP,GAAoB,CAApB,GAAsB,CAAzF;AACA,eAAKX,SAAL,CAAeC,gBAAf,CAAgC;AAAA;AAAA,gDAAeW,qBAA/C,EAAsErB,MAAM,CAACsB,aAAP,GAAqB,CAArB,GAAuB,CAA7F;AACA,eAAKb,SAAL,CAAeC,gBAAf,CAAgC;AAAA;AAAA,gDAAea,uBAA/C,EAAwEvB,MAAM,CAACwB,UAAP,GAAkB,CAAlB,GAAoB,CAA5F;AACA,eAAKf,SAAL,CAAeC,gBAAf,CAAgC;AAAA;AAAA,gDAAee,2BAA/C,EAA4EzB,MAAM,CAAC0B,iBAAP,GAAyB,CAAzB,GAA2B,CAAvG;AAEA,eAAKC,KAAL,GAAa,KAAKC,KAAlB;AACH;;AAEDC,QAAAA,SAAS,GAAU;AACf,iBAAO,KAAK1D,UAAL,CAAiB0D,SAAjB,EAAP;AACH;;AAEDvB,QAAAA,KAAK,CAACwB,WAAsC,GAAG;AAAA;AAAA,kCAASvB,gBAAT,CAA0BwB,GAApE,EAAmF;AACpF,cAAI,CAAC,MAAMzB,KAAN,CAAYwB,WAAZ,CAAL,EAA+B;AAC3B,mBAAO,KAAP;AACH;;AACD,eAAKE,eAAL,GAAuB,KAAvB;AAEA,eAAKC,KAAL,CAAWH,WAAX;AACA,iBAAO,IAAP;AACH;;AAEDG,QAAAA,KAAK,CAACH,WAAD,EAAsB;AACvB,eAAKI,UAAL;;AAEA,kBAAQJ,WAAR;AACI,iBAAK;AAAA;AAAA,sCAASvB,gBAAT,CAA0BwB,GAA/B;AACI,mBAAKI,WAAL,CAAiB,MAAI;AACjB,qBAAK9D,UAAL,GAAkB,IAAlB;AACH,eAFD;AAGA;;AAEJ,iBAAK;AAAA;AAAA,sCAASkC,gBAAT,CAA0BC,KAA/B;AACA,iBAAK;AAAA;AAAA,sCAASD,gBAAT,CAA0B6B,SAA/B;AACA,iBAAK;AAAA;AAAA,sCAAS7B,gBAAT,CAA0B8B,QAA/B;AACI,mBAAKhE,UAAL,GAAkB,IAAlB;AACA;AAXR;AAaH;;AAED8D,QAAAA,WAAW,CAACG,QAAD,EAAqB;AAC5B;AACA;AACA;AACAA,UAAAA,QAAQ,QAAR,IAAAA,QAAQ;AACX;;AAEiB,YAAdC,cAAc,GAAG;AAAA;;AACjB,iBAAO,kBAAKpE,UAAL,CAAiB6B,MAAjB,8BAAyBwC,YAAzB,KAAyC,CAAhD;AACH;;AACgB,YAAbC,aAAa,GAAG;AAAA;;AAChB,iBAAO,kBAAKtE,UAAL,CAAiB6B,MAAjB,8BAAyB0C,aAAzB,KAA0C,CAAjD;AACH;;AAEDC,QAAAA,SAAS,CAACC,QAAD,EAAsB;AAC3B,cAAI,KAAKC,MAAT,EAAiB;AACb;AACH;;AACD,cAAID,QAAQ,CAACE,MAAT;AAAA;AAAA,+BAAJ,EAAuC;AACnC,gBAAI,KAAKrC,SAAL,CAAesC,sBAAf,CAAsC;AAAA;AAAA,kDAAehC,sBAArD,KAAgF,CAApF,EAAuF;AACnF,oBAAMiC,MAAM,GAAGJ,QAAQ,CAACE,MAAT,CAAgBG,UAAhB,CAA2B,IAA3B,CAAf;AACA;AAAA;AAAA,sCAAQC,iBAAR,CAA0BC,mBAA1B,CAA8CP,QAAQ,CAACE,MAAT,CAAgBM,IAAhB,CAAqBC,WAArB,EAA9C,EAAkFL,MAAlF;AACA,mBAAKM,IAAL,CAAUN,MAAV;AACH;AACJ,WAND,MAMO,IAAIJ,QAAQ,CAACE,MAAT;AAAA;AAAA,yCAAwC,CAACF,QAAQ,CAACE,MAAT,CAAgBnE,KAA7D,EAAoE;AACvE,iBAAK4E,cAAL,CAAoBX,QAAQ,CAACE,MAA7B;AACH;AACJ;AAED;AACJ;AACA;;;AACIZ,QAAAA,UAAU,GAAG,CAEZ;;AAEDsB,QAAAA,SAAS,CAACC,MAAD,EAAiB;AACtB,cAAI,KAAKnF,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAaoF,IAAb,CAAkBD,MAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,YAAY,CAACF,MAAD,EAAiB;AACzB,cAAI,KAAKnF,OAAT,EAAkB;AACd,kBAAMsF,KAAK,GAAG,KAAKtF,OAAL,CAAauF,OAAb,CAAqBJ,MAArB,CAAd;;AACA,gBAAIG,KAAK,IAAI,CAAb,EAAgB;AACZ,mBAAKtF,OAAL,CAAawF,MAAb,CAAoBF,KAApB,EAA2B,CAA3B;AACH;AACJ;AACJ;;AAEDG,QAAAA,MAAM,CAACC,CAAD,EAAYC,CAAZ,EAAuB;AACzB,eAAKb,IAAL,CAAUc,WAAV,CAAsBF,CAAtB,EAAyBC,CAAzB;AACH;;AAtKiD,O;;;;;iBAE5B,I", "sourcesContent": ["import { _decorator} from 'cc';\r\nimport { GameIns } from '../../../GameIns';\r\nimport { GameEnum } from '../../../const/GameEnum';\r\nimport PlaneBase from 'db://assets/bundles/common/script/game/ui/plane/PlaneBase';\r\nimport FCollider from '../../../collider-system/FCollider';\r\nimport { Bullet } from '../../../bullet/Bullet';\r\nimport { Plane } from 'db://assets/bundles/common/script/ui/Plane';\r\nimport { Weapon } from 'db://assets/bundles/common/script/game/ui/plane/weapon/Weapon';\r\nimport { EnemyData } from '../../../data/EnemyData';\r\nimport { AttributeConst } from 'db://assets/bundles/common/script/const/AttributeConst';\r\nimport { Movable, eMoveEvent } from '../../../move/Movable';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EnemyPlaneBase')\r\nexport default class EnemyPlaneBase extends PlaneBase {\r\n    @property(Plane)//敌机显示组件\r\n    plane: Plane | null = null;\r\n    _enemyData: EnemyData | null = null;\r\n    _moveCom: Movable | null = null;\r\n    public get moveCom() { return this._moveCom; }\r\n\r\n    removeAble:boolean = false;\r\n    bullets: Bullet[] = [];\r\n\r\n    private _weapons: Weapon[] = [];\r\n\r\n    initPlane(data: EnemyData) {\r\n        this.enemy = true\r\n        this._enemyData = data;\r\n        super.init();\r\n\r\n        this._moveCom = this.getComponent(Movable) || this.addComponent(Movable);\r\n        this._moveCom!.removeAllListeners();\r\n        this._moveCom!.on(eMoveEvent.onBecomeInvisible, () => this.onBecameInvisible());\r\n        this._moveCom!.on(eMoveEvent.onBecomeVisible, () => this.onBecameVisible());\r\n\r\n        // 初始化武器\r\n        this._weapons = this.getComponentsInChildren(Weapon);\r\n        if (this._weapons.length > 0) {\r\n            for (let weapon of this._weapons) {\r\n                weapon.init();\r\n                weapon.setOwner(this).setTarget(GameIns.mainPlaneManager.mainPlane);\r\n            }\r\n        }\r\n\r\n        this.refreshProperty();\r\n    }\r\n\r\n    initMove(angle: number) {\r\n        if (!this._moveCom) {\r\n            return;\r\n        }\r\n\r\n        // 速度从表格里读取\r\n        this._moveCom!.speed = this._enemyData!.config?.moveSpeed || 100;\r\n        this._moveCom!.speedAngle = angle;\r\n        this._moveCom!.turnSpeed = this._enemyData!.config?.turnSpeed || 0;\r\n        this._moveCom!.setMovable(true);\r\n    }\r\n\r\n    _dieWhenOffScreen() {\r\n        this.toDie(GameEnum.EnemyDestroyType.Leave);\r\n    }\r\n\r\n    private onBecameInvisible() {\r\n        // TODO: 从表格里增加延时销毁的配置\r\n        this._dieWhenOffScreen();\r\n        // this.scheduleOnce(this._dieWhenOffScreen, delayDestroy);\r\n    }\r\n\r\n    private onBecameVisible() {\r\n        // this.unschedule(this._dieWhenOffScreen);\r\n    }\r\n\r\n    refreshProperty() {\r\n        const config = this._enemyData?.config;\r\n        if (!config) {\r\n            return;\r\n        }\r\n        this.attribute.setBaseAttribute(AttributeConst.MaxHPOutAdd, config.baseHp);\r\n        this.attribute.setBaseAttribute(AttributeConst.AttackOutAdd, config.baseAtk);\r\n        this.attribute.setBaseAttribute(AttributeConst.StatusImmuneBulletHurt, config.immuneBulletDamage?1:0);\r\n        this.attribute.setBaseAttribute(AttributeConst.StatusImmuneCollisionHurt, config.immuneCollideDamage?1:0);\r\n        this.attribute.setBaseAttribute(AttributeConst.StatusIgnoreBullet, config.ignoreBullet?1:0);\r\n        this.attribute.setBaseAttribute(AttributeConst.StatusIgnoreCollision, config.ignoreCollide?1:0);\r\n        this.attribute.setBaseAttribute(AttributeConst.StatusImmuneNuclearHurt, config.immuneNuke?1:0);\r\n        this.attribute.setBaseAttribute(AttributeConst.StatusImmuneActiveSkillHurt, config.immuneActiveSkill?1:0);\r\n\r\n        this.curHp = this.maxHp\r\n    }\r\n\r\n    getAttack():number {\r\n        return this._enemyData!.getAttack();\r\n    }\r\n\r\n    toDie(destroyType: GameEnum.EnemyDestroyType = GameEnum.EnemyDestroyType.Die): boolean  {\r\n        if (!super.toDie(destroyType)) {\r\n            return false;\r\n        }\r\n        this.colliderEnabled = false;\r\n\r\n        this.onDie(destroyType);\r\n        return true;\r\n    }\r\n\r\n    onDie(destroyType: number) {\r\n        this.willRemove();\r\n\r\n        switch (destroyType) {\r\n            case GameEnum.EnemyDestroyType.Die:\r\n                this.playDieAnim(()=>{\r\n                    this.removeAble = true;\r\n                });\r\n                break;\r\n\r\n            case GameEnum.EnemyDestroyType.Leave:\r\n            case GameEnum.EnemyDestroyType.TrackOver:\r\n            case GameEnum.EnemyDestroyType.TimeOver:\r\n                this.removeAble = true;\r\n                break;\r\n        }\r\n    }\r\n\r\n    playDieAnim(callBack: Function) {\r\n        // if (this.plane) {\r\n        //     this.plane.playDieAnim(callBack);   \r\n        // }\r\n        callBack?.();\r\n    }\r\n\r\n    get collisionLevel() {\r\n        return this._enemyData!.config?.collideLevel || 0;\r\n    }\r\n    get collisionHurt() {\r\n        return this._enemyData!.config?.collideDamage || 0;\r\n    }\r\n\r\n    onCollide(collider: FCollider) {\r\n        if (this.isDead) {\r\n            return\r\n        }\r\n        if (collider.entity instanceof Bullet) {\r\n            if (this.attribute.getFinalAttributeByKey(AttributeConst.StatusImmuneBulletHurt) == 0) {\r\n                const damage = collider.entity.calcDamage(this);\r\n                GameIns.hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(), damage);\r\n                this.hurt(damage)\r\n            }\r\n        } else if (collider.entity instanceof PlaneBase && !collider.entity.enemy) {\r\n            this.collisionPlane(collider.entity);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 准备移除敌机\r\n     */\r\n    willRemove() {\r\n\r\n    }\r\n\r\n    addBullet(bullet: Bullet) {\r\n        if (this.bullets) {\r\n            this.bullets.push(bullet);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 从敌人移除子弹\r\n     * @param {Bullet} bullet 子弹对象\r\n     */\r\n    removeBullet(bullet: Bullet) {\r\n        if (this.bullets) {\r\n            const index = this.bullets.indexOf(bullet);\r\n            if (index >= 0) {\r\n                this.bullets.splice(index, 1);\r\n            }\r\n        }\r\n    }\r\n\r\n    setPos(x: number, y: number) {\r\n        this.node.setPosition(x, y);\r\n    }\r\n}"]}