{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/ui/plane/weapon/Weapon.ts"], "names": ["_decorator", "instantiate", "Node", "Prefab", "Enum", "CCInteger", "eEmitterStatus", "Emitter", "Entity", "ccclass", "property", "eWeaponUseCond", "Weapon", "type", "displayName", "visible", "useCondition", "DelayTime", "WeaponDestroyed", "m_state", "eWeaponState", "None", "m_stateElapsedTime", "m_target", "m_owner", "m_targetWeapons", "m_emitter", "init", "node", "getComponentsInChildren", "initEmitter", "emitterPrefab", "<PERSON><PERSON><PERSON><PERSON>", "getComponent", "setEntity", "changeStatus", "Prewarm", "state", "value", "<PERSON><PERSON><PERSON><PERSON>", "owner", "<PERSON><PERSON><PERSON><PERSON>", "target", "Immediate", "activate", "tick", "dt", "Aiming", "tickAiming", "Emitting", "tickNone", "aimingTime", "turn<PERSON>o<PERSON>arget", "tickEmitting", "weapon", "isDead", "delayTime", "ownerNode", "targetNode", "angle", "targetAngle", "Math", "atan2", "worldPosition", "y", "x", "PI", "deltaAngle", "max<PERSON><PERSON><PERSON>", "turnSpeed", "abs", "sign"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,W,OAAAA,W;AAAwBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;;AAHxDC,MAAAA,c,iBAAAA,c;AAAgBC,MAAAA,O,iBAAAA,O;;AAClBC,MAAAA,M;;;;;;;;;OAID;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;;gCAElBW,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;;;;;;;;AAcZ;AACA;AACA;;;wBAEaC,M,WADZH,OAAO,CAAC,QAAD,C,UAEHC,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAER,SAAR;AAAmBS,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UAERJ,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAER,SAAR;AAAmBS,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UAERJ,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAET,IAAI,CAACO,cAAD,CAAZ;AAA8BG,QAAAA,WAAW,EAAE;AAA3C,OAAD,C,UAERJ,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAER,SAAR;AAAmBS,QAAAA,WAAW,EAAE,YAAhC;;AACNC,QAAAA,OAAO,GAAG;AACN;AACA,iBAAO,KAAKC,YAAL,KAAsBL,cAAc,CAACM,SAA5C;AACH;;AAJK,OAAD,C,UASRP,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAE,CAACX,IAAD,CAAR;AAAgBY,QAAAA,WAAW,EAAE,MAA7B;;AACNC,QAAAA,OAAO,GAAG;AACN;AACA,iBAAO,KAAKC,YAAL,KAAsBL,cAAc,CAACO,eAA5C;AACH;;AAJK,OAAD,C,UAQRR,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEV,MAAR;AAAgBW,QAAAA,WAAW,EAAE;AAA7B,OAAD,C,2BAzBb,MACaF,MADb;AAAA;AAAA,4BACmC;AAAA;AAAA;;AAAA;;AAES;AAFT;;AAIY;AAJZ;;AAAA;;AAaQ;AAEvC;AAf+B;;AAAA;;AAyBa;AAzBb,eA2BvBO,OA3BuB,GA2BCC,YAAY,CAACC,IA3Bd;AAAA,eA4BvBC,kBA5BuB,GA4BM,CA5BN;AA4BS;AA5BT,eA6BvBC,QA7BuB,GA6BG,IA7BH;AA6BS;AA7BT,eA8BvBC,OA9BuB,GA8BE,IA9BF;AA8BQ;AA9BR,eA+BvBC,eA/BuB,GA+BK,EA/BL;AAAA,eAgCvBC,SAhCuB,GAgCK,IAhCL;AAAA;;AAkC/BC,QAAAA,IAAI,GAAG;AACH,eAAKR,OAAL,GAAeC,YAAY,CAACC,IAA5B,CADG,CAEH;AACA;AACA;;AACA,cAAI,KAAKL,YAAL,KAAsBL,cAAc,CAACO,eAAzC,EAA0D;AACtD,iBAAKO,eAAL,GAAuB,KAAKG,IAAL,CAAUC,uBAAV,CAAkCjB,MAAlC,CAAvB;AACH;AACJ;;AAEOkB,QAAAA,WAAW,GAAG;AAClB,cAAI,CAAC,KAAKJ,SAAV,EAAqB;AACjB,gBAAI,KAAKK,aAAT,EAAwB;AAAA;;AACpB,oBAAMH,IAAI,GAAG3B,WAAW,CAAC,KAAK8B,aAAN,CAAxB;AACA,mBAAKH,IAAL,CAAUI,QAAV,CAAmBJ,IAAnB;AACA,mBAAKF,SAAL,GAAiBE,IAAI,CAACK,YAAL;AAAA;AAAA,qCAAjB;AACA,sCAAKP,SAAL,6BAAgBQ,SAAhB,CAA0B,KAAKV,OAA/B;AACA,uCAAKE,SAAL,8BAAgBS,YAAhB,CAA6B;AAAA;AAAA,oDAAed,IAA5C;AACH;AACJ,WARD,MAQO;AAAA;;AACH;AACA,qCAAKK,SAAL,8BAAgBS,YAAhB,CAA6B;AAAA;AAAA,kDAAeC,OAA5C;AACH;AACJ;;AAEe,YAALC,KAAK,GAAiB;AAC7B,iBAAO,KAAKlB,OAAZ;AACH;;AAEe,YAALkB,KAAK,CAACC,KAAD,EAAsB;AAClC,cAAI,KAAKnB,OAAL,KAAiBmB,KAArB,EAA4B;AACxB;AACH;;AAED,eAAKnB,OAAL,GAAemB,KAAf;AACA,eAAKhB,kBAAL,GAA0B,CAA1B;AACH;;AAEMiB,QAAAA,QAAQ,CAACC,KAAD,EAAwB;AACnC,eAAKhB,OAAL,GAAegB,KAAf;AACA,iBAAO,IAAP;AACH;;AAEMC,QAAAA,SAAS,CAACC,MAAD,EAAgC;AAC5C,eAAKnB,QAAL,GAAgBmB,MAAhB;;AACA,cAAI,KAAK1B,YAAL,KAAsBL,cAAc,CAACgC,SAAzC,EAAoD;AAChD,iBAAKC,QAAL;AACH;;AAED,iBAAO,IAAP;AACH;;AAEMC,QAAAA,IAAI,CAACC,EAAD,EAAa;AACpB,eAAKxB,kBAAL,IAA2BwB,EAA3B;;AACA,kBAAQ,KAAK3B,OAAb;AACI,iBAAKC,YAAY,CAAC2B,MAAlB;AACI,mBAAKC,UAAL,CAAgBF,EAAhB;AACA;;AACJ,iBAAK1B,YAAY,CAAC6B,QAAlB;AACI;AACA;;AACJ,iBAAK7B,YAAY,CAACC,IAAlB;AACI,mBAAK6B,QAAL,CAAcJ,EAAd;AACA;AATR;AAWH,SAnG8B,CAqG/B;;;AACQF,QAAAA,QAAQ,GAAG;AACf,eAAKP,KAAL,GAAa,KAAKc,UAAL,GAAkB,CAAlB,GAAsB/B,YAAY,CAAC2B,MAAnC,GAA4C3B,YAAY,CAAC6B,QAAtE;AACA,eAAKnB,WAAL;AACH;;AAEOkB,QAAAA,UAAU,CAACF,EAAD,EAAa;AAC3B,cAAI,KAAKxB,kBAAL,IAA2B,KAAK6B,UAApC,EAAgD;AAC5C,iBAAKd,KAAL,GAAajB,YAAY,CAAC6B,QAA1B;AACH;;AAED,eAAKG,YAAL,CAAkBN,EAAlB;AACH;;AAEOO,QAAAA,YAAY,CAACP,EAAD,EAAa,CAC7B;AACA;AACA;AACH;;AAEOI,QAAAA,QAAQ,CAACJ,EAAD,EAAa;AACzB,cAAI,KAAK9B,YAAL,KAAsBL,cAAc,CAACO,eAAzC,EAA0D;AACtD,iBAAK,IAAIoC,MAAT,IAAmB,KAAK7B,eAAxB,EAAyC;AACrC,kBAAI,CAAC6B,MAAM,CAACC,MAAZ,EAAoB;AAChB;AACH;AACJ;;AACD,iBAAKX,QAAL;AACH,WAPD,MAOO,IAAI,KAAK5B,YAAL,KAAsBL,cAAc,CAACM,SAAzC,EAAoD;AACvD,gBAAI,KAAKK,kBAAL,IAA2B,KAAKkC,SAApC,EAA+C;AAC3C,mBAAKZ,QAAL;AACH;AACJ;AACJ;;AAEOQ,QAAAA,YAAY,CAACN,EAAD,EAAa;AAC7B,cAAI,CAAC,KAAKvB,QAAN,IAAkB,CAAC,KAAKC,OAA5B,EAAqC;AACjC;AACH;;AACD,gBAAMiC,SAAS,GAAG,KAAKjC,OAAL,CAAaI,IAA/B;AACA,gBAAM8B,UAAU,GAAG,KAAKnC,QAAL,CAAcK,IAAjC;AACA,gBAAM+B,KAAK,GAAGF,SAAS,CAACE,KAAxB;AACA,gBAAMC,WAAW,GAAGC,IAAI,CAACC,KAAL,CAAWJ,UAAU,CAACK,aAAX,CAAyBC,CAAzB,GAA6BP,SAAS,CAACM,aAAV,CAAwBC,CAAhE,EAAmEN,UAAU,CAACK,aAAX,CAAyBE,CAAzB,GAA6BR,SAAS,CAACM,aAAV,CAAwBE,CAAxH,IAA6H,GAA7H,GAAmIJ,IAAI,CAACK,EAAxI,GAA6I,EAAjK;AACA,cAAIC,UAAU,GAAGP,WAAW,GAAGD,KAA/B;;AACA,cAAIQ,UAAU,GAAG,GAAjB,EAAsB;AAClBA,YAAAA,UAAU,IAAI,GAAd;AACH,WAFD,MAEO,IAAIA,UAAU,GAAG,CAAC,GAAlB,EAAuB;AAC1BA,YAAAA,UAAU,IAAI,GAAd;AACH;;AACD,gBAAMC,QAAQ,GAAG,KAAKC,SAAL,GAAiBvB,EAAjB,GAAsB,IAAvC;;AACA,cAAIe,IAAI,CAACS,GAAL,CAASH,UAAT,KAAwBC,QAA5B,EAAsC;AAClCX,YAAAA,SAAS,CAACE,KAAV,GAAkBC,WAAlB;AACH,WAFD,MAGK;AACDH,YAAAA,SAAS,CAACE,KAAV,IAAmBE,IAAI,CAACU,IAAL,CAAUJ,UAAV,IAAwBC,QAA3C;AACH;AACJ;;AA7J8B,O;;;;;iBAEK,E;;;;;;;iBAEC,I;;;;;;;iBAEUzD,cAAc,CAACgC,S;;;;;;;iBAO1B,C;;;;;;;iBASD,E;;;;;;;iBAGG,I", "sourcesContent": ["import { eEmitterStatus, Emitter } from \"db://assets/bundles/common/script/game/bullet/Emitter\";\r\nimport Entity from \"db://assets/bundles/common/script/game/ui/base/Entity\";\r\n\r\nimport { _decorator, instantiate, Component, Node, Prefab, Enum, CCInteger } from \"cc\";\r\nimport PlaneBase from \"../PlaneBase\";\r\nconst { ccclass, property } = _decorator;\r\n\r\nexport enum eWeaponUseCond {\r\n    Immediate,        // 立刻启用\r\n    DelayTime,        // 延迟一段时间启用\r\n    WeaponDestroyed,  // 前置武器被销毁\r\n    // TODO: TargetDistance, // 目标距离在范围内\r\n    // TODO: TargetAngle,    // 目标角度在范围内\r\n}\r\n\r\nexport const enum eWeaponState {\r\n    Aiming,     // 玩家飞机不需要这个状态, 只有敌机需要\r\n    Emitting,   // 发射中\r\n    None,       // 无任何状态:后面看需求是否增加Destroyed等状态\r\n}\r\n\r\n/**\r\n * 武器后续可能也需要继承自PlaneBase, 因为武器可能也需要血量，扣血等逻辑。\r\n */\r\n@ccclass('Weapon')\r\nexport class Weapon extends Entity {\r\n    @property({ type: CCInteger, displayName: \"转向速度\" })\r\n    public readonly turnSpeed: number = 60; // 转向速度（仅用在追踪目标时）\r\n    @property({ type: CCInteger, displayName: \"锁定目标时间(ms)\" })\r\n    public readonly aimingTime: number = 2000; // 锁定目标时间，单位ms\r\n    @property({ type: Enum(eWeaponUseCond), displayName: \"启用条件\"})\r\n    public readonly useCondition: eWeaponUseCond = eWeaponUseCond.Immediate;\r\n    @property({ type: CCInteger, displayName: \"延迟启用时间(ms)\", \r\n        visible() {\r\n            // @ts-ignore\r\n            return this.useCondition === eWeaponUseCond.DelayTime;\r\n        }\r\n    })\r\n    public readonly delayTime: number = 0; // 延迟时间，单位ms\r\n\r\n    // 注意这里的Node不能替换成Weapon，因为cocos在ccclass里不能引用自己。\r\n    @property({ type: [Node], displayName: \"目标武器\", \r\n        visible() { \r\n            // @ts-ignore\r\n            return this.useCondition === eWeaponUseCond.WeaponDestroyed \r\n        } \r\n    })\r\n    public targetWeaponNodes: Node[] = [];\r\n\r\n    @property({ type: Prefab, displayName: \"发射器\" })\r\n    public emitterPrefab: Prefab | null = null; // 武器发射器\r\n\r\n    private m_state: eWeaponState = eWeaponState.None;\r\n    private m_stateElapsedTime: number = 0; // 当前状态持续时间\r\n    private m_target: Entity | null = null; // 目标实体\r\n    private m_owner: Entity | null = null; // 拥有者实体\r\n    private m_targetWeapons: Weapon[] = [];\r\n    private m_emitter: Emitter | null = null;\r\n\r\n    init() {\r\n        this.m_state = eWeaponState.None;\r\n        // if (this.emitter) {\r\n        //     this.emitter.changeStatus(eEmitterStatus.None);\r\n        // }\r\n        if (this.useCondition === eWeaponUseCond.WeaponDestroyed) {\r\n            this.m_targetWeapons = this.node.getComponentsInChildren(Weapon);\r\n        }\r\n    }\r\n\r\n    private initEmitter() {\r\n        if (!this.m_emitter) {\r\n            if (this.emitterPrefab) {\r\n                const node = instantiate(this.emitterPrefab);\r\n                this.node.addChild(node);\r\n                this.m_emitter = node.getComponent(Emitter);\r\n                this.m_emitter?.setEntity(this.m_owner as PlaneBase);\r\n                this.m_emitter?.changeStatus(eEmitterStatus.None);\r\n            }\r\n        } else {\r\n            // 非首次启用了，后面看是直接切到预热还是要算启用延迟\r\n            this.m_emitter?.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    public get state(): eWeaponState {\r\n        return this.m_state;\r\n    }\r\n\r\n    public set state(value: eWeaponState) {\r\n        if (this.m_state === value) {\r\n            return;\r\n        }\r\n\r\n        this.m_state = value;\r\n        this.m_stateElapsedTime = 0;\r\n    }\r\n\r\n    public setOwner(owner: Entity): Weapon {\r\n        this.m_owner = owner;\r\n        return this;\r\n    }\r\n\r\n    public setTarget(target: Entity | null): Weapon {\r\n        this.m_target = target;\r\n        if (this.useCondition === eWeaponUseCond.Immediate) {\r\n            this.activate();\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    public tick(dt: number) {\r\n        this.m_stateElapsedTime += dt;\r\n        switch (this.m_state) {\r\n            case eWeaponState.Aiming:\r\n                this.tickAiming(dt);\r\n                break;\r\n            case eWeaponState.Emitting:\r\n                //this.tickEmitting(dt);\r\n                break;\r\n            case eWeaponState.None:\r\n                this.tickNone(dt);\r\n                break;\r\n        }\r\n    }\r\n\r\n    // 激活武器\r\n    private activate() {\r\n        this.state = this.aimingTime > 0 ? eWeaponState.Aiming : eWeaponState.Emitting;\r\n        this.initEmitter();\r\n    }\r\n\r\n    private tickAiming(dt: number) {\r\n        if (this.m_stateElapsedTime >= this.aimingTime) {\r\n            this.state = eWeaponState.Emitting;\r\n        }\r\n\r\n        this.turnToTarget(dt);\r\n    }\r\n\r\n    private tickEmitting(dt: number) {\r\n        //if (this.m_emitter && this.m_emitter.status !== eEmitterStatus.Emitting) {\r\n        //    this.m_emitter.changeStatus(eEmitterStatus.Emitting);\r\n        //}\r\n    }\r\n\r\n    private tickNone(dt: number) {\r\n        if (this.useCondition === eWeaponUseCond.WeaponDestroyed) {\r\n            for (let weapon of this.m_targetWeapons) {\r\n                if (!weapon.isDead) {\r\n                    return;\r\n                }\r\n            }\r\n            this.activate();\r\n        } else if (this.useCondition === eWeaponUseCond.DelayTime) {\r\n            if (this.m_stateElapsedTime >= this.delayTime) {\r\n                this.activate();\r\n            }\r\n        }\r\n    }\r\n\r\n    private turnToTarget(dt: number) {\r\n        if (!this.m_target || !this.m_owner) {\r\n            return;\r\n        }\r\n        const ownerNode = this.m_owner.node;\r\n        const targetNode = this.m_target.node;\r\n        const angle = ownerNode.angle;\r\n        const targetAngle = Math.atan2(targetNode.worldPosition.y - ownerNode.worldPosition.y, targetNode.worldPosition.x - ownerNode.worldPosition.x) * 180 / Math.PI - 90;\r\n        let deltaAngle = targetAngle - angle;\r\n        if (deltaAngle > 180) {\r\n            deltaAngle -= 360;\r\n        } else if (deltaAngle < -180) {\r\n            deltaAngle += 360;\r\n        }\r\n        const maxDelta = this.turnSpeed * dt / 1000;\r\n        if (Math.abs(deltaAngle) <= maxDelta) {\r\n            ownerNode.angle = targetAngle;\r\n        }\r\n        else {\r\n            ownerNode.angle += Math.sign(deltaAngle) * maxDelta;\r\n        }\r\n    }\r\n}"]}