{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/data/task/Task.ts"], "names": ["Task", "MyApp", "csproto", "logError", "ResTaskClass", "DataEvent", "EventMgr", "taskMap", "Map", "init", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_TASK_GET_INFO", "onGetTaskInfoMsg", "CS_CMD_TASK_GET_REWARD", "onGetTaskRewardMsg", "CS_CMD_TASK_GET_LIST", "onTaskListMsg", "CS_CMD_TASK_UPDATE_DATA", "onTaskUpdateDataMsg", "refreshAllTasks", "refreshTaskByClass", "DAILY_TASK", "WEEKLY_TASK", "TASK_ORBIT", "taskClass", "sendMessage", "task_get_list", "task_class", "getTaskListByClass", "get", "getTaskByTaskId", "taskId", "taskList", "find", "t", "task_id", "values", "task", "undefined", "getTaskDescAndProgress", "taskCfg", "taskDesc", "taskGoal", "desc", "progressMax", "params", "length", "replace", "toString", "commitTaskProgress", "goalType", "goldTypeTasks", "for<PERSON>ach", "tasks", "filter", "status", "comm", "TASK_STATUS", "TASK_STATUS_NORMAL", "lubanTables", "TbResTask", "push", "CS_CMD_TASK_GOAL_UPDATE", "task_goal_update", "goal_type", "msg", "body", "task_list", "set", "emit", "TaskRefresh", "taskInfo", "task_update_data", "task_info", "taskIndex", "findIndex", "rewardList", "task_get_reward", "reward_list", "task_get_info", "update"], "mappings": ";;;2FAQaA,I;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AARJC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,O;;AAEEC,MAAAA,Q,iBAAAA,Q;;AACsBC,MAAAA,Y,iBAAAA,Y;;AACtBC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;sBAEIN,I,GAAN,MAAMA,IAAN,CAA4B;AAAA;AAC/B;AAD+B,eAE/BO,OAF+B,GAEwB,IAAIC,GAAJ,EAFxB;AAAA;;AAIxBC,QAAAA,IAAI,GAAS;AAChB;AAAA;AAAA,8BAAMC,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,oBAA/C,EAAqE,KAAKC,gBAA1E,EAA4F,IAA5F;AACA;AAAA;AAAA,8BAAML,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBG,sBAA/C,EAAuE,KAAKC,kBAA5E,EAAgG,IAAhG;AACA;AAAA;AAAA,8BAAMP,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBK,oBAA/C,EAAqE,KAAKC,aAA1E,EAAyF,IAAzF;AACA;AAAA;AAAA,8BAAMT,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBO,uBAA/C,EAAwE,KAAKC,mBAA7E,EAAkG,IAAlG;AACA,eAAKC,eAAL;AACH;;AAEDA,QAAAA,eAAe,GAAG;AACd,eAAKC,kBAAL,CAAwB;AAAA;AAAA,4CAAaC,UAArC;AACA,eAAKD,kBAAL,CAAwB;AAAA;AAAA,4CAAaE,WAArC;AACA,eAAKF,kBAAL,CAAwB;AAAA;AAAA,4CAAaG,UAArC;AACH;;AAEDH,QAAAA,kBAAkB,CAACI,SAAD,EAA0B;AACxC;AAAA;AAAA,8BAAMjB,MAAN,CAAakB,WAAb,CAAyB;AAAA;AAAA,kCAAQhB,EAAR,CAAWC,MAAX,CAAkBK,oBAA3C,EAAiE;AAAEW,YAAAA,aAAa,EAAE;AAAEC,cAAAA,UAAU,EAAEH;AAAd;AAAjB,WAAjE;AACH;;AAEDI,QAAAA,kBAAkB,CAACJ,SAAD,EAAoD;AAClE,iBAAO,KAAKpB,OAAL,CAAayB,GAAb,CAAiBL,SAAjB,KAA+B,EAAtC;AACH;;AACDM,QAAAA,eAAe,CAACC,MAAD,EAAiBP,SAAjB,EAA+E;AAC1F,cAAIA,SAAJ,EAAe;AACX,kBAAMQ,QAAQ,GAAG,KAAK5B,OAAL,CAAayB,GAAb,CAAiBL,SAAjB,KAA+B,EAAhD;AACA,mBAAOQ,QAAQ,CAACC,IAAT,CAAcC,CAAC,IAAIA,CAAC,CAACC,OAAF,KAAcJ,MAAjC,CAAP;AACH;;AACD,eAAK,MAAMC,QAAX,IAAuB,KAAK5B,OAAL,CAAagC,MAAb,EAAvB,EAA8C;AAC1C,kBAAMC,IAAI,GAAGL,QAAQ,CAACC,IAAT,CAAcC,CAAC,IAAIA,CAAC,CAACC,OAAF,KAAcJ,MAAjC,CAAb;;AACA,gBAAIM,IAAJ,EAAU;AACN,qBAAOA,IAAP;AACH;AACJ;;AACD,iBAAOC,SAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,sBAAsB,CAACC,OAAD,EAA0D;AAC5E,cAAIC,QAAgB,GAAGD,OAAO,CAACE,QAAR,CAAiBC,IAAjB,IAAyB,EAAhD;AACA,cAAIC,WAAmB,GAAG,CAA1B;;AACA,cAAIJ,OAAO,CAACE,QAAR,CAAiBG,MAAjB,CAAwBC,MAAxB,IAAkC,CAAtC,EAAyC;AACrCF,YAAAA,WAAW,GAAGJ,OAAO,CAACE,QAAR,CAAiBG,MAAjB,CAAwB,CAAxB,CAAd;AACAJ,YAAAA,QAAQ,GAAGA,QAAQ,CAACM,OAAT,CAAiB,GAAjB,EAAsBP,OAAO,CAACE,QAAR,CAAiBG,MAAjB,CAAwB,CAAxB,EAA2BG,QAA3B,EAAtB,CAAX;AACH,WAHD,MAGO,IAAIR,OAAO,CAACE,QAAR,CAAiBG,MAAjB,CAAwBC,MAAxB,IAAkC,CAAtC,EAAyC;AAC5C;AACAL,YAAAA,QAAQ,GAAGA,QAAQ,CAACM,OAAT,CAAiB,IAAjB,EAAuBP,OAAO,CAACE,QAAR,CAAiBG,MAAjB,CAAwB,CAAxB,EAA2BG,QAA3B,EAAvB,CAAX;AACAP,YAAAA,QAAQ,GAAGA,QAAQ,CAACM,OAAT,CAAiB,GAAjB,EAAsBP,OAAO,CAACE,QAAR,CAAiBG,MAAjB,CAAwB,CAAxB,EAA2BG,QAA3B,EAAtB,CAAX;AACAJ,YAAAA,WAAW,GAAGJ,OAAO,CAACE,QAAR,CAAiBG,MAAjB,CAAwB,CAAxB,CAAd,CAJ4C,CAK5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;;AACD,iBAAO;AAAEF,YAAAA,IAAI,EAAEF,QAAR;AAAkBG,YAAAA;AAAlB,WAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIK,QAAAA,kBAAkB,CAACC,QAAD,EAAwB,GAAGL,MAA3B,EAA6C;AAC3D,gBAAMM,aAAa,GAAG,EAAtB;AACA,eAAK/C,OAAL,CAAagD,OAAb,CAAqB,CAACpB,QAAD,EAAWR,SAAX,KAAyB;AAC1C,kBAAM6B,KAAK,GAAGrB,QAAQ,CAACsB,MAAT,CAAgBpB,CAAC,IAAI;AAC/B,kBAAIA,CAAC,CAACqB,MAAF,KAAa;AAAA;AAAA,sCAAQC,IAAR,CAAaC,WAAb,CAAyBC,kBAA1C,EAA8D,OAAO,KAAP;AAC9D,oBAAMlB,OAAO,GAAG;AAAA;AAAA,kCAAMmB,WAAN,CAAkBC,SAAlB,CAA4B/B,GAA5B,CAAgCK,CAAC,CAACC,OAAlC,CAAhB;AACA,qBAAOK,OAAO,IAAIA,OAAO,CAACE,QAAR,CAAiBQ,QAAjB,KAA8BA,QAAhD;AACH,aAJa,KAIR,EAJN;;AAKA,gBAAIG,KAAK,CAACP,MAAN,GAAe,CAAnB,EAAsB;AAClBK,cAAAA,aAAa,CAACU,IAAd,CAAmB,GAAGR,KAAtB;AACH;AACJ,WATD;AAUA,cAAIF,aAAa,CAACL,MAAd,KAAyB,CAA7B,EAAgC;AAChC;AAAA;AAAA,8BAAMvC,MAAN,CAAakB,WAAb,CAAyB;AAAA;AAAA,kCAAQhB,EAAR,CAAWC,MAAX,CAAkBoD,uBAA3C,EAAoE;AAChEC,YAAAA,gBAAgB,EAAE;AACdC,cAAAA,SAAS,EAAEd,QADG;AAEdd,cAAAA,MAAM,EAAES;AAFM;AAD8C,WAApE;AAMH;;AAEO7B,QAAAA,aAAa,CAACiD,GAAD,EAA0B;AAAA;;AAC3C,gBAAMjC,QAAQ,GAAG,cAAAiC,GAAG,CAACC,IAAJ,oCAAUxC,aAAV,+BAAyByC,SAAzB,KAAsC,EAAvD;AACA,gBAAM3C,SAAS,iBAAGyC,GAAG,CAACC,IAAP,2BAAG,WAAUxC,aAAb,qBAAG,WAAyBC,UAA3C;;AACA,cAAIH,SAAJ,EAAe;AACX,iBAAKpB,OAAL,CAAagE,GAAb,CAAiB5C,SAAjB,EAA4BQ,QAA5B;AACH;;AACD;AAAA;AAAA,oCAASqC,IAAT,CAAc;AAAA;AAAA,sCAAUC,WAAxB,EAAqC9C,SAArC;AACH;;AAEON,QAAAA,mBAAmB,CAAC+C,GAAD,EAA0B;AAAA;;AACjD,gBAAMM,QAAQ,iBAAGN,GAAG,CAACC,IAAP,2BAAG,WAAUM,gBAAb,qBAAG,WAA4BC,SAA7C;AACA,gBAAMjD,SAAS,iBAAGyC,GAAG,CAACC,IAAP,2BAAG,WAAUM,gBAAb,qBAAG,WAA4B7C,UAA9C;;AACA,cAAI,CAAC4C,QAAD,IAAa,CAACA,QAAQ,CAACpC,OAAvB,IAAkC,CAACX,SAAvC,EAAkD;AAC9C;AAAA;AAAA,sCAAS,MAAT,EAAkB,uCAAlB;AACA;AACH;;AACD,gBAAMQ,QAAQ,GAAG,KAAK5B,OAAL,CAAayB,GAAb,CAAiBL,SAAjB,KAA+B,EAAhD;AACA,gBAAMkD,SAAS,GAAG1C,QAAQ,CAAC2C,SAAT,CAAmBzC,CAAC,IAAIA,CAAC,CAACC,OAAF,MAAcoC,QAAd,oBAAcA,QAAQ,CAAEpC,OAAxB,CAAxB,CAAlB;;AACA,cAAIuC,SAAS,KAAK,CAAC,CAAnB,EAAsB;AAClB1C,YAAAA,QAAQ,CAAC0C,SAAD,CAAR,GAAsBH,QAAtB;AACH,WAFD,MAEO;AACHvC,YAAAA,QAAQ,CAAC6B,IAAT,CAAcU,QAAd;AACH;;AACD;AAAA;AAAA,oCAASF,IAAT,CAAc;AAAA;AAAA,sCAAUC,WAAxB,EAAqC9C,SAArC;AACH,SAtH8B,CAwH/B;;;AACQV,QAAAA,kBAAkB,CAACmD,GAAD,EAA0B;AAAA;;AAChD,gBAAMW,UAAU,GAAG,eAAAX,GAAG,CAACC,IAAJ,sCAAUW,eAAV,gCAA2BC,WAA3B,KAA0C,EAA7D,CADgD,CAEhD;AACA;AACA;AACA;AACA;AACA;AACH,SAjI8B,CAmI/B;;;AACQlE,QAAAA,gBAAgB,CAACqD,GAAD,EAA0B;AAAA;;AAC9C,gBAAMjC,QAAQ,GAAG,eAAAiC,GAAG,CAACC,IAAJ,sCAAUa,aAAV,gCAAyBZ,SAAzB,KAAsC,EAAvD;AACAnC,UAAAA,QAAQ,CAACoB,OAAT,CAAiBlB,CAAC,IAAI;AAClB,kBAAMM,OAAO,GAAG;AAAA;AAAA,gCAAMmB,WAAN,CAAkBC,SAAlB,CAA4B/B,GAA5B,CAAgCK,CAAC,CAACC,OAAlC,CAAhB;;AACA,gBAAI,CAACK,OAAL,EAAc;AACV;AAAA;AAAA,wCAAS,MAAT,EAAkB,WAAUN,CAAC,CAACC,OAAQ,YAAtC;AACA;AACH;;AACD,gBAAIH,QAAQ,GAAG,KAAK5B,OAAL,CAAayB,GAAb,CAAiBW,OAAO,CAAChB,SAAzB,KAAuC,EAAtD;AACAQ,YAAAA,QAAQ,CAAC6B,IAAT,CAAc3B,CAAd;AACA,iBAAK9B,OAAL,CAAagE,GAAb,CAAiB5B,OAAO,CAAChB,SAAzB,EAAoCQ,QAApC;AACH,WATD;AAUH;;AAIDgD,QAAAA,MAAM,GAAS,CACd;;AArJ8B,O", "sourcesContent": ["import { MyApp } from \"db://assets/bundles/common/script/app/MyApp\";\nimport csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\nimport { IData } from \"db://assets/bundles/common/script/data/DataManager\";\nimport { logError } from \"db://assets/scripts/utils/Logger\";\nimport { ResGoalType, ResTask, ResTaskClass } from '../../autogen/luban/schema';\nimport { DataEvent } from \"../../event/DataEvent\";\nimport { EventMgr } from \"../../event/EventManager\";\n\nexport class Task implements IData {\n    // 任务合集\n    taskMap: Map<ResTaskClass, csproto.cs.ICSTaskInfo[]> = new Map();\n\n    public init(): void {\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_TASK_GET_INFO, this.onGetTaskInfoMsg, this)\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_TASK_GET_REWARD, this.onGetTaskRewardMsg, this)\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_TASK_GET_LIST, this.onTaskListMsg, this)\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_TASK_UPDATE_DATA, this.onTaskUpdateDataMsg, this)\n        this.refreshAllTasks();\n    }\n\n    refreshAllTasks() {\n        this.refreshTaskByClass(ResTaskClass.DAILY_TASK)\n        this.refreshTaskByClass(ResTaskClass.WEEKLY_TASK)\n        this.refreshTaskByClass(ResTaskClass.TASK_ORBIT)\n    }\n\n    refreshTaskByClass(taskClass: ResTaskClass) {\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_TASK_GET_LIST, { task_get_list: { task_class: taskClass } })\n    }\n\n    getTaskListByClass(taskClass: ResTaskClass): csproto.cs.ICSTaskInfo[] {\n        return this.taskMap.get(taskClass) || [];\n    }\n    getTaskByTaskId(taskId: number, taskClass?: ResTaskClass): csproto.cs.ICSTaskInfo | undefined {\n        if (taskClass) {\n            const taskList = this.taskMap.get(taskClass) || [];\n            return taskList.find(t => t.task_id === taskId);\n        }\n        for (const taskList of this.taskMap.values()) {\n            const task = taskList.find(t => t.task_id === taskId);\n            if (task) {\n                return task;\n            }\n        }\n        return undefined;\n    }\n\n    /**\n     * 获取任务描述和进度最大值\n     * @param taskCfg 任务配置\n     * @returns 任务描述和进度最大值\n     */\n    getTaskDescAndProgress(taskCfg: ResTask): { desc: string, progressMax: number } {\n        let taskDesc: string = taskCfg.taskGoal.desc || \"\";\n        let progressMax: number = 0;\n        if (taskCfg.taskGoal.params.length == 1) {\n            progressMax = taskCfg.taskGoal.params[0];\n            taskDesc = taskDesc.replace(\"N\", taskCfg.taskGoal.params[0].toString());\n        } else if (taskCfg.taskGoal.params.length == 2) {\n            //暂时不读表了，表配置还没出来,先直接替换param[0]\n            taskDesc = taskDesc.replace(\"XX\", taskCfg.taskGoal.params[0].toString());\n            taskDesc = taskDesc.replace(\"N\", taskCfg.taskGoal.params[1].toString());\n            progressMax = taskCfg.taskGoal.params[1];\n            // switch (taskCfg.taskGoal.goalType) {\n            //     case ResGoalType.MODE_PASS_TIMES:\n            //         const gameMode = MyApp.lubanTables.TbResGameMode.get(taskCfg.taskGoal.params[0]);\n            //         replaceName = gameMode?.description || \"\";\n            //         break;\n            //     default:\n            //         break;\n            // }\n        }\n        return { desc: taskDesc, progressMax };\n    }\n\n    /**\n     * 上报任务目标进度\n     * @param goalType 任务目标类型\n     * @param params 任务目标参数 任务目标参数根据goalType不同而不同 比如关卡目标参数为关卡id\n     * @returns \n     */\n    commitTaskProgress(goalType: ResGoalType, ...params: number[]) {\n        const goldTypeTasks = [];\n        this.taskMap.forEach((taskList, taskClass) => {\n            const tasks = taskList.filter(t => {\n                if (t.status !== csproto.comm.TASK_STATUS.TASK_STATUS_NORMAL) return false;\n                const taskCfg = MyApp.lubanTables.TbResTask.get(t.task_id!);\n                return taskCfg && taskCfg.taskGoal.goalType === goalType\n            }) || [];\n            if (tasks.length > 0) {\n                goldTypeTasks.push(...tasks);\n            }\n        })\n        if (goldTypeTasks.length === 0) return;\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_TASK_GOAL_UPDATE, {\n            task_goal_update: {\n                goal_type: goalType,\n                values: params,\n            }\n        })\n    }\n\n    private onTaskListMsg(msg: csproto.cs.IS2CMsg) {\n        const taskList = msg.body?.task_get_list?.task_list || [];\n        const taskClass = msg.body?.task_get_list?.task_class;\n        if (taskClass) {\n            this.taskMap.set(taskClass, taskList);\n        }\n        EventMgr.emit(DataEvent.TaskRefresh, taskClass)\n    }\n\n    private onTaskUpdateDataMsg(msg: csproto.cs.IS2CMsg) {\n        const taskInfo = msg.body?.task_update_data?.task_info;\n        const taskClass = msg.body?.task_update_data?.task_class;\n        if (!taskInfo || !taskInfo.task_id || !taskClass) {\n            logError(\"Task\", `task update data task_id is undefined`)\n            return\n        }\n        const taskList = this.taskMap.get(taskClass) || [];\n        const taskIndex = taskList.findIndex(t => t.task_id === taskInfo?.task_id);\n        if (taskIndex !== -1) {\n            taskList[taskIndex] = taskInfo;\n        } else {\n            taskList.push(taskInfo);\n        }\n        EventMgr.emit(DataEvent.TaskRefresh, taskClass)\n    }\n\n    // 任务奖励\n    private onGetTaskRewardMsg(msg: csproto.cs.IS2CMsg) {\n        const rewardList = msg.body?.task_get_reward?.reward_list || []\n        // if (taskId) {\n        //     const taskCfg = MyApp.lubanTables.TbResTask.get();\n        //     if (taskCfg) {\n        //         this.taskMap.set(taskCfg.taskClass, t);\n        //     }\n        // }\n    }\n\n    // 全任务信息\n    private onGetTaskInfoMsg(msg: csproto.cs.IS2CMsg) {\n        const taskList = msg.body?.task_get_info?.task_list || [];\n        taskList.forEach(t => {\n            const taskCfg = MyApp.lubanTables.TbResTask.get(t.task_id!)\n            if (!taskCfg) {\n                logError(\"Task\", `task id ${t.task_id} not found`)\n                return\n            }\n            let taskList = this.taskMap.get(taskCfg.taskClass) || [];\n            taskList.push(t);\n            this.taskMap.set(taskCfg.taskClass, taskList);\n        })\n    }\n\n\n\n    update(): void {\n    }\n}\n"]}