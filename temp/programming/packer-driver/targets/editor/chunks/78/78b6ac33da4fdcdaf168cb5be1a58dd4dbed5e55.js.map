{"version": 3, "sources": ["file:///Users/<USER>/Documents/Young/trunk/wip/M2Game/Client/trunk/assets/bundles/common/script/game/wave/Wave.ts"], "names": ["_decorator", "CCFloat", "CCInteger", "Component", "WaveData", "eSpawnOrder", "eWaveCompletion", "GameIns", "WaveEventGroup", "WaveEventGroupContext", "ccclass", "property", "executeInEditMode", "menu", "WaveTrack", "WaveTrackGroup", "Wave", "displayName", "editor<PERSON><PERSON><PERSON>", "type", "_isCompleted", "_waveElapsedTime", "_nextSpawnTime", "_totalWeight", "_waveCompleteParam", "_nextSpawnIndex", "_spawnQueue", "_offsetX", "_offsetY", "_eventGroups", "_eventGroupContext", "_createPlaneDelegate", "isCompleted", "onLoad", "waveData", "spawnOrder", "Random", "spawnGroup", "for<PERSON>ach", "group", "weight", "selfWeight", "eventGroupData", "groupData", "push", "reset", "length", "trigger", "x", "y", "waveCompletionParam", "eval", "waveCompletion", "SpawnCount", "i", "randomWeight", "battleManager", "random", "planeID", "wave", "tryStart", "tick", "dtInMiliseconds", "spawnFromQueue", "spawnFromGroup", "spawnSingleFromQueue", "spawnInterval", "index", "spawnPos", "spawnAngle", "createPlane", "planeId", "pos", "angle", "enemy", "enemyManager", "addPlane", "setPos", "initMove", "setCreatePlaneDelegate", "func"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAqBC,MAAAA,S,OAAAA,S;;AACrDC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,e,iBAAAA,e;;AACvBC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,c,iBAAAA,c;AAAgBC,MAAAA,qB,iBAAAA,qB;;;;;;;;;OACnB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA;AAAxC,O,GAAiDb,U;;2BAG1Cc,S,WADZJ,OAAO,CAAC,WAAD,C,UAEHC,QAAQ,CAACT,SAAD,C,UAERS,QAAQ,CAACV,OAAD,C,UAERU,QAAQ,CAACV,OAAD,C,UAERU,QAAQ,CAACV,OAAD,C,2BARb,MACaa,SADb,CACuB;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEP,C;;;;;;;iBAEG,C;;;;;;;iBAEK,C;;;;;;;iBAEF,C;;;;gCAITC,c,YADZL,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAACT,SAAD,C,UAERS,QAAQ,CAACT,SAAD,C,UAERS,QAAQ,CAACT,SAAD,C,WAERS,QAAQ,CAAC,CAACG,SAAD,CAAD,C,6BARb,MACaC,cADb,CAC4B;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEV,C;;;;;;;iBAEG,C;;;;;;;iBAEE,C;;;;;;;iBAEU,E;;;;sBAMpBC,I,aAHZN,OAAO,CAAC,MAAD,C,WACPG,IAAI,CAAC,OAAD,C,WACJD,iBAAiB,E,WAEbD,QAAQ,CAAC;AAACM,QAAAA,WAAW,EAAE,IAAd;AAAoBC,QAAAA,UAAU,EAAE;AAAhC,OAAD,C,WAGRP,QAAQ,CAAC;AAACQ,QAAAA,IAAI;AAAA;AAAA;AAAL,OAAD,C,gEAPb,MAGaH,IAHb,SAG0Bb,SAH1B,CAGoC;AAAA;AAAA;;AAAA;;AAEM;AAFN;;AAOhC;AACJ;AACA;AAToC,eAUxBiB,YAVwB,GAUA,KAVA;AAAA,eAaxBC,gBAbwB,GAaG,CAbH;AAAA,eAcxBC,cAdwB,GAcC,CAdD;AAAA,eAexBC,YAfwB,GAeD,CAfC;AAgBhC;AAhBgC,eAiBxBC,kBAjBwB,GAiBK,CAjBL;AAkBhC;AAlBgC,eAmBxBC,eAnBwB,GAmBE,CAnBF;AAAA,eAoBxBC,WApBwB,GAoBA,EApBA;AAqBhC;AArBgC,eAsBxBC,QAtBwB,GAsBL,CAtBK;AAAA,eAuBxBC,QAvBwB,GAuBL,CAvBK;AAwBhC;AAxBgC,eAyBxBC,YAzBwB,GAyBS,EAzBT;AAAA,eA0BxBC,kBA1BwB,GA0ByB,IA1BzB;AA6LhC;AA7LgC,eA8LxBC,oBA9LwB,GA8L2D,IA9L3D;AAAA;;AAWhC;AACsB,YAAXC,WAAW,GAAG;AAAE,iBAAO,KAAKZ,YAAZ;AAA2B;;AAgBtDa,QAAAA,MAAM,GAAG;AACL,cAAI,KAAKC,QAAT,EAAmB;AACf,gBAAI,KAAKA,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,4CAAYC,MAA7C,EAAqD;AACjD,mBAAKb,YAAL,GAAoB,CAApB,CADiD,CAEjD;;AACA,mBAAKW,QAAL,CAAcG,UAAd,CAAyBC,OAAzB,CAAkCC,KAAD,IAAW;AACxC,qBAAKhB,YAAL,IAAqBgB,KAAK,CAACC,MAA3B;AACAD,gBAAAA,KAAK,CAACE,UAAN,GAAmB,KAAKlB,YAAxB;AACH,eAHD;AAIH;;AAED,gBAAI,KAAKW,QAAL,CAAcQ,cAAlB,EAAkC;AAC9B,kBAAI,CAAC,KAAKZ,kBAAV,EAA8B;AAC1B,qBAAKA,kBAAL,GAA0B;AAAA;AAAA,qEAA1B;AACH;;AACD,mBAAKI,QAAL,CAAcQ,cAAd,CAA6BJ,OAA7B,CAAsCK,SAAD,IAAe;AAChD,sBAAMJ,KAAK,GAAG;AAAA;AAAA,sDAAmB,KAAKT,kBAAxB,EAA6Ca,SAA7C,CAAd;;AACA,qBAAKd,YAAL,CAAkBe,IAAlB,CAAuBL,KAAvB;AACH,eAHD;AAIH;AACJ;AACJ;;AAEOM,QAAAA,KAAK,GAAG;AACZ,eAAKzB,YAAL,GAAoB,KAApB;AACA,eAAKC,gBAAL,GAAwB,CAAxB;AACA,eAAKC,cAAL,GAAsB,CAAtB;AACA,eAAKG,eAAL,GAAuB,CAAvB;AACA,eAAKC,WAAL,CAAiBoB,MAAjB,GAA0B,CAA1B;;AACA,eAAKhB,kBAAL,CAAyBe,KAAzB;;AAEA,eAAKhB,YAAL,CAAkBS,OAAlB,CAA2BC,KAAD,IAAW;AACjCA,YAAAA,KAAK,CAACM,KAAN;AACH,WAFD;AAGH;;AAEDE,QAAAA,OAAO,CAACC,CAAD,EAAYC,CAAZ,EAAuB;AAC1B,eAAKJ,KAAL;AACA,eAAKlB,QAAL,GAAgBqB,CAAhB;AACA,eAAKpB,QAAL,GAAgBqB,CAAhB,CAH0B,CAK1B;;AACA,cAAI,KAAKf,QAAT,EAAmB;AACf,iBAAKV,kBAAL,GAA0B,KAAKU,QAAL,CAAcgB,mBAAd,CAAkCC,IAAlC,EAA1B;;AACA,gBAAI,KAAKjB,QAAL,CAAckB,cAAd,KAAiC;AAAA;AAAA,oDAAgBC,UAArD,EAAiE;AAC7D,kBAAI,KAAKnB,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,8CAAYC,MAA7C,EAAqD;AACjD,qBAAK,IAAIkB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK9B,kBAAzB,EAA6C8B,CAAC,EAA9C,EAAkD;AAC9C,wBAAMC,YAAY,GAAG;AAAA;AAAA,0CAAQC,aAAR,CAAsBC,MAAtB,KAAiC,KAAKlC,YAA3D;;AACA,uBAAK,MAAMgB,KAAX,IAAoB,KAAKL,QAAL,CAAcG,UAAlC,EAA8C;AAC1C,wBAAIkB,YAAY,IAAIhB,KAAK,CAACE,UAA1B,EAAsC;AAClC,2BAAKf,WAAL,CAAiBkB,IAAjB,CAAsBL,KAAK,CAACmB,OAA5B;;AACA;AACH;AACJ;AACJ;AACJ,eAVD,MAUO;AACH,qBAAK,IAAIJ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK9B,kBAAzB,EAA6C8B,CAAC,EAA9C,EAAkD;AAC9C;AACA,uBAAK5B,WAAL,CAAiBkB,IAAjB,CAAsB,KAAKV,QAAL,CAAcG,UAAd,CAAyBiB,CAAC,GAAG,KAAKpB,QAAL,CAAcG,UAAd,CAAyBS,MAAtD,EAA8DY,OAApF;AACH;AACJ;AACJ;AACJ;;AAED,eAAK5B,kBAAL,CAAyB6B,IAAzB,GAAgC,IAAhC;;AACA,eAAK9B,YAAL,CAAkBS,OAAlB,CAA2BC,KAAD,IAAW;AACjCA,YAAAA,KAAK,CAACqB,QAAN;AACH,WAFD;AAGH,SAhG+B,CAkGhC;;;AACAC,QAAAA,IAAI,CAACC,eAAD,EAA0B;AAC1B,cAAI,KAAK1C,YAAT,EAAuB;AAEvB,eAAKC,gBAAL,IAAyByC,eAAzB;;AACA,cAAI,KAAK5B,QAAL,CAAckB,cAAd,KAAiC;AAAA;AAAA,kDAAgBC,UAArD,EAAiE;AAC7D;AACA,gBAAI,KAAKhC,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,kBAAI,CAAC,KAAKyC,cAAL,EAAL,EAA4B;AACxB,qBAAK3C,YAAL,GAAoB,IAApB;AACH;AACJ;AACJ,WAPD,MAQK;AACD;AACA,gBAAI,KAAKC,gBAAL,IAAyB,KAAKG,kBAAlC,EAAsD;AAClD,mBAAKJ,YAAL,GAAoB,IAApB;AACH,aAFD,MAEO;AACH,kBAAI,KAAKC,gBAAL,IAAyB,KAAKC,cAAlC,EAAkD;AAC9C,qBAAK0C,cAAL;AACH;AACJ;AACJ;;AAED,cAAI,KAAKnC,YAAL,CAAkBiB,MAAlB,GAA2B,CAA/B,EAAkC;AAC9B,iBAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKzB,YAAL,CAAkBiB,MAAtC,EAA8CQ,CAAC,EAA/C,EAAmD;AAC/C,mBAAKzB,YAAL,CAAkByB,CAAlB,EAAqBO,IAArB,CAA0BC,eAA1B;AACH;AACJ;AACJ;;AAEOC,QAAAA,cAAc,GAAY;AAC9B,cAAI,KAAKtC,eAAL,IAAwB,KAAKC,WAAL,CAAiBoB,MAA7C,EAAqD;AACjD,mBAAO,KAAP;AACH;;AAED,eAAKmB,oBAAL,CAA0B,KAAKxC,eAAL,EAA1B;AACA,eAAKH,cAAL,GAAsB,KAAKD,gBAAL,GAAwB,KAAKa,QAAL,CAAcgC,aAAd,CAA4Bf,IAA5B,EAA9C;AACA,iBAAO,IAAP;AACH;;AAEOc,QAAAA,oBAAoB,CAACE,KAAD,EAAsB;AAC9C,cAAIA,KAAK,IAAI,KAAKzC,WAAL,CAAiBoB,MAA9B,EAAsC;AAClC;AACH;;AAED,cAAIsB,QAAQ,GAAG,KAAKlC,QAAL,CAAckC,QAA7B;AACA,cAAIC,UAAU,GAAG,KAAKnC,QAAL,CAAcmC,UAAd,CAAyBlB,IAAzB,EAAjB,CAN8C,CAO9C;;AAEA,eAAKmB,WAAL,CAAiB,KAAK5C,WAAL,CAAiByC,KAAjB,CAAjB,EAA0CC,QAA1C,EAAoDC,UAApD;AACH;;AAEOL,QAAAA,cAAc,GAAS;AAC3B,eAAK1C,cAAL,GAAsB,KAAKD,gBAAL,GAAwB,KAAKa,QAAL,CAAcgC,aAAd,CAA4Bf,IAA5B,EAA9C;AAEA,cAAIiB,QAAQ,GAAG,KAAKlC,QAAL,CAAckC,QAA7B;AACA,cAAIC,UAAU,GAAG,KAAKnC,QAAL,CAAcmC,UAAd,CAAyBlB,IAAzB,EAAjB;;AAEA,cAAI,KAAKjB,QAAL,CAAcC,UAAd,KAA6B;AAAA;AAAA,0CAAYC,MAA7C,EAAqD;AACjD,kBAAMmB,YAAY,GAAG;AAAA;AAAA,oCAAQC,aAAR,CAAsBC,MAAtB,KAAiC,KAAKlC,YAA3D;;AACA,iBAAK,MAAMgB,KAAX,IAAoB,KAAKL,QAAL,CAAcG,UAAlC,EAA8C;AAC1C,kBAAIkB,YAAY,IAAIhB,KAAK,CAACE,UAA1B,EAAsC;AAClC,qBAAK6B,WAAL,CAAiB/B,KAAK,CAACmB,OAAvB,EAAgCU,QAAhC,EAA0CC,UAA1C;AACA;AACH;AACJ;AACJ,WARD,MAQO;AACH,iBAAKC,WAAL,CAAiB,KAAKpC,QAAL,CAAcG,UAAd,CAAyB,KAAKZ,eAAL,KAAyB,KAAKS,QAAL,CAAcG,UAAd,CAAyBS,MAA3E,EAAmFY,OAApG,EAA6GU,QAA7G,EAAuHC,UAAvH;AACH;AACJ;;AAEOC,QAAAA,WAAW,CAACC,OAAD,EAAkBC,GAAlB,EAA6BC,KAA7B,EAA4C;AAC3DD,UAAAA,GAAG,CAACxB,CAAJ,IAAS,KAAKrB,QAAd;AACA6C,UAAAA,GAAG,CAACvB,CAAJ,IAAS,KAAKrB,QAAd;;AAEA,cAAI,KAAKG,oBAAT,EAA+B;AAC3B,iBAAKA,oBAAL,CAA0BwC,OAA1B,EAAmCC,GAAnC,EAAwCC,KAAxC;;AACA;AACH;;AAED,cAAIC,KAAK,GAAG;AAAA;AAAA,kCAAQC,YAAR,CAAqBC,QAArB,CAA8BL,OAA9B,CAAZ;;AACA,cAAIG,KAAJ,EAAW;AACP;AACA;AACA;AACAA,YAAAA,KAAK,CAACG,MAAN,CAAaL,GAAG,CAACxB,CAAjB,EAAoBwB,GAAG,CAACvB,CAAxB;AACAyB,YAAAA,KAAK,CAACI,QAAN,CAAeL,KAAf;AACH;AACJ;;AAIMM,QAAAA,sBAAsB,CAACC,IAAD,EAA4D;AACrF,eAAKjD,oBAAL,GAA4BiD,IAA5B;AACH;;AAjM+B,O;;;;;iBAEb,E;;;;;;;iBAGW;AAAA;AAAA,qC", "sourcesContent": ["import { _decorator, CCBoolean, CCFloat, CCInteger, CCString, Component, Vec2 } from 'cc';\r\nimport { WaveData, eSpawnOrder, eWaveCompletion } from '../data/WaveData';\r\nimport { GameIns } from 'db://assets/bundles/common/script/game/GameIns';\r\nimport { WaveEventGroup, WaveEventGroupContext } from './WaveEventGroup';\r\nconst { ccclass, property, executeInEditMode, menu } = _decorator;\r\n\r\n@ccclass('WaveTrack')\r\nexport class WaveTrack {\r\n    @property(CCInteger)\r\n    public id = 0;\r\n    @property(CCFloat)\r\n    public speed = 0;\r\n    @property(CCFloat)\r\n    public accelerate = 0;\r\n    @property(CCFloat)\r\n    public Interval = 0;\r\n}\r\n\r\n@ccclass('WaveTrackGroup')\r\nexport class WaveTrackGroup {\r\n    @property(CCInteger)\r\n    public type = 0;\r\n    @property(CCInteger)\r\n    public loopNum = 0;\r\n    @property(CCInteger)\r\n    public formIndex = 0;\r\n    @property([WaveTrack])\r\n    public tracks: WaveTrack[] = [];\r\n}\r\n\r\n@ccclass('Wave')\r\n@menu(\"怪物/波次\")\r\n@executeInEditMode()\r\nexport class Wave extends Component {\r\n    @property({displayName: '名称', editorOnly: true})\r\n    waveName: string = '';                // 备注(策划用)\r\n\r\n    @property({type:WaveData})\r\n    readonly waveData: WaveData = new WaveData();\r\n\r\n    /*\r\n     * 以下是新的wave逻辑, 旧的逻辑主要在WaveManager与EnemyWave\r\n     */\r\n    private _isCompleted: boolean = false;\r\n    // 当前波次是否已完成\r\n    public get isCompleted() { return this._isCompleted; }\r\n    private _waveElapsedTime: number = 0;\r\n    private _nextSpawnTime: number = 0;\r\n    private _totalWeight: number = 0;\r\n    // 这个参数可能是时间，也可能是数量，取决于waveData.waveCompletion\r\n    private _waveCompleteParam: number = 0;\r\n    // 以下两个是用在waveCompletion == SpawnCount时的队列\r\n    private _nextSpawnIndex: number = 0;\r\n    private _spawnQueue: number[] = [];\r\n    // 当前wave的偏移位置\r\n    private _offsetX: number = 0;\r\n    private _offsetY: number = 0;\r\n    // 事件组\r\n    private _eventGroups: WaveEventGroup[] = [];\r\n    private _eventGroupContext: WaveEventGroupContext|null = null;\r\n\r\n    onLoad() {\r\n        if (this.waveData) {\r\n            if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n                this._totalWeight = 0;\r\n                // add up _totalWeight if is random\r\n                this.waveData.spawnGroup.forEach((group) => {\r\n                    this._totalWeight += group.weight;\r\n                    group.selfWeight = this._totalWeight;\r\n                });\r\n            }\r\n            \r\n            if (this.waveData.eventGroupData) {\r\n                if (!this._eventGroupContext) {\r\n                    this._eventGroupContext = new WaveEventGroupContext();\r\n                }\r\n                this.waveData.eventGroupData.forEach((groupData) => {\r\n                    const group = new WaveEventGroup(this._eventGroupContext!, groupData);\r\n                    this._eventGroups.push(group);\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    private reset() {\r\n        this._isCompleted = false;\r\n        this._waveElapsedTime = 0;\r\n        this._nextSpawnTime = 0;\r\n        this._nextSpawnIndex = 0;\r\n        this._spawnQueue.length = 0;\r\n        this._eventGroupContext!.reset();\r\n        \r\n        this._eventGroups.forEach((group) => {\r\n            group.reset();\r\n        });\r\n    }\r\n\r\n    trigger(x: number, y: number) {\r\n        this.reset();\r\n        this._offsetX = x;\r\n        this._offsetY = y;\r\n\r\n        // 对于固定数量的波次，可以预先生成队列，每次从里面取即可\r\n        if (this.waveData) {\r\n            this._waveCompleteParam = this.waveData.waveCompletionParam.eval();\r\n            if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {\r\n                if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n                    for (let i = 0; i < this._waveCompleteParam; i++) {\r\n                        const randomWeight = GameIns.battleManager.random() * this._totalWeight;\r\n                        for (const group of this.waveData.spawnGroup) {\r\n                            if (randomWeight <= group.selfWeight) {\r\n                                this._spawnQueue.push(group.planeID);\r\n                                break;\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    for (let i = 0; i < this._waveCompleteParam; i++) {\r\n                        // 通过取余实现循环\r\n                        this._spawnQueue.push(this.waveData.spawnGroup[i % this.waveData.spawnGroup.length].planeID);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        this._eventGroupContext!.wave = this;\r\n        this._eventGroups.forEach((group) => {\r\n            group.tryStart();\r\n        });\r\n    }\r\n\r\n    // tick wave\r\n    tick(dtInMiliseconds: number) {\r\n        if (this._isCompleted) return;\r\n\r\n        this._waveElapsedTime += dtInMiliseconds;\r\n        if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {\r\n            // 产出固定数量的波次\r\n            if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n                if (!this.spawnFromQueue()) {\r\n                    this._isCompleted = true;\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            // 完全根据时间的波次\r\n            if (this._waveElapsedTime >= this._waveCompleteParam) {\r\n                this._isCompleted = true;\r\n            } else {\r\n                if (this._waveElapsedTime >= this._nextSpawnTime) {\r\n                    this.spawnFromGroup();\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this._eventGroups.length > 0) {\r\n            for (let i = 0; i < this._eventGroups.length; i++) {\r\n                this._eventGroups[i].tick(dtInMiliseconds);\r\n            }\r\n        }\r\n    }\r\n\r\n    private spawnFromQueue(): boolean {        \r\n        if (this._nextSpawnIndex >= this._spawnQueue.length) {\r\n            return false;\r\n        }\r\n\r\n        this.spawnSingleFromQueue(this._nextSpawnIndex++);\r\n        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();\r\n        return true;\r\n    }\r\n\r\n    private spawnSingleFromQueue(index: number): void {\r\n        if (index >= this._spawnQueue.length) {\r\n            return;\r\n        }\r\n\r\n        let spawnPos = this.waveData.spawnPos;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n        // let spawnSpeed = this.waveData.spawnSpeed.eval();\r\n\r\n        this.createPlane(this._spawnQueue[index], spawnPos, spawnAngle);\r\n    }\r\n\r\n    private spawnFromGroup(): void {\r\n        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();\r\n\r\n        let spawnPos = this.waveData.spawnPos;\r\n        let spawnAngle = this.waveData.spawnAngle.eval();\r\n\r\n        if (this.waveData.spawnOrder === eSpawnOrder.Random) {\r\n            const randomWeight = GameIns.battleManager.random() * this._totalWeight;\r\n            for (const group of this.waveData.spawnGroup) {\r\n                if (randomWeight <= group.selfWeight) {\r\n                    this.createPlane(group.planeID, spawnPos, spawnAngle);\r\n                    break;\r\n                }\r\n            }\r\n        } else {\r\n            this.createPlane(this.waveData.spawnGroup[this._nextSpawnIndex++ % this.waveData.spawnGroup.length].planeID, spawnPos, spawnAngle);\r\n        }\r\n    }\r\n\r\n    private createPlane(planeId: number, pos: Vec2, angle: number) {\r\n        pos.x += this._offsetX;\r\n        pos.y += this._offsetY;\r\n        \r\n        if (this._createPlaneDelegate) {\r\n            this._createPlaneDelegate(planeId, pos, angle);\r\n            return;\r\n        }\r\n\r\n        let enemy = GameIns.enemyManager.addPlane(planeId);\r\n        if (enemy) {\r\n            // enemy.initTrack(this.waveData.trackGroups, this.waveData.liveParam, spawnPos.x, spawnPos.y);\r\n            // enemy.setStandByTime(0);\r\n            // console.log(\"createPlane\", planeId, pos, angle, speed);\r\n            enemy.setPos(pos.x, pos.y);\r\n            enemy.initMove(angle);\r\n        }\r\n    }\r\n\r\n    // 以下几个函数是为了给编辑器预览用\r\n    private _createPlaneDelegate: ((planeId: number, pos: Vec2, angle: number) => void)|null = null;\r\n    public setCreatePlaneDelegate(func: (planeId: number, pos: Vec2, angle: number) => void) {\r\n        this._createPlaneDelegate = func;\r\n    }\r\n}"]}